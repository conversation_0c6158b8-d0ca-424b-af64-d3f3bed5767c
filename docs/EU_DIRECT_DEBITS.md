# EU Direct Debit Payment Lifecycle

This document outlines the lifecycle of a direct debit payment for a user under the EU entity (`WEALTHYHOOD_EUROPE`). These payments use the `DIRECT_DEBIT_AND_BANK_TRANSFER` method, which is a multi-stage process involving several providers.

## Stage 1: Automated Deposit Creation

*   **What happens:** When a recurring top-up is scheduled, a `DepositCashTransactionDocument` is created in the database. For EU users, the `depositMethod` is set to `DIRECT_DEBIT_AND_BANK_TRANSFER`.
*   **Where it happens:** This process is initiated in [`src/services/transactionService.ts`](src/services/transactionService.ts:1906) within the `createAssetTransactionPendingDepositForRecurringTopUp` or `createSavingsTransactionPendingDepositForRecurringTopUp` functions. These call the internal [`_createAutomatedDepositTransaction`](src/services/transactionService.ts:9764) function, which sets the correct `depositMethod` based on the user's entity.

## Stage 2: GoCardless Payment Collection

*   **What happens:** A direct debit payment is created with GoCardless to collect funds from the user's bank account.
*   **Where it happens:**
    *   The [`createGoCardlessDirectDebitPayment`](src/services/transactionService.ts:5486) function in [`src/services/transactionService.ts`](src/services/transactionService.ts:1) initiates the payment with GoCardless.
    *   GoCardless sends webhooks to notify our system of the payment status. These are handled by [`src/controllers/goCardlessPaymentsWebhookController.ts`](src/controllers/goCardlessPaymentsWebhookController.ts:1).
    *   The [`processGoCardlessPaymentEvent`](src/services/transactionService.ts:5416) function in [`src/services/transactionService.ts`](src/services/transactionService.ts:1) updates the `directDebit.providers.gocardless.status` field in the deposit document. When the status is `confirmed` or `paid_out`, the [`isDirectDebitPaymentCollected`](src/models/Transaction.ts:1642) virtual field on the transaction becomes `true`.

## Stage 3: Devengo Bank Transfer (Acquisition & Collection)

*   **What happens:** Once GoCardless collects the funds, they are transferred via **Devengo** in a two-step process to our brokerage partner, Wealthkernel.
    1.  **Acquisition**: Funds are moved from the GoCardless payout account to our internal Devengo collection account.
    2.  **Collection**: Funds are then transferred from our Devengo collection account to Wealthkernel.
*   **Where it happens:**
    *   The [`devengoWebhookController.ts`](src/controllers/devengoWebhookController.ts:1) listens for Devengo webhooks.
    *   The `_handleIncomingPaymentConfirmed` function is triggered when funds arrive in a Devengo account.
    *   The [`createBankTransferPaymentAndUpdateDeposit`](src/services/transactionService.ts:5522) function in [`src/services/transactionService.ts`](src/services/transactionService.ts:1) creates the outgoing payment to the next stage.
    *   The [`transferWithIntermediary`](src/models/Transaction.ts:959) field in the transaction document tracks the status of these transfers.

## Stage 4: Wealthkernel Settlement

*   **What happens:** Wealthkernel receives the funds from Devengo and settles the deposit.
*   **Where it happens:**
    *   The [`createEligibleWealthkernelDeposits`](src/services/transactionService.ts:2553) function in [`src/services/transactionService.ts`](src/services/transactionService.ts:1) creates a deposit expectation at Wealthkernel.
    *   When Wealthkernel settles the deposit, the [`syncDepositStatusByWkDepositId`](src/services/transactionService.ts:3415) function is called via a webhook, updating the transaction's status to `Settled`.