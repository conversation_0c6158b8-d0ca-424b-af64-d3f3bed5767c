import { Types } from "mongoose";
import { ParticipantRoleType } from "../models/Participant";
import {
  ChargeMethodType,
  ChargeTypeType,
  PortfolioTransactionCategoryType,
  TransactionCategoryType
} from "../models/Transaction";
import { KycStatusType, PortfolioConversionStatusType, UserTypeEnum } from "../models/User";
import { RewardStatusType } from "../models/Reward";
import { WealthkernelOrderStatusType } from "../external-services/wealthkernelService";
import { GiftStatusType } from "../models/Gift";
import { MandateCategoryType } from "../models/Mandate";
import { AutomationCategoryType } from "../models/Automation";
import { plansConfig } from "@wealthyhood/shared-configs";
import { UserDataRequestStatusType } from "../models/UserDataRequest";
import { AppNotificationSettingEnum } from "../models/NotificationSettings";

export type TransactionsFilter = {
  categories?: TransactionCategoryType[];
  portfolio?: Types.ObjectId;
  owner?: string;
  truelayerStatuses?: string[];
  statuses?: string[];
  hasWealthkernelId?: boolean;
  hasDirectDebitWealthkernelId?: boolean;
  hasDirectDebitGoCardlessId?: boolean;
  settlementDate?: { startDate: Date; endDate: Date };
  creationDate?: { startDate?: Date; endDate?: Date };
  wealthkernelSubmissionDate?: { startDate?: Date; endDate?: Date };
  linkedUserDataRequest?: string;
  linkedAutomation?: string;
  hasLinkedAutomation?: boolean;
  hasCreditTicket?: boolean;
  pendingDeposit?: string;
  hasViewedAppModal?: boolean;
  filterOutPendingDepositsLinkedToValidAssetTransaction?: boolean;
  filterOutIncompleteDeposits?: boolean;
  includeTransactionsLinkedToIncompleteDeposits?: boolean;
  depositMethod?: DepositMethodEnum;
};

export type ChargeTransactionsFilter = TransactionsFilter & {
  subscription?: Types.ObjectId;
  price?: plansConfig.PriceType;
  chargeMonth?: string;
  chargeMethods?: ChargeMethodType[];
  chargeTypes?: ChargeTypeType[];
};

export type AssetTransactionsFilter = TransactionsFilter & {
  portfolioTransactionCategories?: PortfolioTransactionCategoryType[];
};

export type DepositTransactionsFilter = TransactionsFilter & {
  truelayerPaymentId?: string;
  saltedgeCustomPaymentId?: string;
};

export type UsersFilter = {
  roles?: UserTypeEnum[];
  email?: string;
  portfolioConversionStatus?: PortfolioConversionStatusType;
  converted?: boolean;
  createdAfter?: Date;
  kycStatus?: KycStatusType;
  referredOnly?: boolean;
  potentiallyDuplicateOnly?: boolean;
  kycFailedAfter?: Date;
  hasRequestedDeletion?: boolean;
  hasAcceptedTerms?: boolean;
  hasSubmittedRequiredInfo?: boolean;
};

export type SubscriptionsFilter = {
  category?: plansConfig.SubscriptionCategoryType;
  active?: boolean;
  prices?: plansConfig.PriceType[];
  nextChargeAt?: { startDate: Date; endDate?: Date };
  notExpiringOnly?: boolean;
  expiredOnly?: boolean;
};

export type BankAccountsFilter = {
  owner?: string;
};

export type ParticipantsFilter = {
  email?: string;
  grsfId?: string;
  participantRole?: ParticipantRoleType;
};

export type DailyTickerFilter = {
  portfolio?: string;
  date?: { startDate: Date; endDate?: Date };
};

export type SundownDigestFilter = {
  date?: { startDate: Date; endDate?: Date };
};

export type IndexPriceFilter = {
  date?: { startDate: Date; endDate?: Date };
};

export type DailySummarySnapshotFilter = {
  owner?: Types.ObjectId;
  date?: { startDate: Date; endDate?: Date };
};

export type RewardsFilter = {
  assetId?: string;
  orderSubmissionDay?: Date;
  targetUser?: string;
  referral?: string;
  hasViewedAppModal?: boolean;
  status?: RewardStatusType;
  orderStatus?: WealthkernelOrderStatusType;
  restrictedOnly?: boolean;
  updatedDate?: { startDate: Date; endDate: Date };
  accepted?: boolean;
  creationDate?: { startDate?: Date; endDate?: Date };
};

export type RewardInvitationsFilter = {
  targetUserEmail?: string;
};

export type MandatesFilter = {
  owner?: string;
  category?: MandateCategoryType;
  includeInactive?: boolean;
};

export type NotificationSettingsFilter = {
  hasEnabledDeviceNotifications?: boolean;
  hasEnabledAppNotificationSetting?: AppNotificationSettingEnum;
};

export type UserDataRequestsFilter = {
  owner?: string;
  creationDate?: { startDate?: Date; endDate?: Date };
  statuses?: UserDataRequestStatusType[];
};

export type PaymentMethodsFilter = {
  owner?: string;
};

export type AutomationsFilter = {
  owner?: string;
  categories?: AutomationCategoryType[];
  mandate?: string;
  activeOnly?: boolean;
  isInitialised?: boolean;
  daysOfMonth?: number[];
};

export type GiftsFilter = {
  targetUserEmail?: string;
  gifter?: string;
  hasViewedAppModal?: boolean;
  status?: GiftStatusType;
  used?: boolean;
  updatedDate?: { startDate: Date; endDate: Date };
};

export type OrdersFilter = {
  isin?: string;
  submissionDay?: Date;
};
