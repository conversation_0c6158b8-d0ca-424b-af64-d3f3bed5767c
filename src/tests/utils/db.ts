import mongoose from "mongoose";
import { createClient } from "@libsql/client";

async function createSqliteDb(): Promise<void> {
  const libsqlClient = createClient({
    url: "file::memory:?cache=shared", // fully in-memory SQLite
    authToken: undefined // not needed for file:// URLs
  });

  // Create accounting ledger table
  await libsqlClient.execute(`
    CREATE TABLE IF NOT EXISTS accountingledger (
      id               INTEGER PRIMARY KEY AUTOINCREMENT,
      aa               INTEGER      NOT NULL,
      account_code     TEXT         NOT NULL,
      side             TEXT CHECK (side IN ('debit','credit')) NOT NULL,
      amount           REAL         NOT NULL,
      reference_number TEXT,
      article_date     TEXT         NOT NULL,
      description      TEXT         NOT NULL,
      document_id      TEXT,
      owner_id         TEXT,
      created_at       TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Create cash balances table for snapshots
  await libsqlClient.execute(`
    CREATE TABLE IF NOT EXISTS cashbalances (
      id               INTEGER PRIMARY KEY AUTOINCREMENT,
      account_code     TEXT         NOT NULL,
      balance          REAL         NOT NULL,
      as_of_date       TEXT         NOT NULL,
      created_at       TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Create reporting checkpoints table for incremental processing
  await libsqlClient.execute(`
    CREATE TABLE IF NOT EXISTS reportingcheckpoints (
      id               INTEGER PRIMARY KEY AUTOINCREMENT,
      report_type      TEXT         NOT NULL,
      last_processed_id INTEGER    NOT NULL,
      processed_at     TEXT         NOT NULL,
      created_at       TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);
}

async function connectDb(moduleName: string): Promise<void> {
  let url: string;
  if (process.env.DATABASE_IN_MEMORY_ENABLED === "true") {
    url = `${process.env.IN_MEMORY_DATABASE_URL}/${moduleName}`;
  } else {
    url = `${process.env.DATABASE_URL}-${moduleName}`;
  }

  _initListeners();

  await mongoose
    .set("strictQuery", false)
    .connect(url)
    .catch((err) => {
      console.error(
        `🙅 🚫 🙅 🚫 🙅 🚫 🙅 🚫 mongoose exception handling on in-memory database connect → ${err.message}`
      );
    });
}

async function clearDb(): Promise<void> {
  await Promise.all(Object.values(mongoose.connection.collections).map((collection) => collection.deleteMany({})));
}

async function closeDb(): Promise<void> {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
}

function _initListeners(): void {
  mongoose.connection.on("error", (err) => {
    console.error(`🙅 🚫 🙅 🚫 🙅 🚫 🙅 🚫 mongodb connection error → ${err.message}`);
  });

  mongoose.connection.on("connected", function () {
    console.log(`✅ ✅ ✅ → mongodb is connected for ${mongoose.connection.name}`);
  });

  mongoose.connection.on("disconnected", function () {
    console.warn("❌ ❌ ❌ → mongodb disconnected");
  });
}

async function clearSqliteDb(): Promise<void> {
  const libsqlClient = createClient({
    url: "file::memory:?cache=shared",
    authToken: undefined
  });

  // Clear all tables
  await libsqlClient.execute("DELETE FROM accountingledger");
  await libsqlClient.execute("DELETE FROM cashbalances");
  await libsqlClient.execute("DELETE FROM reportingcheckpoints");

  // Reset the AUTOINCREMENT counter so that primary keys start from 1 again
  // This keeps deterministic IDs across tests (they are asserted against).
  try {
    await libsqlClient.execute("DELETE FROM sqlite_sequence WHERE name = 'accountingledger'");
    await libsqlClient.execute("DELETE FROM sqlite_sequence WHERE name = 'cashbalances'");
    await libsqlClient.execute("DELETE FROM sqlite_sequence WHERE name = 'reportingcheckpoints'");
  } catch (err) {
    // The sqlite_sequence table is only present after at least one AUTOINCREMENT
    // insert has been made. Ignore the error if the table doesn't exist yet.
    if (!(err instanceof Error) || !/no such table/i.test(err.message)) {
      throw err;
    }
  }
}

export { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb };
