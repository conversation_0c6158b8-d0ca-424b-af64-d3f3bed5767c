import { DateTime } from "luxon";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildBankAccount,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildHoldingDTO,
  buildIntraDayAssetTicker,
  buildInvestmentProduct,
  buildMandate,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildStockSplitCorporateEvent,
  buildSubscription,
  buildTopUpAutomation,
  buildUser
} from "../../tests/utils/generateModels";
import InvestmentProductService, {
  AssetDataResponseType,
  AssetRecentActivityItemType,
  ETFAssetFundamentalsType,
  NonBondIndexStatsType,
  StockAssetFundamentalsType
} from "../investmentProductService";
import logger from "../../external-services/loggerService";
import eodService, { EodStockFundamentalsResponseType } from "../../external-services/eodService";
import { buildStockFundamentalsResponse } from "../../tests/utils/generateEod";
import { faker } from "@faker-js/faker";
import StatisticsConfig from "../../configs/statisticsConfig";
import axios from "axios";
import { RedisClientService } from "../../loaders/redis";
import { UserDocument } from "../../models/User";
import { PortfolioDocument } from "../../models/Portfolio";
import { MandateDocument } from "../../models/Mandate";
import {
  investmentUniverseConfig,
  marketHoursConfig,
  publicInvestmentUniverseConfig,
  entitiesConfig
} from "@wealthyhood/shared-configs";
import { AssetTransactionDocument, DepositCashTransactionDocument } from "../../models/Transaction";
import DateUtil from "../../utils/dateUtil";
import { OrderDocument, OrderSubmissionIntentEnum } from "../../models/Order";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import Decimal from "decimal.js";
import { ProviderEnum } from "../../configs/providersConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MARKET_TRADING_HOURS } = marketHoursConfig;

describe("InvestmentProductService", () => {
  beforeAll(async () => await connectDb("InvestmentProductService"));
  afterAll(async () => await closeDb());

  describe("cacheAllAssetsWeeklyReturns", () => {
    const TODAY = new Date("2024-01-01");

    describe("when all asset data is fetched successfully", () => {
      const ASSET_ID_1: investmentUniverseConfig.AssetType = "equities_apple";
      const ASSET_ID_2: investmentUniverseConfig.AssetType = "equities_microsoft";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_nikola";

      const WEEKLY_INTRADAY_DATA_1 = [
        { timestamp: DateUtil.getDateOfDaysAgo(TODAY, 7).getTime(), close: 100 },
        { timestamp: TODAY.getTime(), close: 110 }
      ];
      const WEEKLY_INTRADAY_DATA_2 = [
        { timestamp: DateUtil.getDateOfDaysAgo(TODAY, 7).getTime(), close: 200 },
        { timestamp: TODAY.getTime(), close: 180 }
      ];

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());

        await RedisClientService.Instance.del(`asset:weeklyReturn:${ASSET_ID_1}`);
        await RedisClientService.Instance.del(`asset:weeklyReturn:${ASSET_ID_2}`);
        await RedisClientService.Instance.del(`asset:weeklyReturn:${DEPRECATED_ASSET_ID}`);

        jest
          .spyOn(eodService, "getIntradayPrices")
          .mockImplementation(
            async (
              assetId: publicInvestmentUniverseConfig.PublicAssetType
            ): Promise<{ timestamp: number; close: number }[]> => {
              if (assetId === ASSET_ID_1) {
                return WEEKLY_INTRADAY_DATA_1;
              } else if (assetId === ASSET_ID_2) {
                return WEEKLY_INTRADAY_DATA_2;
              }
              return [];
            }
          );

        await Promise.all([
          buildInvestmentProduct(false, { assetId: ASSET_ID_1 }),
          buildInvestmentProduct(false, { assetId: ASSET_ID_2 }),
          buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID })
        ]);

        // Run method
        await InvestmentProductService.cacheAllAssetsWeeklyReturns();
      });

      afterAll(async () => await clearDb());

      it("should cache weekly returns for all assets in Redis", async () => {
        const appleReturn = await RedisClientService.Instance.get(`asset:weeklyReturn:${ASSET_ID_1}`);
        const microsoftReturn = await RedisClientService.Instance.get(`asset:weeklyReturn:${ASSET_ID_2}`);

        // Apple: (110 - 100) / 100 = 0.1 = 10%
        expect(appleReturn).toBeCloseTo(0.1);

        // Microsoft: (180 - 200) / 200 = -0.1 = -10%
        expect(microsoftReturn).toBeCloseTo(-0.1);

        // Should not call eodService for deprecated asset
        expect(eodService.getIntradayPrices).not.toHaveBeenCalledWith(DEPRECATED_ASSET_ID);
      });
    });

    describe("when there is an error fetching data for some assets", () => {
      const ASSET_ID_1: investmentUniverseConfig.AssetType = "equities_apple";
      const ASSET_ID_2: investmentUniverseConfig.AssetType = "equities_microsoft";
      const WEEKLY_INTRADAY_DATA = [
        { timestamp: DateUtil.getDateOfDaysAgo(TODAY, 7).getTime(), close: 100 },
        { timestamp: TODAY.getTime(), close: 110 }
      ];

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());
        await RedisClientService.Instance.del(`asset:weeklyReturn:${ASSET_ID_1}`);
        await RedisClientService.Instance.del(`asset:weeklyReturn:${ASSET_ID_2}`);

        jest
          .spyOn(eodService, "getIntradayPrices")
          .mockImplementation(
            async (
              assetId: publicInvestmentUniverseConfig.PublicAssetType
            ): Promise<{ timestamp: number; close: number }[]> => {
              if (assetId === ASSET_ID_1) {
                return WEEKLY_INTRADAY_DATA;
              } else if (assetId === ASSET_ID_2) {
                throw new Error("API error");
              }
              return [];
            }
          );

        await Promise.all([
          buildInvestmentProduct(false, { assetId: ASSET_ID_1 }),
          buildInvestmentProduct(false, { assetId: ASSET_ID_2 })
        ]);

        // Run method
        await InvestmentProductService.cacheAllAssetsWeeklyReturns();
      });

      afterAll(async () => await clearDb());

      it("should cache weekly returns for successful assets in Redis", async () => {
        const appleReturn = await RedisClientService.Instance.get(`asset:weeklyReturn:${ASSET_ID_1}`);

        // Apple: (110 - 100) / 100 = 0.1 = 10%
        expect(appleReturn).toBeCloseTo(0.1);
      });

      it("should log errors for failed assets", () => {
        expect(logger.error).toHaveBeenCalledWith(
          expect.stringContaining(`Failed to process weekly return for asset: ${ASSET_ID_2}`),
          expect.objectContaining({
            module: "InvestmentProductService",
            method: "cacheAllAssetsWeeklyReturns"
          })
        );
      });

      it("should not cache data for failed assets", async () => {
        const microsoftReturn = await RedisClientService.Instance.get(`asset:weeklyReturn:${ASSET_ID_2}`);
        expect(microsoftReturn).toBeUndefined();
      });
    });
  });

  describe("getAssetData", () => {
    describe("when there is a stock asset in EOD but not in Redis", () => {
      let eodDataResponse: EodStockFundamentalsResponseType;

      beforeAll(async () => {
        jest.resetAllMocks();

        eodDataResponse = buildStockFundamentalsResponse();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should retrieve correct data", async () => {
        // Before we call getAssetData, Redis does not have fundamental data for that asset
        expect(await RedisClientService.Instance.get("eod:fundamentals:equities_microsoft")).toEqual(undefined);

        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType)?.about).toEqual(
          expect.objectContaining({ exchange: eodDataResponse.General.Exchange })
        );
      });

      it("should write response into Redis", async () => {
        // Before getAssetData, Redis has fundamental data for that asset
        expect(await RedisClientService.Instance.get("eod:fundamentals:equities_microsoft")).toEqual(
          eodDataResponse
        );
      });
    });

    describe("when there is a stock asset in Redis and CEO is the second officer", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 22000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Mr. Nick Chairman",
                Title: "Chairman"
              },
              1: {
                Name: "Mrs. Mariah CEO",
                Title: "Vice - Chairman & CEO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should retrieve correct officer from response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            ceo: "Mariah CEO"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and there is no CEO in first two officers", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 22000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Mr. Nick Chairman",
                Title: "Chairman"
              },
              1: {
                Name: "Mrs. Mariah CEO",
                Title: "Co-Founder & Director"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should retrieve correct officer from response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            ceo: "-"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and there is only one officer", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 22000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Mr. Nick Chairman",
                Title: "CFO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should retrieve correct officer from response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            ceo: "-"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and address is missing", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 22000,
            AddressData: undefined,
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Satya Nadella",
                Title: "Chairman & CEO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should have a dash as the headquarters", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            headquarters: "-"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and employees are less than 500", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 300,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Satya Nadella",
                Title: "Chairman & CEO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should have a dash as the employees", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            employees: "-"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, employees are more than 500 and the user locale is en", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 1000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Satya Nadella",
                Title: "Chairman & CEO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should have a correctly formatted employees number", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            employees: "1,000"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, employees are more than 500 and the user locale is el", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 1000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Satya Nadella",
                Title: "Chairman & CEO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should have a correctly formatted employees number", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "el",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).about).toEqual(
          expect.objectContaining({
            employees: "1.000"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and analyst ratings are missing", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 1000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Satya Nadella",
                Title: "Chairman & CEO"
              }
            },
            LogoURL: "/img/logos/US/MSFT.png"
          },
          AnalystRatings: {
            StrongBuy: null,
            Buy: null,
            Hold: null,
            Sell: null,
            StrongSell: null
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include analyst ratings in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).analystViews).toBeUndefined();
      });
    });

    describe("when there is a stock asset in Redis, market cap is in the 9 figures and the user locale is 'en'", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          Highlights: {
            DividendYield: faker.number.int({ min: 0.01, max: 1 }),
            EarningsShare: faker.number.int({ min: 1, max: 100 }),
            PERatio: faker.number.int({ min: 1, max: 100 }),
            MarketCapitalization: *********, // 125.4 million
            WallStreetTargetPrice: faker.number.int({ min: 100, max: 200 })
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should format the market cap correctly", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).metrics).toEqual(
          expect.objectContaining({
            marketCap: "$125M"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, market cap is in the 10 figures and the user locale is 'en'", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          Highlights: {
            DividendYield: faker.number.int({ min: 0.01, max: 1 }),
            EarningsShare: faker.number.int({ min: 1, max: 100 }),
            PERatio: faker.number.int({ min: 1, max: 100 }),
            MarketCapitalization: *********0, // 1.254 billion
            WallStreetTargetPrice: faker.number.int({ min: 100, max: 200 })
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should format the market cap correctly", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).metrics).toEqual(
          expect.objectContaining({
            marketCap: "$1.25B"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, market cap is in the 11 figures and the user locale is 'en'", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          Highlights: {
            DividendYield: faker.number.int({ min: 0.01, max: 1 }),
            EarningsShare: faker.number.int({ min: 1, max: 100 }),
            PERatio: faker.number.int({ min: 1, max: 100 }),
            MarketCapitalization: *********00, // 12.54 billion
            WallStreetTargetPrice: faker.number.int({ min: 100, max: 200 })
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should format the market cap correctly", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).metrics).toEqual(
          expect.objectContaining({
            marketCap: "$12.5B"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, market cap is in the 12 figures and the user locale is 'en'", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          Highlights: {
            DividendYield: faker.number.int({ min: 0.01, max: 1 }),
            EarningsShare: faker.number.int({ min: 1, max: 100 }),
            PERatio: faker.number.int({ min: 1, max: 100 }),
            MarketCapitalization: *********000, // 125.4 billion
            WallStreetTargetPrice: faker.number.int({ min: 100, max: 200 })
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should format the market cap correctly", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).metrics).toEqual(
          expect.objectContaining({
            marketCap: "$125B"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, market cap is in the 13 figures and the user locale is 'en'", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          Highlights: {
            DividendYield: faker.number.int({ min: 0.01, max: 1 }),
            EarningsShare: faker.number.int({ min: 1, max: 100 }),
            PERatio: faker.number.int({ min: 1, max: 100 }),
            MarketCapitalization: *********0000, // 1.25 trillion
            WallStreetTargetPrice: faker.number.int({ min: 100, max: 200 })
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should format the market cap correctly", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).metrics).toEqual(
          expect.objectContaining({
            marketCap: "$1.25T"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis, market cap is in the 13 figures and the user locale is 'el'", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          Highlights: {
            DividendYield: faker.number.int({ min: 0.01, max: 1 }),
            EarningsShare: faker.number.int({ min: 1, max: 100 }),
            PERatio: faker.number.int({ min: 1, max: 100 }),
            MarketCapitalization: *********0000, // 1.25 trillion
            WallStreetTargetPrice: faker.number.int({ min: 100, max: 200 })
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should format the market cap correctly", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "el",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).metrics).toEqual(
          expect.objectContaining({
            marketCap: "$1,25T"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and there is a draw between buy and sell", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 20,
            Buy: 20,
            Hold: 10,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should set buy as the majority", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).analystViews).toEqual(
          expect.objectContaining({
            isMajority: "buy"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and there is a draw between buy and hold", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 20,
            Buy: 20,
            Hold: 40,
            Sell: 5,
            StrongSell: 5
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should set buy as the majority", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).analystViews).toEqual(
          expect.objectContaining({
            isMajority: "buy"
          })
        );
      });
    });

    describe("when there is a stock asset in Redis and there is a draw between sell and hold", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should set sell as the majority", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as StockAssetFundamentalsType).analystViews).toEqual(
          expect.objectContaining({
            isMajority: "sell"
          })
        );
      });
    });

    describe("when there is a stock asset that is ADR", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'ADR' tag", async () => {
        const { tags } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("ADR");
      });
    });

    describe("when there is a stock asset that supports fractional investing", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'FRACTIONAL' tag", async () => {
        const { tags } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("FRACTIONAL");
      });
    });

    describe("when there is a stock asset and current time is within market hours", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date("2024-01-03T09:30:00.000-05:00").valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'MARKET_OPEN' tag and market info", async () => {
        const { tags, marketInfo } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("MARKET_OPEN");
        expect(marketInfo).toMatchObject({
          isOpen: true,
          nextMarketOpen: DateTime.fromISO("2024-01-04T09:30:00.000", { zone: "America/New_York" }).toMillis()
        });
      });
    });

    describe("when there is a stock asset and current time is within market hours (daylight savings period)", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date("2024-06-03T09:30:00.000-04:00").valueOf()); // Dailight savings period is GMT-4

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'MARKET_OPEN' tag and market info", async () => {
        const { tags, marketInfo } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("MARKET_OPEN");
        expect(marketInfo).toMatchObject({
          isOpen: true,
          nextMarketOpen: DateTime.fromISO("2024-06-04T09:30:00.000", { zone: "America/New_York" }).toMillis()
        });
      });
    });

    describe("when there is a stock asset and current time is within market hours but its a US bank holiday", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date("2024-07-04:14:45:00Z").getTime());

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'MARKET_CLOSED' tag and market info", async () => {
        const { tags, marketInfo } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("MARKET_CLOSED");
        expect(marketInfo).toMatchObject({
          isOpen: false,
          nextMarketOpen: DateTime.fromISO("2024-07-05T09:30:00.000", { zone: "America/New_York" }).toMillis()
        });
      });
    });

    describe("when there is a stock asset and current time is outside of market hours and it's a Saturday", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date("2024-06-01:22:00:00Z").getTime());

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'MARKET_CLOSED' tag", async () => {
        const { tags, marketInfo } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("MARKET_CLOSED");
        expect(marketInfo).toMatchObject({
          isOpen: false,
          nextMarketOpen: DateTime.fromISO("2024-06-03T09:30:00.000", { zone: "America/New_York" }).toMillis()
        });
      });
    });

    describe("when there is a stock asset and current time is outside of market hours before today's market open", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date("2024-06-03:09:00:00-04:00").getTime());

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'MARKET_CLOSED' tag with nextMarketOpen as today", async () => {
        const { tags, marketInfo } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("MARKET_CLOSED");
        expect(marketInfo).toMatchObject({
          isOpen: false,
          nextMarketOpen: DateTime.fromISO("2024-06-03T09:30:00.000", { zone: "America/New_York" }).toMillis()
        });
      });
    });

    describe("when there is a stock asset and current time is outside of market hours after today's market open", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date("2024-06-03:18:00:00-04:00").getTime());

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'MARKET_CLOSED' tag with nextMarketOpen as tommorow", async () => {
        const { tags, marketInfo } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("MARKET_CLOSED");
        expect(marketInfo).toMatchObject({
          isOpen: false,
          nextMarketOpen: DateTime.fromISO("2024-06-04T09:30:00.000", { zone: "America/New_York" }).toMillis()
        });
      });
    });

    describe("when there is a stock asset commission-free", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_bp", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 5,
            Buy: 5,
            Hold: 40,
            Sell: 20,
            StrongSell: 20
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_bp", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include 'COMMISSION_FREE' tag", async () => {
        const { tags } = (await InvestmentProductService.getAssetData(
          "equities_bp",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).toContain("COMMISSION_FREE");
      });
    });

    describe("when there is a stock asset in Redis and analyst views can be divided into /100 percentage without needing rounding", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 });

        const eodDataResponse = buildStockFundamentalsResponse({
          AnalystRatings: {
            StrongBuy: 12,
            Buy: 6,
            Hold: 6,
            Sell: 0,
            StrongSell: 0
          }
        });

        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should set sell as the majority", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_microsoft",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        // Analyst views are divided perfectly into 25% and 75% and therefore rounding is not needed
        expect((fundamentals as StockAssetFundamentalsType).analystViews).toEqual(
          expect.objectContaining({
            totalAnalysts: 24,
            percentageBuy: 75,
            percentageSell: 0,
            percentageHold: 25,
            isMajority: "buy"
          })
        );
      });
    });

    describe("when there is an ETF asset and current time is outside of market hours and realtime execution is disabled", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "0" },
              "United Kingdom": { "Equity_%": "0" },
              "Europe Developed": { "Equity_%": "0" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "0" },
              Japan: { "Equity_%": "0" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "0" },
              "Asia Emerging": { "Equity_%": "0" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include 'MARKET_CLOSED' tag", async () => {
        const { tags } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(tags).not.toContain("MARKET_CLOSED");
      });
    });

    describe("when there is an ETF asset and realtime execution is enabled", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "0" },
              "United Kingdom": { "Equity_%": "0" },
              "Europe Developed": { "Equity_%": "0" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "0" },
              Japan: { "Equity_%": "0" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "0" },
              "Asia Emerging": { "Equity_%": "0" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include SMART_EXECUTION tag", async () => {
        const { tags } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          {
            isRealtimeETFExecutionEnabled: true
          }
        )) as AssetDataResponseType;

        expect(tags).toContain("SMART_EXECUTION");
      });
    });

    describe("when there is an ETF asset and realtime execution is disabled", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "0" },
              "United Kingdom": { "Equity_%": "0" },
              "Europe Developed": { "Equity_%": "0" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "0" },
              Japan: { "Equity_%": "0" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "0" },
              "Asia Emerging": { "Equity_%": "0" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include COMMISSION_FREE tag", async () => {
        const { tags } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          {
            isRealtimeETFExecutionEnabled: false
          }
        )) as AssetDataResponseType;

        expect(tags).toContain("COMMISSION_FREE");
      });
    });

    describe("when there is an ETF asset and it has a different fundamentals ticker than its formal ticker", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_global_pharma", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "0" },
              "United Kingdom": { "Equity_%": "0" },
              "Europe Developed": { "Equity_%": "0" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "0" },
              Japan: { "Equity_%": "0" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "0" },
              "Asia Emerging": { "Equity_%": "0" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };
        const eodDataResponseForFormalTicker = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2300",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "0" },
              "United Kingdom": { "Equity_%": "0" },
              "Europe Developed": { "Equity_%": "0" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "0" },
              Japan: { "Equity_%": "0" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "0" },
              "Asia Emerging": { "Equity_%": "0" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("eod:fundamentals:equities_global_pharma", eodDataResponse),
          RedisClientService.Instance.set(
            "eod:fundamentals:formalticker:equities_global_pharma",
            eodDataResponseForFormalTicker
          )
        ]);
      });
      afterAll(async () => await clearDb());

      it("should include the expense ratio based on the formal ticker", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_global_pharma",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).expenseRatio).toBe("0.23");
      });
    });

    describe("when there is an ETF asset in Redis and there are no world region weights", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "0" },
              "United Kingdom": { "Equity_%": "0" },
              "Europe Developed": { "Equity_%": "0" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "0" },
              Japan: { "Equity_%": "0" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "0" },
              "Asia Emerging": { "Equity_%": "0" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include world regions in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).geographyDistribution).toBeUndefined();
      });
    });

    describe("when there is an ETF asset in Redis and there are no sector weights", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "64.319" },
              "United Kingdom": { "Equity_%": "0.26825" },
              "Europe Developed": { "Equity_%": "6.535" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "4.253" },
              Japan: { "Equity_%": "5.312" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "18.636" },
              "Asia Emerging": { "Equity_%": "0.677" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "0" },
              "Financial Services": { "Equity_%": "0" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "0" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "0" },
              Technology: { "Equity_%": "0" },
              "Consumer Defensive": { "Equity_%": "0" },
              Healthcare: { "Equity_%": "0" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include sectors in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).sectorDistribution).toBeUndefined();
      });
    });

    describe("when there is an ETF asset in Redis and there is no dividend yield", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "64.319" },
              "United Kingdom": { "Equity_%": "0.26825" },
              "Europe Developed": { "Equity_%": "6.535" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "4.253" },
              Japan: { "Equity_%": "5.312" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "18.636" },
              "Asia Emerging": { "Equity_%": "0.677" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "4.49357" },
              "Financial Services": { "Equity_%": "1.60887" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "2.10227" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "1.23068" },
              Technology: { "Equity_%": "89.16771" },
              "Consumer Defensive": { "Equity_%": "0.26825" },
              Healthcare: { "Equity_%": "1.12864" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should include dividend yield as '-' in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect(
          ((fundamentals as ETFAssetFundamentalsType).indexStats as NonBondIndexStatsType).dividendYield
        ).toBe("-");
      });
    });

    describe("when there is an ETF asset in Redis and there is no sector", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "commodities_gold", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "64.319" },
              "United Kingdom": { "Equity_%": "0.26825" },
              "Europe Developed": { "Equity_%": "6.535" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "4.253" },
              Japan: { "Equity_%": "5.312" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "18.636" },
              "Asia Emerging": { "Equity_%": "0.677" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "0" },
              "Financial Services": { "Equity_%": "0" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "0" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "0" },
              Technology: { "Equity_%": "0" },
              "Consumer Defensive": { "Equity_%": "0" },
              Healthcare: { "Equity_%": "0" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:commodities_gold", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include sectors in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "commodities_gold",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).sectorDistribution).toBeUndefined();
      });
    });

    describe("when there is an ETF asset in Redis and it is of Commodities asset class", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "commodities_gold", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "64.319" },
              "United Kingdom": { "Equity_%": "0.26825" },
              "Europe Developed": { "Equity_%": "6.535" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "4.253" },
              Japan: { "Equity_%": "5.312" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "18.636" },
              "Asia Emerging": { "Equity_%": "0.677" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "0" },
              "Financial Services": { "Equity_%": "0" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "0" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "0" },
              Technology: { "Equity_%": "0" },
              "Consumer Defensive": { "Equity_%": "0" },
              Healthcare: { "Equity_%": "0" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:commodities_gold", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include top holdings in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "commodities_gold",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).topHoldings).toBeUndefined();
      });
    });

    describe("when there is an ETF asset in Redis and it is of Real Estate asset class", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "real_estate_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "64.319" },
              "United Kingdom": { "Equity_%": "0.26825" },
              "Europe Developed": { "Equity_%": "6.535" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "4.253" },
              Japan: { "Equity_%": "5.312" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "18.636" },
              "Asia Emerging": { "Equity_%": "0.677" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "0" },
              "Financial Services": { "Equity_%": "0" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "0" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "0" },
              Technology: { "Equity_%": "0" },
              "Consumer Defensive": { "Equity_%": "0" },
              Healthcare: { "Equity_%": "0" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:real_estate_us", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include top holdings in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "real_estate_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).topHoldings).toBeUndefined();
      });
    });

    describe("when there is an ETF asset in Redis and it is of synthetic replication", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us_energy_broad", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            },
            World_Regions: {
              "North America": { "Equity_%": "64.319" },
              "United Kingdom": { "Equity_%": "0.26825" },
              "Europe Developed": { "Equity_%": "6.535" },
              "Europe Emerging": { "Equity_%": "0" },
              "Africa/Middle East": { "Equity_%": "4.253" },
              Japan: { "Equity_%": "5.312" },
              Australasia: { "Equity_%": "0" },
              "Asia Developed": { "Equity_%": "18.636" },
              "Asia Emerging": { "Equity_%": "0.677" },
              "Latin America": { "Equity_%": "0" }
            },
            Sector_Weights: {
              "Basic Materials": { "Equity_%": "0" },
              "Consumer Cyclicals": { "Equity_%": "0" },
              "Financial Services": { "Equity_%": "0" },
              "Real Estate": { "Equity_%": "0" },
              "Communication Services": { "Equity_%": "0" },
              Energy: { "Equity_%": "0" },
              Industrials: { "Equity_%": "0" },
              Technology: { "Equity_%": "0" },
              "Consumer Defensive": { "Equity_%": "0" },
              Healthcare: { "Equity_%": "0" },
              Utilities: { "Equity_%": "0" }
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us_energy_broad", eodDataResponse);
      });
      afterAll(async () => await clearDb());

      it("should not include top holdings in the response", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us_energy_broad",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).topHoldings).toBeUndefined();
      });
    });

    describe("when there is a ETF asset in Redis and EOD does not provide a holding code", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "State of Qatar 4.82%": {
                // `Code` field is not provided
                Name: "State of Qatar 4.82%",
                "Assets_%": 0.56
              }
            }
          },
          World_Regions: {
            "North America": {
              "Equity_%": "0",
              Relative_to_Category: "10"
            },
            "United Kingdom": {
              "Equity_%": "0",
              Relative_to_Category: "2.5"
            },
            "Europe Developed": {
              "Equity_%": "0",
              Relative_to_Category: "10"
            },
            "Europe Emerging": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Africa/Middle East": {
              "Equity_%": "0",
              Relative_to_Category: "62.5"
            },
            Japan: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Australasia: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Asia Developed": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Asia Emerging": {
              "Equity_%": "0",
              Relative_to_Category: "15"
            },
            "Latin America": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            }
          },
          Sector_Weights: {
            "Basic Materials": {
              "Equity_%": "0",
              Relative_to_Category: "67.5"
            },
            "Consumer Cyclicals": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Financial Services": {
              "Equity_%": "0",
              Relative_to_Category: "17.5"
            },
            "Real Estate": {
              "Equity_%": "0",
              Relative_to_Category: "7.5"
            },
            "Communication Services": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Energy: {
              "Equity_%": "0",
              Relative_to_Category: "7.5"
            },
            Industrials: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Technology: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Consumer Defensive": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Healthcare: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Utilities: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
        await RedisClientService.Instance.set("eod:customLogos:equities_us", {});
      });
      afterAll(async () => await clearDb());

      it("should return empty string logoURL", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).topHoldings).toEqual([
          { logoUrl: "", name: "State of Qatar 4.82%", weight: "0.56%" }
        ]);
      });
    });

    describe("when there is a ETF asset in Redis and EOD does provide a holding code", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            }
          },
          World_Regions: {
            "North America": {
              "Equity_%": "0",
              Relative_to_Category: "10"
            },
            "United Kingdom": {
              "Equity_%": "0",
              Relative_to_Category: "2.5"
            },
            "Europe Developed": {
              "Equity_%": "0",
              Relative_to_Category: "10"
            },
            "Europe Emerging": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Africa/Middle East": {
              "Equity_%": "0",
              Relative_to_Category: "62.5"
            },
            Japan: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Australasia: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Asia Developed": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Asia Emerging": {
              "Equity_%": "0",
              Relative_to_Category: "15"
            },
            "Latin America": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            }
          },
          Sector_Weights: {
            "Basic Materials": {
              "Equity_%": "0",
              Relative_to_Category: "67.5"
            },
            "Consumer Cyclicals": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Financial Services": {
              "Equity_%": "0",
              Relative_to_Category: "17.5"
            },
            "Real Estate": {
              "Equity_%": "0",
              Relative_to_Category: "7.5"
            },
            "Communication Services": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Energy: {
              "Equity_%": "0",
              Relative_to_Category: "7.5"
            },
            Industrials: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Technology: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Consumer Defensive": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Healthcare: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Utilities: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
        await RedisClientService.Instance.set("eod:customLogos:equities_us", {});
      });
      afterAll(async () => await clearDb());

      it("should return eod logo url from our CF bucket", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).topHoldings).toEqual([
          {
            logoUrl: "https://etf-holdings-logos.wealthyhood.dev/eod/AAPL.png",
            name: "Apple",
            weight: "3.73%"
          }
        ]);
      });
    });

    describe("when there is a ETF asset in Redis and EOD does provide a holding code AND a custom logo exists", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
          if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
            return Promise.resolve({
              data: {
                expected_return: 8.8,
                annual_risk: 16.6
              }
            });
          }

          return Promise.resolve({});
        });

        await buildInvestmentProduct(true, { assetId: "equities_us", price: 200 });

        const eodDataResponse = {
          ETF_Data: {
            Valuations_Growth: {
              Valuations_Rates_Portfolio: {
                "Price/Prospective Earnings": "13.88",
                "Dividend-Yield Factor": "2.46"
              }
            },
            Ongoing_Charge: "0.2200",
            Holdings_Count: 100,
            Top_10_Holdings: {
              "AAPL.US": {
                Name: "Apple Inc",
                "Assets_%": 3.73,
                Code: "AAPL",
                Exchange: "US"
              }
            }
          },
          World_Regions: {
            "North America": {
              "Equity_%": "0",
              Relative_to_Category: "10"
            },
            "United Kingdom": {
              "Equity_%": "0",
              Relative_to_Category: "2.5"
            },
            "Europe Developed": {
              "Equity_%": "0",
              Relative_to_Category: "10"
            },
            "Europe Emerging": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Africa/Middle East": {
              "Equity_%": "0",
              Relative_to_Category: "62.5"
            },
            Japan: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Australasia: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Asia Developed": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Asia Emerging": {
              "Equity_%": "0",
              Relative_to_Category: "15"
            },
            "Latin America": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            }
          },
          Sector_Weights: {
            "Basic Materials": {
              "Equity_%": "0",
              Relative_to_Category: "67.5"
            },
            "Consumer Cyclicals": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Financial Services": {
              "Equity_%": "0",
              Relative_to_Category: "17.5"
            },
            "Real Estate": {
              "Equity_%": "0",
              Relative_to_Category: "7.5"
            },
            "Communication Services": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Energy: {
              "Equity_%": "0",
              Relative_to_Category: "7.5"
            },
            Industrials: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Technology: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            "Consumer Defensive": {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Healthcare: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            },
            Utilities: {
              "Equity_%": "0",
              Relative_to_Category: "0"
            }
          }
        };

        await RedisClientService.Instance.set("eod:fundamentals:equities_us", eodDataResponse);
        await RedisClientService.Instance.set("eod:customLogos:equities_us", {
          AAPL: "https://etf-holdings-logos.wealthyhood.dev/AAPL.png"
        });
      });
      afterAll(async () => await clearDb());

      it("should return the custom logo url", async () => {
        const { fundamentals } = (await InvestmentProductService.getAssetData(
          "equities_us",
          "en",
          entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        )) as AssetDataResponseType;

        expect((fundamentals as ETFAssetFundamentalsType).topHoldings).toEqual([
          {
            logoUrl: "https://etf-holdings-logos.wealthyhood.dev/AAPL.png",
            name: "Apple",
            weight: "3.73%"
          }
        ]);
      });
    });
  });

  describe("cacheAssetFundamentalsData", () => {
    describe("when there is an ETF asset with data on EOD", () => {
      const eodEtfDataResponse = {
        ETF_Data: {
          Valuations_Growth: {
            Valuations_Rates_Portfolio: {
              "Price/Prospective Earnings": "13.88",
              "Dividend-Yield Factor": "2.46"
            }
          },
          Ongoing_Charge: "0.2200",
          Holdings_Count: 100,
          Top_10_Holdings: {
            "AAPL.US": {
              Name: "Apple Inc",
              "Assets_%": 3.73,
              Code: "AAPL",
              Exchange: "US"
            }
          },
          World_Regions: {
            "North America": { "Equity_%": "0" },
            "United Kingdom": { "Equity_%": "0" },
            "Europe Developed": { "Equity_%": "0" },
            "Europe Emerging": { "Equity_%": "0" },
            "Africa/Middle East": { "Equity_%": "0" },
            Japan: { "Equity_%": "0" },
            Australasia: { "Equity_%": "0" },
            "Asia Developed": { "Equity_%": "0" },
            "Asia Emerging": { "Equity_%": "0" },
            "Latin America": { "Equity_%": "0" }
          },
          Sector_Weights: {
            "Basic Materials": { "Equity_%": "0" },
            "Consumer Cyclicals": { "Equity_%": "4.49357" },
            "Financial Services": { "Equity_%": "1.60887" },
            "Real Estate": { "Equity_%": "0" },
            "Communication Services": { "Equity_%": "2.10227" },
            Energy: { "Equity_%": "0" },
            Industrials: { "Equity_%": "1.23068" },
            Technology: { "Equity_%": "89.16771" },
            "Consumer Defensive": { "Equity_%": "0.26825" },
            Healthcare: { "Equity_%": "1.12864" },
            Utilities: { "Equity_%": "0" }
          }
        }
      };

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(RedisClientService.Instance, "set");
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse as any);

        await buildInvestmentProduct(true, { assetId: "equities_us" });

        await InvestmentProductService.cacheAssetFundamentalsData();
      });
      afterAll(async () => await clearDb());

      it("should add data in Redis", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:fundamentals:equities_us");
        expect(cachedData).toEqual(eodEtfDataResponse);
      });
    });
  });

  describe("cacheAssetHistoricalPrices", () => {
    describe("when there is no stock split for the asset", () => {
      const TODAY = new Date("2024-01-01");
      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";
      const WEEKLY_INTRADAY_DATA: { timestamp: number; close: number }[] = [
        { timestamp: TODAY.getTime(), close: 10 }
      ];
      const MONTHLY_INTRADAY_DATA: { timestamp: number; close: number }[] = [
        { timestamp: TODAY.getTime(), close: 20 }
      ];
      const HISTORICAL_PRICE_DATA: { date: string; close: number }[] = [{ date: TODAY.toDateString(), close: 1 }];

      beforeAll(async () => {
        // set conditions
        Date.now = jest.fn(() => TODAY.getTime());
        jest
          .spyOn(eodService, "getIntradayPrices")
          .mockImplementation(
            async (
              assetId: investmentUniverseConfig.AssetType,
              options: { from: Date }
            ): Promise<{ timestamp: number; close: number }[]> => {
              if (new Date(options.from).getTime() >= DateUtil.getDateOfDaysAgo(TODAY, 8).getTime()) {
                return WEEKLY_INTRADAY_DATA;
              } else if (new Date(options.from).getTime() >= DateUtil.getDateOfDaysAgo(TODAY, 31).getTime()) {
                return MONTHLY_INTRADAY_DATA;
              } else {
                return [];
              }
            }
          );
        jest.spyOn(eodService, "getHistoricalPrices").mockResolvedValue(HISTORICAL_PRICE_DATA);
        await buildInvestmentProduct(false, { assetId: ASSET_ID });

        // run method
        await InvestmentProductService.cacheAssetHistoricalPrices();
      });
      afterAll(async () => await clearDb());

      it("should cache in Redis weekly intraday data", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:historical:w:equities_apple");
        expect(cachedData).toMatchObject(WEEKLY_INTRADAY_DATA);
      });

      it("should cache in Redis monthly intraday data", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:historical:m:equities_apple");
        expect(cachedData).toMatchObject(MONTHLY_INTRADAY_DATA);
      });

      it("should cache in Redis the asset price at the beginning of the month", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:price_30d_ago:equities_apple");
        expect(cachedData).toBe(MONTHLY_INTRADAY_DATA[0].close);
      });

      it("should cache in Redis the asset price yesterday", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:price_1d_ago:equities_apple");
        expect(cachedData).toBe(HISTORICAL_PRICE_DATA.at(-1).close);
      });

      it("should cache in Redis all historical data", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:historical:equities_apple");
        expect(cachedData).toMatchObject(HISTORICAL_PRICE_DATA);
      });
    });

    describe("when there is a stock split for the asset", () => {
      const TODAY = new Date("2024-01-01");
      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";
      const WEEKLY_INTRADAY_DATA: { timestamp: number; close: number }[] = [
        { timestamp: TODAY.getTime(), close: 10 }
      ];
      const MONTHLY_INTRADAY_DATA: { timestamp: number; close: number }[] = [
        { timestamp: TODAY.getTime(), close: 20 }
      ];
      const HISTORICAL_PRICE_DATA: { date: string; close: number }[] = [{ date: TODAY.toDateString(), close: 1 }];

      beforeAll(async () => {
        // set conditions
        Date.now = jest.fn(() => TODAY.getTime());
        jest
          .spyOn(eodService, "getIntradayPrices")
          .mockImplementation(
            async (
              assetId: investmentUniverseConfig.AssetType,
              options: { from: Date }
            ): Promise<{ timestamp: number; close: number }[]> => {
              if (new Date(options.from).getTime() >= DateUtil.getDateOfDaysAgo(TODAY, 8).getTime()) {
                return WEEKLY_INTRADAY_DATA;
              } else if (new Date(options.from).getTime() >= DateUtil.getDateOfDaysAgo(TODAY, 31).getTime()) {
                return MONTHLY_INTRADAY_DATA;
              } else {
                return [];
              }
            }
          );
        jest.spyOn(eodService, "getHistoricalPrices").mockResolvedValue(HISTORICAL_PRICE_DATA);
        await buildInvestmentProduct(false, { assetId: ASSET_ID });

        await buildStockSplitCorporateEvent({
          asset: "equities_apple",
          date: DateUtil.getYesterday(TODAY),
          splitRatio: "5.000000/1.000000"
        });

        // run method
        await InvestmentProductService.cacheAssetHistoricalPrices();
      });
      afterAll(async () => await clearDb());

      it("should cache in Redis the ADJUSTED asset price (based on the stock split) at the beginning of the month", async () => {
        const cachedData = await RedisClientService.Instance.get("eod:price_30d_ago:equities_apple");
        expect(cachedData).toBe(Decimal.div(MONTHLY_INTRADAY_DATA[0].close, 5).toNumber());
      });
    });
  });

  describe("getPricesAndReturnsByTenor", () => {
    const TODAY = new Date("2024-05-01");

    beforeEach(async () => {
      Date.now = jest.fn(() => TODAY.getTime());
      await clearDb();
    });

    it("should return data organised by tenor and include today's intraday prices", async () => {
      // set conditions
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(TODAY, 30);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

      // today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: TODAY,
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });
      // weekly tenor intraday data
      const eodWeeklyIntraDayData = [{ timestamp: new Date(YESTERDAY).getTime(), close: 1 }];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);
      // monthly tenor intraday data
      const eodMonthlyIntraDayData = [{ timestamp: new Date(WEEK_AGO).getTime(), close: 2 }];
      await RedisClientService.Instance.set("eod:historical:m:equities_apple", eodMonthlyIntraDayData);
      // historical data (3m, 6m, 1y, max)
      const eodHistoricalDailyData = [{ date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate(), close: 5 }];
      await RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData);

      // run method
      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      // assertions
      expect(pricesByTenor).toMatchObject(
        expect.objectContaining({
          "1w": {
            // [1, 10]
            data: [
              { timestamp: eodWeeklyIntraDayData[0].timestamp, close: eodWeeklyIntraDayData[0].close },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 1) / 1 * 100
            returns: 900,
            displayIntraday: true
          },
          "1m": {
            // [2, 10]
            data: [
              { timestamp: eodMonthlyIntraDayData[0].timestamp, close: eodMonthlyIntraDayData[0].close },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 2) / 2 * 100
            returns: 400,
            displayIntraday: true
          },
          "3m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "6m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "1y": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          max: {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          }
        })
      );
    });

    it("should adjust price based on stock split if one exists", async () => {
      // set conditions
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(TODAY, 30);

      // We define the stock split date to have happened 3 days ago
      const STOCK_SPLIT_DATE = DateUtil.getDateOfDaysAgo(TODAY, 3);
      const ONE_DAY_BEFORE_SPLIT_DATE = DateUtil.getDateOfDaysAgo(STOCK_SPLIT_DATE, 1);
      const ONE_DAY_AFTER_SPLIT_DATE = DateUtil.getDateAfterNdays(STOCK_SPLIT_DATE, 1);

      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

      // today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: TODAY,
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });
      // weekly tenor intraday data
      const eodWeeklyIntraDayData = [
        { timestamp: new Date(ONE_DAY_BEFORE_SPLIT_DATE).getTime(), close: 5 },
        { timestamp: new Date(STOCK_SPLIT_DATE).getTime(), close: 1 },
        { timestamp: new Date(ONE_DAY_AFTER_SPLIT_DATE).getTime(), close: 1 },
        { timestamp: new Date(YESTERDAY).getTime(), close: 1 }
      ];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);
      // monthly tenor intraday data
      const eodMonthlyIntraDayData = [
        { timestamp: new Date(WEEK_AGO).getTime(), close: 10 },
        { timestamp: new Date(ONE_DAY_BEFORE_SPLIT_DATE).getTime(), close: 5 },
        { timestamp: new Date(STOCK_SPLIT_DATE).getTime(), close: 1 },
        { timestamp: new Date(ONE_DAY_AFTER_SPLIT_DATE).getTime(), close: 1 },
        { timestamp: new Date(YESTERDAY).getTime(), close: 5 }
      ];
      await RedisClientService.Instance.set("eod:historical:m:equities_apple", eodMonthlyIntraDayData);
      // historical data (3m, 6m, 1y, max)
      const eodHistoricalDailyData = [{ date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate(), close: 5 }];
      await RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData);

      await buildStockSplitCorporateEvent({
        asset: "equities_apple",
        date: STOCK_SPLIT_DATE,
        splitRatio: "5.000000/1.000000" // 1 -> 5 split ratio
      });

      // run method
      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      // assertions
      expect(pricesByTenor).toMatchObject(
        expect.objectContaining({
          "1w": {
            // [1, 1, 1, 1, 10]
            data: [
              {
                timestamp: eodWeeklyIntraDayData[0].timestamp,
                close: Decimal.div(eodWeeklyIntraDayData[0].close, 5).toNumber()
              },
              {
                timestamp: eodWeeklyIntraDayData[1].timestamp,
                close: Decimal.div(eodWeeklyIntraDayData[1].close, 1).toNumber()
              },
              { timestamp: eodWeeklyIntraDayData[2].timestamp, close: eodWeeklyIntraDayData[2].close },
              { timestamp: eodWeeklyIntraDayData[3].timestamp, close: eodWeeklyIntraDayData[3].close },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 1) / 1 * 100
            returns: 900,
            displayIntraday: true
          },
          "1m": {
            // [2, 10]
            data: [
              {
                timestamp: eodMonthlyIntraDayData[0].timestamp,
                close: Decimal.div(eodMonthlyIntraDayData[0].close, 5).toNumber()
              },
              {
                timestamp: eodMonthlyIntraDayData[1].timestamp,
                close: Decimal.div(eodMonthlyIntraDayData[1].close, 5).toNumber()
              },
              { timestamp: eodMonthlyIntraDayData[2].timestamp, close: eodMonthlyIntraDayData[2].close },
              { timestamp: eodMonthlyIntraDayData[3].timestamp, close: eodMonthlyIntraDayData[3].close },
              { timestamp: eodMonthlyIntraDayData[4].timestamp, close: eodMonthlyIntraDayData[4].close },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 2) / 2 * 100
            returns: 400,
            displayIntraday: true
          },
          "3m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "6m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "1y": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          max: {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          }
        })
      );
    });

    it("should request data from EOD if cached data does not exist", async () => {
      // set conditions
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(TODAY, 30);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

      // today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: TODAY,
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });
      // weekly tenor intraday data
      const eodWeeklyIntraDayData = [{ timestamp: new Date(YESTERDAY).getTime(), close: 1 }];
      await RedisClientService.Instance.del("eod:historical:w:equities_apple");
      // monthly tenor intraday data
      const eodMonthlyIntraDayData = [{ timestamp: new Date(WEEK_AGO).getTime(), close: 2 }];
      await RedisClientService.Instance.del("eod:historical:m:equities_apple");
      // historical data (3m, 6m, 1y, max)
      const eodHistoricalDailyData = [
        { date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate() as string, close: 5 }
      ];
      await RedisClientService.Instance.del("eod:historical:equities_apple");

      jest
        .spyOn(eodService, "getIntradayPrices")
        .mockImplementation(
          async (
            assetId: investmentUniverseConfig.AssetType,
            options: { from: Date }
          ): Promise<{ timestamp: number; close: number }[]> => {
            if (new Date(options.from).getTime() >= DateUtil.getDateOfDaysAgo(TODAY, 8).getTime()) {
              return eodWeeklyIntraDayData;
            } else if (new Date(options.from).getTime() >= DateUtil.getDateOfDaysAgo(TODAY, 31).getTime()) {
              return eodMonthlyIntraDayData;
            } else {
              return [];
            }
          }
        );
      jest.spyOn(eodService, "getHistoricalPrices").mockResolvedValue(eodHistoricalDailyData);

      // run method
      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      // assertions
      expect(pricesByTenor).toMatchObject(
        expect.objectContaining({
          "1w": {
            // [1, 10]
            data: [
              { timestamp: eodWeeklyIntraDayData[0].timestamp, close: eodWeeklyIntraDayData[0].close },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 1) / 1 * 100
            returns: 900,
            displayIntraday: true
          },
          "1m": {
            // [2, 10]
            data: [
              { timestamp: eodMonthlyIntraDayData[0].timestamp, close: eodMonthlyIntraDayData[0].close },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 2) / 2 * 100
            returns: 400,
            displayIntraday: true
          },
          "3m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "6m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "1y": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          max: {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          }
        })
      );
    });

    it("should not use latest intraday prices if the data is not for today", async () => {
      // set conditions
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(TODAY, 30);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });

      // latest intraday ticker - will be skipped
      await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(YESTERDAY),
        pricePerCurrency: { GBP: 10, EUR: 10, USD: 11 },
        investmentProduct: investmentProduct.id
      });
      // weekly tenor intraday data
      const eodWeeklyIntraDayData = [{ timestamp: new Date(YESTERDAY).getTime(), close: 1 }];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);
      // monthly tenor intraday data
      const eodMonthlyIntraDayData = [{ timestamp: new Date(WEEK_AGO).getTime(), close: 2 }];
      await RedisClientService.Instance.set("eod:historical:m:equities_apple", eodMonthlyIntraDayData);
      // historical data (3m, 6m, 1y, max)
      const eodHistoricalDailyData = [{ date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate(), close: 5 }];
      await RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData);

      // run method
      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      // assertions
      expect(pricesByTenor).toMatchObject(
        expect.objectContaining({
          "1w": {
            data: [{ timestamp: eodWeeklyIntraDayData[0].timestamp, close: eodWeeklyIntraDayData[0].close }],
            returns: 0,
            displayIntraday: true
          },
          "1m": {
            data: [{ timestamp: eodMonthlyIntraDayData[0].timestamp, close: eodMonthlyIntraDayData[0].close }],
            returns: 0,
            displayIntraday: true
          },
          "3m": {
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              }
            ],
            returns: 0,
            displayIntraday: false
          },
          "6m": {
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              }
            ],
            returns: 0,
            displayIntraday: false
          },
          "1y": {
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              }
            ],
            returns: 0,
            displayIntraday: false
          },
          max: {
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              }
            ],
            returns: 0,
            displayIntraday: false
          }
        })
      );
    });

    it("should sample data at 30-minute intervals for 1m tenor", async () => {
      const ASSET_ID = "equities_apple";
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(TODAY, 30);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });

      // Create today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: TODAY,
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });

      // Create monthly data with points every 5 minutes
      const eodMonthlyIntraDayData = [
        // 10:00 - 10:25 (6 points)
        { timestamp: new Date(MONTH_AGO).setHours(10, 0, 0, 0), close: 100 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 5, 0, 0), close: 101 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 10, 0, 0), close: 102 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 15, 0, 0), close: 103 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 20, 0, 0), close: 104 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 25, 0, 0), close: 105 },
        // 10:30 - 10:55 (6 points)
        { timestamp: new Date(MONTH_AGO).setHours(10, 30, 0, 0), close: 106 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 35, 0, 0), close: 107 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 40, 0, 0), close: 108 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 45, 0, 0), close: 109 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 50, 0, 0), close: 110 },
        { timestamp: new Date(MONTH_AGO).setHours(10, 55, 0, 0), close: 111 },
        // 11:00 - 11:25 (6 points)
        { timestamp: new Date(MONTH_AGO).setHours(11, 0, 0, 0), close: 112 },
        { timestamp: new Date(MONTH_AGO).setHours(11, 5, 0, 0), close: 113 },
        { timestamp: new Date(MONTH_AGO).setHours(11, 10, 0, 0), close: 114 },
        { timestamp: new Date(MONTH_AGO).setHours(11, 15, 0, 0), close: 115 },
        { timestamp: new Date(MONTH_AGO).setHours(11, 20, 0, 0), close: 116 },
        { timestamp: new Date(MONTH_AGO).setHours(11, 25, 0, 0), close: 117 }
      ];
      await RedisClientService.Instance.set(`eod:historical:m:${ASSET_ID}`, eodMonthlyIntraDayData);

      // Create historical data with multiple points per week
      const eodHistoricalDailyData = Array.from({ length: 600 }, (_, i) => ({
        date: DateTime.fromJSDate(DateUtil.getDateOfDaysAgo(TODAY, i)).toISODate(),
        close: 200 + i
      }));
      await RedisClientService.Instance.set(`eod:historical:${ASSET_ID}`, eodHistoricalDailyData);

      // Run method
      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      expect(pricesByTenor["1m"].data.length).toBe(5);
      expect(pricesByTenor["1m"]).toEqual(
        expect.objectContaining({
          returns: -90, // (10 - 100) / 100 * 100
          displayIntraday: true,
          data: expect.arrayContaining([
            {
              timestamp: new Date(MONTH_AGO).setHours(10, 0, 0, 0),
              close: 100
            },
            {
              timestamp: new Date(MONTH_AGO).setHours(10, 30, 0, 0),
              close: 106
            },
            {
              timestamp: new Date(MONTH_AGO).setHours(11, 0, 0, 0),
              close: 112
            },
            {
              timestamp: new Date(MONTH_AGO).setHours(11, 30, 0, 0),
              close: 117
            },
            {
              timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
              close: todaysIntraDayTicker.pricePerCurrency["USD"]
            }
          ])
        })
      );
    });

    it("should sample data at weekly intervals for max tenor and ensure Fridays", async () => {
      const ASSET_ID = "equities_apple";
      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });

      // Create today's ticker (on a Wednesday)
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: TODAY,
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });

      // Create historical data with multiple points per week
      // Generate 502+ points to test sampling
      const eodHistoricalDailyData = Array.from({ length: 505 }, (_, i) => {
        const date = DateUtil.getDateOfDaysAgo(TODAY, i);
        return {
          date: DateTime.fromJSDate(date).toISODate(),
          close: 200 + i
        };
      });
      await RedisClientService.Instance.set(`eod:historical:${ASSET_ID}`, eodHistoricalDailyData);

      // Run method
      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      // Verify max tenor data
      const maxData = pricesByTenor.max.data;

      // Check that all points except the last one are on Fridays
      maxData.slice(0, -1).forEach((point) => {
        const date = new Date(point.timestamp);
        expect(date.getDay()).toBe(5); // 5 is Friday
        expect(date.getHours()).toBe(0); // Should be midnight
        expect(date.getMinutes()).toBe(0);
        expect(date.getSeconds()).toBe(0);
      });

      // Check that the last point is today's ticker regardless of the day
      expect(maxData[maxData.length - 1]).toEqual({
        timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
        close: todaysIntraDayTicker.pricePerCurrency["USD"]
      });

      // Verify we have approximately the right number of points (505 days / 7 days per week ≈ 72 weeks + today's point)
      expect(maxData.length).toBeLessThanOrEqual(Math.ceil(505 / 7) + 1);
    });

    it("should return week old data in the weekly tenor if today data do NOT exist", async () => {
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);

      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });

      const eodWeeklyIntraDayData = [
        { timestamp: new Date(WEEK_AGO).getTime(), close: 1 },
        { timestamp: new Date(YESTERDAY).getTime(), close: 2 }
      ];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);

      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      expect(pricesByTenor["1w"].data).toEqual([
        { timestamp: new Date(WEEK_AGO).getTime(), close: 1 },
        {
          timestamp: new Date(YESTERDAY).getTime(),
          close: 2
        }
      ]);
    });

    it("should filter out week old data from the weekly tenor if today data exist", async () => {
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);

      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });

      const eodWeeklyIntraDayData = [
        { timestamp: new Date(WEEK_AGO).getTime(), close: 1 }, // Should be filtered out
        { timestamp: new Date(YESTERDAY).getTime(), close: 2 },
        { timestamp: new Date(TODAY).getTime(), close: 10 }
      ];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);

      const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

      expect(pricesByTenor["1w"].data).toEqual([
        { timestamp: new Date(YESTERDAY).getTime(), close: 2 },
        {
          timestamp: new Date(TODAY).getTime(),
          close: 10
        }
      ]);
    });

    describe("when today's intraday prices and historical data timestamps coincide", () => {
      it("should return the weekly by keep unique timestamps, prioritising today's intraday prices", async () => {
        // set conditions
        const ASSET_ID = "equities_apple";
        const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
        const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

        // today's tickers
        const todaysIntraDayTicker = await buildIntraDayAssetTicker({
          currency: "USD",
          timestamp: TODAY,
          pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
          investmentProduct: investmentProduct.id
        });

        // historical intraday ticker that should be overriden by today's intradayticker with the same timestamp
        const sameTimestampAsIntraDayTickerThatShouldNotBeUsed = {
          timestamp: new Date(TODAY).getTime(),
          close: 11
        };

        // weekly tenor intraday data
        const eodWeeklyIntraDayData = [
          { timestamp: new Date(YESTERDAY).getTime(), close: 2 },
          sameTimestampAsIntraDayTickerThatShouldNotBeUsed
        ];
        await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);

        // run method
        const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

        // assertions
        expect(pricesByTenor["1w"].data).toEqual([
          { timestamp: eodWeeklyIntraDayData[0].timestamp, close: eodWeeklyIntraDayData[0].close },
          {
            timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
            close: todaysIntraDayTicker.pricePerCurrency["USD"]
          }
        ]);
      });
    });

    describe("when today's intraday prices and historical data timestamps are badly ordered", () => {
      it("should return the weekly data ordered by timestamp", async () => {
        // set conditions
        const ASSET_ID = "equities_apple";
        const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
        const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

        // today's tickers
        const todaysIntraDayTicker = await buildIntraDayAssetTicker({
          currency: "USD",
          timestamp: TODAY,
          pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
          investmentProduct: investmentProduct.id
        });

        // historical intraday ticker that should be sorted to be after the intraday asset ticker
        const timestampOfTheSameDay2HoursLaterThatShouldBeSorted = {
          timestamp: new Date(TODAY).getTime() + 2 * 60 * 60 * 1000, //add 2 hours
          close: 12
        };

        // weekly tenor intraday data
        const eodWeeklyIntraDayData = [
          { timestamp: new Date(YESTERDAY).getTime(), close: 2 },
          timestampOfTheSameDay2HoursLaterThatShouldBeSorted
        ];
        await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);

        // run method
        const pricesByTenor = await InvestmentProductService.getPricesAndReturnsByTenor(ASSET_ID);

        // assertions
        expect(pricesByTenor["1w"].data).toEqual([
          { timestamp: eodWeeklyIntraDayData[0].timestamp, close: eodWeeklyIntraDayData[0].close },
          {
            timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
            close: todaysIntraDayTicker.pricePerCurrency["USD"]
          },
          {
            timestamp: timestampOfTheSameDay2HoursLaterThatShouldBeSorted.timestamp,
            close: timestampOfTheSameDay2HoursLaterThatShouldBeSorted.close
          }
        ]);
      });
    });
  });

  describe("getAssetRecentActivity", () => {
    const PAST_DATE = new Date("2021-12-07T09:00:00Z");
    const TODAY = new Date("2022-08-31T08:00:00Z");
    const FUTURE_DATE = new Date("2023-12-07T09:00:00Z");

    const DATE_OUTSIDE_MARKET_HOURS = new Date("2022-08-31T08:00:00Z");
    const DATE_INSIDE_MARKET_HOURS = new Date("2022-08-31T15:00:00Z");

    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let mandate: MandateDocument;

    beforeAll(async () => {
      jest.clearAllMocks();
    });

    beforeEach(async () => {
      await clearDb();
      Date.now = jest.fn(() => TODAY.valueOf());
      jest.resetAllMocks();
      user = await buildUser();
      await buildSubscription({ owner: user.id });
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
      mandate = await buildMandate({
        owner: user.id,
        bankAccount: user.bankAccounts[0].id,
        providers: {
          gocardless: {
            id: "MAN123",
            status: "active"
          }
        }
      });
      await buildTopUpAutomation({
        owner: user.id,
        mandate: mandate.id,
        portfolio: portfolio.id,
        category: "TopUpAutomation",
        active: true
      });

      await Promise.all([
        buildInvestmentProduct(false, { assetId: "equities_china" }),
        buildInvestmentProduct(false, { assetId: "equities_eu" })
      ]);
    });

    describe("when user has a NotStarted rebalance", () => {
      it("should not return any rebalance orders", async () => {
        await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "NotStarted",
          sellExecutionWindow: { start: FUTURE_DATE },
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_china");
        expect(receivedOrders.length).toBe(0);
      });
    });

    describe("when user has a PendingBuy rebalance", () => {
      it("should return the rebalance orders", async () => {
        const rebalance = await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "PendingBuy",
          sellExecutionWindow: { start: PAST_DATE },
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_china"]?.isin,
            quantity: 1,
            side: "Buy",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: TODAY } }
          }),
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_china");
        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: ASSET_CONFIG["equities_china"]?.isin,
              transaction: expect.objectContaining({ owner: user.id, _id: rebalance.id }),
              isCancellable: false
            })
          })
        );
      });
    });

    describe("when user has a PendingSell rebalance", () => {
      it("should return the rebalance orders", async () => {
        const rebalance = await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "PendingSell",
          sellExecutionWindow: { start: FUTURE_DATE },
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_china"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_china");
        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: ASSET_CONFIG["equities_china"]?.isin,
              transaction: expect.objectContaining({ owner: user.id, _id: rebalance.id }),
              isCancellable: false
            })
          })
        );
      });
    });

    describe("when user has a rejected rebalance", () => {
      it("should not return any rebalance orders", async () => {
        const rebalance = await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "Rejected",
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_china"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Rejected", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Rejected", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_china");
        expect(receivedOrders.length).toBe(0);
      });
    });

    describe("when user has a cancelled rebalance", () => {
      it("should return the rebalance orders", async () => {
        const rebalance = await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "Cancelled",
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_china"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Cancelled", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Cancelled", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_china");
        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: ASSET_CONFIG["equities_china"]?.isin,
              transaction: expect.objectContaining({ owner: user.id, _id: rebalance.id }),
              isCancellable: false
            })
          })
        );
      });
    });

    describe("when user has a settled rebalance", () => {
      it("should return the rebalance orders", async () => {
        const rebalance = await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "Settled",
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_china"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalance._id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_china");
        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: ASSET_CONFIG["equities_china"]?.isin,
              transaction: expect.objectContaining({ owner: user.id, _id: rebalance.id }),
              isCancellable: false
            })
          })
        );
      });
    });

    describe("when user has a pending dividend", () => {
      it("should return the the matching orders", async () => {
        await Promise.all([
          buildHoldingDTO(true, "equities_adobe", 1),
          buildDividendTransaction({ owner: user.id, asset: "equities_adobe", providers: {} }),
          buildDividendTransaction({ owner: user.id, asset: "equities_microsoft", providers: {} })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_adobe");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "dividend",

            item: expect.objectContaining({
              asset: "equities_adobe",
              owner: user.id
            })
          })
        );
      });
    });

    describe("when user has a cancelled dividend", () => {
      it("should return the the matching orders", async () => {
        await Promise.all([
          buildHoldingDTO(true, "equities_adobe", 1),
          buildDividendTransaction({
            owner: user.id,
            asset: "equities_adobe",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Cancelled"
              }
            }
          }),
          buildDividendTransaction({
            owner: user.id,
            asset: "equities_microsoft",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Cancelled"
              }
            }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_adobe");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "dividend",
            item: expect.objectContaining({
              asset: "equities_adobe",
              owner: user.id
            })
          })
        );
      });
    });

    describe("when user has a settled dividend", () => {
      it("should not return the matching orders", async () => {
        await Promise.all([
          buildHoldingDTO(true, "equities_adobe", 1),
          buildDividendTransaction({
            owner: user.id,
            asset: "equities_adobe",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }),
          buildDividendTransaction({
            owner: user.id,
            asset: "equities_microsoft",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_adobe");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "dividend",
            item: expect.objectContaining({
              asset: "equities_adobe",
              owner: user.id
            })
          })
        );
      });
    });

    describe("when user has a not accepted reward", () => {
      it("should not return the matching orders", async () => {
        await Promise.all([
          buildReward({ targetUser: user.id, asset: "commodities_gold", accepted: false }),
          buildReward({ targetUser: user.id, asset: "corporate_bonds_eu", accepted: false })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "commodities_gold");

        expect(receivedOrders.length).toBe(0);
      });
    });

    describe("when user has a settled reward", () => {
      it("should return the matching orders", async () => {
        await Promise.all([
          buildReward({ targetUser: user.id, asset: "corporate_bonds_eu", status: "Settled", accepted: true }),
          buildReward({ targetUser: user.id, asset: "commodities_gold", status: "Settled", accepted: true })
        ]);

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "commodities_gold");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "commodities_gold",
              targetUser: expect.objectContaining({ id: user.id }),
              isCancellable: false,
              accepted: true
            })
          })
        );
      });
    });

    describe("when user has a pending single-asset transaction and the order is not submitted", () => {
      it("should return the matching οrder and it should be cancellable", async () => {
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "update",
          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
        ]);

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: order.isin,
              _id: order.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              }),
              isCancellable: true
            })
          })
        );
      });
    });

    describe("when user has a real time single-asset stock transaction and the order is not submitted but we are in market hours", () => {
      beforeEach(() => {
        Date.now = jest.fn(() => DATE_INSIDE_MARKET_HOURS.valueOf());
      });

      it("should return the matching οrder and it should not be cancellable", async () => {
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "update",
          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
        ]);

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin,
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
        });
        await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin,
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: order.isin,
              _id: order.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              }),
              isCancellable: false
            })
          })
        );
      });
    });

    describe("when user has a real time single-asset stock transaction and the order is not submitted but we are outside market hours", () => {
      beforeEach(() => {
        Date.now = jest.fn(() => DATE_OUTSIDE_MARKET_HOURS.valueOf());
      });

      it("should return the matching οrder and it should be cancellable", async () => {
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "update",
          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
        ]);

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: order.isin,
              _id: order.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              }),
              isCancellable: true
            })
          })
        );
      });
    });

    describe("when user has a pending single-asset transaction and the order is submitted", () => {
      it("should return the matching οrder and it should NOT be cancellable", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
        ]);

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "update",

          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } }
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();

        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: order.isin,
              _id: order.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              }),
              isCancellable: false
            })
          })
        );
      });
    });

    describe("when user has a cancelled asset transaction", () => {
      it("should return the matching οrder", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
        ]);
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "buy",
          executionWindow: { start: FUTURE_DATE },
          status: "Cancelled"
        });

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        const anotherOrder = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin
        });
        assetTransaction.orders = [order, anotherOrder];
        await assetTransaction.save();
        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: order.isin,
              _id: order.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              })
            })
          })
        );
      });
    });

    describe("when user has a rejected asset transaction", () => {
      it("should return the matching οrder", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
        ]);
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "buy",
          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        const anotherOrder = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin
        });
        assetTransaction.orders = [order, anotherOrder];
        await assetTransaction.save();
        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(1);
        expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: order.isin,
              _id: order.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              })
            })
          })
        );
      });
    });

    describe("when user has a DepositFailed asset transaction", () => {
      it("should not return the matching order transaction", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
        ]);
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "buy",
          executionWindow: { start: FUTURE_DATE },
          status: "DepositFailed"
        });

        const order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        const anotherOrder = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin
        });
        assetTransaction.orders = [order, anotherOrder];
        await assetTransaction.save();
        const receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");

        expect(receivedOrders.length).toBe(0);
      });
    });

    describe("when user has a PendingDeposit asset buy transaction", () => {
      describe("and the deposit has executed status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let order: OrderDocument;
        let receivedOrders: AssetRecentActivityItemType[];
        let user: UserDocument;

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  version: "v3",
                  id: faker.string.uuid(),
                  status: "executed"
                }
              },
              bankAccount: user.bankAccounts[0].id
            },
            user
          );

          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            createdAt: PAST_DATE,
            portfolioTransactionCategory: "update",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            executionWindow: { start: FUTURE_DATE },
            status: "PendingDeposit"
          });
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
          ]);

          order = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_airbnb"]?.isin
          });
          const anotherOrder = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_paypal"]?.isin
          });
          assetTransaction.orders = [order, anotherOrder];
          await assetTransaction.save();

          receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");
        });

        it("should return the matching order and it should be cancellable", async () => {
          expect(receivedOrders.length).toBe(1);
          expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
            expect.objectContaining({
              type: "order",
              item: expect.objectContaining({
                isin: order.isin,
                _id: order.id,
                transaction: expect.objectContaining({
                  _id: assetTransaction.id,
                  owner: user.id
                }),
                isCancellable: true
              })
            })
          );
        });
      });

      describe("and the deposit has authorized status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let order: OrderDocument;
        let receivedOrders: AssetRecentActivityItemType[];
        let user: UserDocument;

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  version: "v3",
                  id: faker.string.uuid(),
                  status: "authorized"
                }
              },
              bankAccount: user.bankAccounts[0].id
            },
            user
          );

          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            createdAt: PAST_DATE,
            portfolioTransactionCategory: "update",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            executionWindow: { start: FUTURE_DATE },
            status: "PendingDeposit"
          });
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
          ]);

          order = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_airbnb"]?.isin
          });
          const anotherOrder = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_paypal"]?.isin
          });
          assetTransaction.orders = [order, anotherOrder];
          await assetTransaction.save();

          receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");
        });

        it("should return the matching order and it should be cancellable", async () => {
          expect(receivedOrders.length).toBe(1);
          expect(JSON.parse(JSON.stringify(receivedOrders[0]))).toEqual(
            expect.objectContaining({
              type: "order",
              item: expect.objectContaining({
                isin: order.isin,
                _id: order.id,
                transaction: expect.objectContaining({
                  _id: assetTransaction.id,
                  owner: user.id
                }),
                isCancellable: true
              })
            })
          );
        });
      });

      describe("and the deposit has authorizing status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let order: OrderDocument;
        let receivedOrders: AssetRecentActivityItemType[];
        let user: UserDocument;

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  version: "v3",
                  id: faker.string.uuid(),
                  status: "authorizing"
                }
              },
              bankAccount: user.bankAccounts[0].id
            },
            user
          );

          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            createdAt: PAST_DATE,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            executionWindow: { start: FUTURE_DATE },
            status: "PendingDeposit"
          });

          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
          ]);
          order = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_airbnb"]?.isin
          });
          const anotherOrder = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_paypal"]?.isin
          });
          assetTransaction.orders = [order, anotherOrder];

          await assetTransaction.save();

          receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");
        });

        it("should not return the matching order", async () => {
          expect(receivedOrders.length).toBe(0);
        });
      });

      describe("and the deposit has no status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let order: OrderDocument;
        let receivedOrders: AssetRecentActivityItemType[];
        let user: UserDocument;

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );

          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            createdAt: PAST_DATE,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            executionWindow: { start: FUTURE_DATE },
            status: "PendingDeposit"
          });
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
          ]);
          order = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_airbnb"]?.isin
          });
          const anotherOrder = await buildOrder({
            side: "Buy",
            transaction: assetTransaction._id,
            isin: ASSET_CONFIG["equities_paypal"]?.isin
          });
          assetTransaction.orders = [order, anotherOrder];
          await assetTransaction.save();

          receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");
        });

        it("should not return the matching order", async () => {
          expect(receivedOrders.length).toBe(0);
        });
      });
    });

    describe("when user has past orders with active & deprecated ISIN", () => {
      let recentActivity: AssetRecentActivityItemType[];
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock_deprecated_1";

      beforeEach(async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: ACTIVE_ASSET_ID }),
          buildInvestmentProduct(true, { assetId: DEPRECATED_ASSET_ID })
        ]);

        const assetTransactionActiveIsin = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "update",

          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });
        const orderActiveIsin = await buildOrder({
          side: "Buy",
          transaction: assetTransactionActiveIsin._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          isin: ASSET_CONFIG[ACTIVE_ASSET_ID]?.isin
        });
        assetTransactionActiveIsin.orders = [orderActiveIsin];
        await assetTransactionActiveIsin.save();

        const assetTransactionDeprecatedIsin = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "update",

          executionWindow: { start: FUTURE_DATE },
          status: "Pending"
        });
        const orderDeprecatedIsin = await buildOrder({
          side: "Buy",
          transaction: assetTransactionDeprecatedIsin._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID]?.isin
        });
        assetTransactionDeprecatedIsin.orders = [orderDeprecatedIsin];
        await assetTransactionDeprecatedIsin.save();

        recentActivity = await InvestmentProductService.getAssetRecentActivity(user.id, ACTIVE_ASSET_ID);
      });

      it("should return the orders for both the active and the deprecated isin", async () => {
        expect(recentActivity.length).toBe(2);
        expect(recentActivity.map((order) => (order.item as OrderDocument).isin)).toEqual(
          expect.arrayContaining([ASSET_CONFIG[ACTIVE_ASSET_ID].isin, ASSET_CONFIG[DEPRECATED_ASSET_ID].isin])
        );
      });
    });

    describe("when user has a Completed deposit but a PendingDeposit repeating Asset Transaction", () => {
      let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;
      let order: OrderDocument;
      let receivedOrders: AssetRecentActivityItemType[];
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser();
        await buildSubscription({ owner: user.id });
        await buildBankAccount({ owner: user.id });
        await user.populate("bankAccounts");
        portfolio = await buildPortfolio({ owner: user.id });

        // Create deposit with collecting status
        pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
            status: "Pending",
            directDebit: {
              activeProviders: [ProviderEnum.GOCARDLESS],
              providers: {
                gocardless: {
                  id: faker.string.uuid(),
                  status: "paid_out"
                },
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Completed"
                }
              }
            },
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        // Create repeating asset transaction
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          createdAt: PAST_DATE,
          portfolioTransactionCategory: "buy",
          pendingDeposit: pendingDepositLinkedToAssetTransaction,
          executionWindow: { start: FUTURE_DATE },
          status: "PendingDeposit",
          linkedAutomation: await buildTopUpAutomation() // Makes it repeating
        });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true })
        ]);

        order = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin
        });
        const anotherOrder = await buildOrder({
          side: "Buy",
          transaction: assetTransaction._id,
          isin: ASSET_CONFIG["equities_paypal"]?.isin
        });
        assetTransaction.orders = [order, anotherOrder];
        await assetTransaction.save();

        receivedOrders = await InvestmentProductService.getAssetRecentActivity(user.id, "equities_airbnb");
      });

      it("should not return the matching order", async () => {
        expect(receivedOrders.length).toBe(0);
      });
    });
  });

  describe("getInvestmentProductsDict", () => {
    describe("when there is an investment product not in our universe", () => {
      beforeEach(async () => {
        jest.clearAllMocks();
        await clearDb();

        // We build an investment product for an asset that is not in our universe (YET!)
        await buildInvestmentProduct(false, { assetId: "equities_wealthyhood" });
      });

      it("should not throw & not include investment product in result dictionary", async () => {
        const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", false);

        expect(Object.values(investmentProducts).length === 0);
      });
    });

    describe("when there are investment products in our universe and dict key is 'id'", () => {
      let investmentProductA: InvestmentProductDocument;
      let investmentProductB: InvestmentProductDocument;

      beforeEach(async () => {
        await clearDb();

        investmentProductA = await buildInvestmentProduct(false, { assetId: "equities_apple" });
        investmentProductB = await buildInvestmentProduct(false, { assetId: "equities_jp" });
      });

      it("should return an investment product dictionary by id", async () => {
        const investmentProductById = await InvestmentProductService.getInvestmentProductsDict("id", false);
        expect(investmentProductById).toMatchObject({
          [investmentProductA.id]: expect.objectContaining({
            commonId: investmentProductA.commonId
          }),
          [investmentProductB.id]: expect.objectContaining({
            commonId: investmentProductB.commonId
          })
        });
      });
    });

    describe("when there is an investment product in our universe that is not listed and only listed products are requested", () => {
      let investmentProductA: InvestmentProductDocument;

      beforeEach(async () => {
        await clearDb();

        investmentProductA = await buildInvestmentProduct(false, { assetId: "equities_apple", listed: true });
        await buildInvestmentProduct(false, { assetId: "equities_jp", listed: false });
      });

      it("should return an investment product dictionary by id only for active investment products", async () => {
        const investmentProductById = await InvestmentProductService.getInvestmentProductsDict("id", false, {
          listedOnly: true
        });
        expect(Object.values(investmentProductById).length).toBe(1);
        expect(investmentProductById).toMatchObject({
          [investmentProductA.id]: expect.objectContaining({
            commonId: investmentProductA.commonId
          })
        });
      });
    });

    describe("when there is an investment product in our universe that is not listed and both listed and unlisted products are requested", () => {
      let investmentProductA: InvestmentProductDocument;
      let investmentProductB: InvestmentProductDocument;

      beforeEach(async () => {
        await clearDb();

        investmentProductA = await buildInvestmentProduct(false, { assetId: "equities_apple", listed: true });
        investmentProductB = await buildInvestmentProduct(false, { assetId: "equities_jp", listed: false });
      });

      it("should return an investment product dictionary by id for both listed and unlisted investment products", async () => {
        const investmentProductById = await InvestmentProductService.getInvestmentProductsDict("id", false, {
          listedOnly: false
        });

        expect(Object.values(investmentProductById).length).toBe(2);
        expect(investmentProductById).toMatchObject({
          [investmentProductA.id]: expect.objectContaining({
            commonId: investmentProductA.commonId
          }),
          [investmentProductB.id]: expect.objectContaining({
            commonId: investmentProductB.commonId
          })
        });
      });
    });
  });

  describe("isAssetCurrentlyTraded", () => {
    it("should return false if the asset is deprecated", () => {
      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_blackrock_deprecated_1")).toBe(false);
    });

    it("should return false if current time is on weekend", () => {
      // Weekend: 9:30 New York & 14:30 London
      const weekendTime = DateTime.utc(2024, 6, 30)
        .setZone(MARKET_TRADING_HOURS.US.timeZone, { keepLocalTime: true })
        .set({
          hour: 9,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => weekendTime);

      expect(InvestmentProductService.isAssetCurrentlyTraded("commodities_gold")).toBe(false);
      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_apple")).toBe(false);
    });

    // US
    it("should return false if the asset is US-traded and the current time is outside New York market hours", () => {
      // US Closed Market Hours: 2:30 New York & 7:30 London
      const closedMarketHours = DateTime.utc(2024, 7, 3)
        .setZone(MARKET_TRADING_HOURS.US.timeZone, { keepLocalTime: true })
        .set({
          hour: 2,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => closedMarketHours);

      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_apple")).toBe(false);
    });

    it("should return false if the asset is US-traded and the current time is within New York market hours but is a US bank holiday", () => {
      // US Bank Holiday: 9:30 New York & 14:30 London
      const usBankHoliday = DateTime.utc(2021, 1, 18)
        .setZone(MARKET_TRADING_HOURS.US.timeZone, { keepLocalTime: true })
        .set({
          hour: 9,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => usBankHoliday);

      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_apple")).toBe(false);
    });

    it("should return true if the asset is US-traded and the current time is within New York market hours on a US workday", () => {
      // US workday: 9:30 New York & 14:30 London
      const usWorkday = DateTime.utc(2024, 7, 3)
        .setZone(MARKET_TRADING_HOURS.US.timeZone, { keepLocalTime: true })
        .set({
          hour: 9,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => usWorkday);

      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_apple")).toBe(true);
    });

    // LSE
    it("should return false if the asset is LSE-traded and the current time is outside London market hours", () => {
      // UK Closed Market Hours: 2:30 New York & 7:30 London
      const closedMarketHours = DateTime.utc(2024, 7, 3)
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone, { keepLocalTime: true })
        .set({
          hour: 7,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => closedMarketHours);

      expect(InvestmentProductService.isAssetCurrentlyTraded("commodities_gold")).toBe(false);
    });

    it("should return false if the asset is LSE-traded and the current time is within London market hours but is a UK bank holiday", () => {
      // UK Bank Holiday: 9:30 New York & 14:30 London
      const ukBankHoliday = DateTime.utc(2024, 5, 6)
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone, { keepLocalTime: true })
        .set({
          hour: 14,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => ukBankHoliday);

      expect(InvestmentProductService.isAssetCurrentlyTraded("commodities_gold")).toBe(false);
    });

    it("should return true if the asset is LSE-traded and the current time is within London market hours on a UK workday", () => {
      // UK Bank Holiday: 9:30 New York & 14:30 London
      const ukWorkday = DateTime.utc(2024, 7, 3)
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone, { keepLocalTime: true })
        .set({
          hour: 14,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => ukWorkday);

      expect(InvestmentProductService.isAssetCurrentlyTraded("commodities_gold")).toBe(true);
    });

    // XETRA
    it("should return false if the asset is XETRA-traded and the current time is outside Berlin market hours", () => {
      // XETRA Closed Market Hours: 00:30 New York & 5:30 London & 6:30 Berlin
      const closedMarketHours = DateTime.utc(2024, 7, 3)
        .setZone(MARKET_TRADING_HOURS.XETRA.timeZone, { keepLocalTime: true })
        .set({
          hour: 6,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => closedMarketHours);

      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_global_health_care_broad")).toBe(false);
    });

    it("should return true if the asset is XETRA-traded and the current time is within Berlin market hours on a Germany workday", () => {
      // XETRA Workday: 3:30 New York & 8:30 London & 9:30 Berlin
      const deWorkday = DateTime.utc(2024, 7, 3)
        .setZone(MARKET_TRADING_HOURS.XETRA.timeZone, { keepLocalTime: true })
        .set({
          hour: 9,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => deWorkday);

      expect(InvestmentProductService.isAssetCurrentlyTraded("equities_global_health_care_broad")).toBe(true);
    });
  });
});
