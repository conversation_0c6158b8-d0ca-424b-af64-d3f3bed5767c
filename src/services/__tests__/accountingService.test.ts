import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../tests/utils/db";
import {
  buildDepositCashTransaction,
  buildCreditTicket,
  buildUser,
  buildPortfolio,
  buildAssetTransaction,
  buildOrder,
  buildDividendTransaction,
  buildSavingsDividend,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildWithdrawalCashTransaction,
  buildReward,
  buildGift
} from "../../tests/utils/generateModels";
import { AccountingService } from "../accountingService";
import AccountingLedgerStorageService from "../../external-services/accountingLedgerStorageService";
import { TransactionDocument } from "../../models/Transaction";
import { DepositMethodEnum, TransferWithIntermediaryStageEnum } from "../../types/transactions";
import { LedgerAccounts } from "../../types/accounting";
import { UserDocument } from "../../models/User";
import { PortfolioDocument } from "../../models/Portfolio";
import { ProviderEnum } from "../../configs/providersConfig";
import { InvoiceReferenceNumber } from "../../models/InvoiceReferenceNumber";
import { AccountingRecordIndex } from "../../models/AccountingRecordIndex";

/**
 * The following scenarios are covered:
 *
 * 1. Deposit (Instant Bank Transfer)
 * 2. Asset Buy
 * 3. Stock Dividend Receipt
 * 4. MMF Dividend Receipt
 * 5. Reinvest MMF Dividend (Buy)
 * 6. Stock Sell
 * 7. MMF Sell
 * 8. Withdrawal (Two-Step)
 */

describe("AccountingService", () => {
  const TODAY = "2025-06-12";
  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));

    await connectDb("AccountingServiceDepositTest");
    await createSqliteDb();
  });
  afterAll(async () => await closeDb());
  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  describe("generateAccountingEntriesOnTransactionUpdate", () => {
    /* ------------------------------------------------------------------
     * Deposit (Instant Bank Transfer)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for the three deposit stages for instant bank-transfer", async () => {
      const DEPOSIT_AMOUNT_CENTS = 11000; // £110.00 in cents
      const DEPOSIT_AMOUNT_EUROS = 110; // £110.00 in euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const creditTicket = await buildCreditTicket({
        status: "Credited",
        owner: user.id,
        portfolio: portfolio.id
      });

      const deposit = await buildDepositCashTransaction(
        {
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
          linkedCreditTicket: creditTicket.id,
          portfolio: portfolio.id
        },
        user,
        portfolio
      );
      await deposit.populate("linkedCreditTicket");

      // Stage 1
      const oldDeposit1: TransactionDocument = deposit.toObject();
      deposit.set(
        {
          transferWithIntermediary: {
            [TransferWithIntermediaryStageEnum.ACQUISITION]: {
              incomingPayment: {
                providers: {
                  devengo: {
                    id: "in-1",
                    status: "confirmed",
                    accountId: "acc-1"
                  }
                }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(deposit, oldDeposit1);

      // Stage 2
      const oldDeposit2: TransactionDocument = deposit.toObject();
      const twSnapshot = deposit.transferWithIntermediary
        ? JSON.parse(JSON.stringify(deposit.transferWithIntermediary))
        : {};
      deposit.set(
        {
          transferWithIntermediary: {
            ...twSnapshot,
            [TransferWithIntermediaryStageEnum.COLLECTION]: {
              outgoingPayment: {
                providers: { devengo: { id: "out-1", status: "confirmed" } }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(deposit, oldDeposit2);

      // Stage 3
      const oldDeposit3: TransactionDocument = deposit.toObject();
      deposit.set(
        {
          providers: { wealthkernel: { id: "wk-txn-1", status: "Settled" } },
          status: "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(deposit, oldDeposit3);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6);

      // Validate that accounting documents were created correctly for transactions
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0); // Deposits don't generate revenue entries

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(3); // One for each deposit stage (movements only)
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(deposit.id);
        expect(record.sourceDocumentType).toBe("Transaction");
      });

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, side: "debit", aa: 1 }, // id 1
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1 }, // id 2
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, side: "debit", aa: 2 }, // id 3
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, side: "credit", aa: 2 }, // id 4
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 3 }, // id 5
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, side: "credit", aa: 3 } // id 6
      ];

      const description = `${user.id}|${deposit.id}|bank transaction (deposit)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: DEPOSIT_AMOUNT_EUROS,
            reference_number: null,
            article_date: TODAY,
            description,
            document_id: deposit.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Direct Debit Deposit (Two-Stage Flow)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for the two direct debit stages", async () => {
      const DEPOSIT_AMOUNT_CENTS = 25000; // £250.00 in cents
      const DEPOSIT_AMOUNT_EUROS = 250; // £250.00 in euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const directDebitDeposit = await buildDepositCashTransaction(
        {
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
          portfolio: portfolio.id,
          status: "Pending"
        },
        user,
        portfolio
      );

      // Stage 1: Devengo collection confirmed (transition from no status to confirmed)
      const oldDeposit1: TransactionDocument = directDebitDeposit.toObject();
      directDebitDeposit.set(
        {
          transferWithIntermediary: {
            collection: {
              incomingPayment: {
                providers: {
                  devengo: {
                    id: "devengo-dd-1",
                    status: "confirmed",
                    accountId: "acc-devengo"
                  }
                }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(directDebitDeposit, oldDeposit1);

      // Stage 2: WealthKernel settlement
      const oldDeposit2: TransactionDocument = directDebitDeposit.toObject();
      directDebitDeposit.set(
        {
          providers: { wealthkernel: { id: "wk-txn-1", status: "Settled" } },
          status: "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(directDebitDeposit, oldDeposit2);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      // Validate that accounting documents were created correctly for direct debits
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0); // Direct debits don't generate revenue entries

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // One for each direct debit stage (movements only)
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(directDebitDeposit.id);
        expect(record.sourceDocumentType).toBe("Transaction");
      });

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, side: "debit", aa: 1 }, // Stage 1: Collection
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1 },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 2 }, // Stage 2: Settlement
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, side: "credit", aa: 2 }
      ];

      const description = `${user.id}|${directDebitDeposit.id}|bank transaction (deposit)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: DEPOSIT_AMOUNT_EUROS,
            reference_number: null,
            article_date: TODAY,
            description,
            document_id: directDebitDeposit.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Asset Buy
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for an asset buy order including commission and broker fees", async () => {
      const COMMISSION_FEE_AMOUNT_CENTS = 200; // 200 cents = £2.00
      const COMMISSION_FEE_AMOUNT_EUROS = Decimal.div(COMMISSION_FEE_AMOUNT_CENTS, 100).toNumber();
      const BROKER_FEE_AMOUNT_CENTS = 100; // 100 cents = £1.00
      const BROKER_FEE_AMOUNT_EUROS = Decimal.div(BROKER_FEE_AMOUNT_CENTS, 100).toNumber();

      // Settlement amount submitted equals net (£9,800) + broker fee £100 -> 9,900
      const SETTLEMENT_AMOUNT_CENTS = 9900; // 9900 cents = £99.00

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: assetTransaction.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: SETTLEMENT_AMOUNT_CENTS,
          amountSubmitted: SETTLEMENT_AMOUNT_CENTS,
          amount: SETTLEMENT_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: {
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          fx: { amount: 0.01, currency: "EUR" },
          realtimeExecution: { amount: COMMISSION_FEE_AMOUNT_EUROS - 0.01, currency: "EUR" }
        },
        isin: "US0378331005"
      });

      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-order-1",
              status: "Matched",
              accountingBrokerFxFee: BROKER_FEE_AMOUNT_EUROS
            }
          },
          filledAt: new Date(TODAY)
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(10);

      // Validate that accounting documents were created correctly
      const invoiceRefs = await InvoiceReferenceNumber.find({}).sort({ createdAt: 1 });
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(order.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(3); // movements, revenues, expenses
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(order.id);
        expect(record.sourceDocumentType).toBe("Order");
      });

      // Test virtual population of linkedOrder
      const populatedInvoiceRef = await InvoiceReferenceNumber.findOne({}).populate("linkedOrder");
      expect(populatedInvoiceRef?.linkedOrder).toBeDefined();
      expect(populatedInvoiceRef?.linkedOrder._id.toString()).toBe(order.id);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      // Expected amounts are converted to pounds inside AccountingService (divide by 100 twice)
      const assetLineAmount = Decimal.div(
        Decimal.sub(SETTLEMENT_AMOUNT_CENTS, BROKER_FEE_AMOUNT_CENTS),
        100
      ).toNumber();

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        // Revenues (2 entries) - aa: 2, with invoice reference
        // Total commission = WH commission + broker commission
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: Decimal.add(COMMISSION_FEE_AMOUNT_EUROS, BROKER_FEE_AMOUNT_EUROS).toNumber(),
          aa: 2,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: Decimal.add(COMMISSION_FEE_AMOUNT_EUROS, BROKER_FEE_AMOUNT_EUROS).toNumber(),
          aa: 2,
          reference_number: "1"
        },
        // Expenses (4 entries) - aa: 3, no invoice reference
        {
          account: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        }
      ];

      const description = `${user.id}|${order.id}|US0378331005|asset buy`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description,
            document_id: order.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Two-Step Withdrawal
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for a two-stage withdrawal", async () => {
      const WITHDRAW_AMOUNT_CENTS = 11300; // £113.00 in cents
      const WITHDRAW_AMOUNT_EUROS = 113; // £113.00 in euros

      // Domestic user
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Build initial withdrawal (Pending)
      const withdrawal = await buildWithdrawalCashTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: WITHDRAW_AMOUNT_CENTS, currency: "EUR" },
        status: "Pending",
        providers: { wealthkernel: { id: "wk-wd-1", status: "Pending" } }
      });

      /* Stage 1 – WealthKernel settles (client ledger → omnibus) */
      const old1: TransactionDocument = withdrawal.toObject();
      withdrawal.set(
        {
          providers: { wealthkernel: { id: "wk-wd-1", status: "Settled" } },
          status: "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(withdrawal, old1);

      /* Stage 2 – Devengo outgoing payment confirmed (omnibus → intermediary) */
      const old2: TransactionDocument = withdrawal.toObject();
      withdrawal.set(
        {
          transferWithIntermediary: {
            collection: {
              outgoingPayment: {
                providers: { devengo: { id: "out-wd-1", status: "confirmed" } }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(withdrawal, old2);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);
      const amt = WITHDRAW_AMOUNT_EUROS;

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", aa: 1 },
        { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, side: "debit", aa: 1 },
        { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, side: "credit", aa: 2 },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 2 }
      ];

      const description = `${user.id}|${withdrawal.id}|bank transaction (withdrawal)`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: amt,
            reference_number: null,
            article_date: TODAY,
            description,
            document_id: withdrawal.id,
            owner_id: user.id
          })
        );
      });
    });
  });

  describe("generateAccountingEntriesOnTransactionInsert", () => {
    /* ------------------------------------------------------------------
     * Stock Dividend Receipt
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for a stock dividend receipt", async () => {
      const DIVIDEND_AMOUNT_CENTS = 1; // 1 cent

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Build dividend transaction already settled
      const dividendTx = await buildDividendTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: DIVIDEND_AMOUNT_CENTS, currency: "EUR" },
        providers: { wealthkernel: { status: "Settled", id: "wk-div-1" } },
        settledAt: new Date(TODAY)
      });

      await AccountingService.generateAccountingEntriesOnTransactionInsert(dividendTx as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(2);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expectedAmount = Decimal.div(DIVIDEND_AMOUNT_CENTS, 100).toNumber(); // 1 → 0.01 euros

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit" },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit" }
      ];

      const description = `${user.id}|${dividendTx.id}|asset dividend`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: 1,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expectedAmount,
            reference_number: null,
            article_date: TODAY,
            description,
            document_id: dividendTx.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * MMF Dividend Receipt
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for an MMF dividend receipt with commission fee", async () => {
      const GROSS_DIVIDEND_CENTS = 10; // 10 cents
      const GROSS_DIVIDEND_EUROS = Decimal.div(GROSS_DIVIDEND_CENTS, 100).toNumber(); // 10 → 0.1 euros
      const COMMISSION_CENTS = 1; // 1 cent
      const COMMISSION_EUROS = Decimal.div(COMMISSION_CENTS, 100).toNumber(); // 1 → 0.01 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const savingsDividendTx = await buildSavingsDividend({
        owner: user.id,
        portfolio: portfolio.id,
        originalDividendAmount: GROSS_DIVIDEND_CENTS,
        consideration: { amount: GROSS_DIVIDEND_CENTS, currency: "EUR" },
        fees: {
          commission: { amount: COMMISSION_EUROS, currency: "EUR" },
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        status: "Pending"
      });

      await AccountingService.generateAccountingEntriesOnTransactionInsert(savingsDividendTx as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", amount: GROSS_DIVIDEND_EUROS },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", amount: GROSS_DIVIDEND_EUROS },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: COMMISSION_EUROS },
        { account: LedgerAccounts.MMF_DIVIDEND_FEES_WH, side: "credit", amount: COMMISSION_EUROS }
      ];

      const description = `${user.id}|${savingsDividendTx.id}|asset dividend`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: 1,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: expected[idx].amount,
            reference_number: null,
            article_date: TODAY,
            description,
            document_id: savingsDividendTx.id,
            owner_id: user.id
          })
        );
      });
    });
  });

  describe("generateAccountingEntriesOnOrderUpdate", () => {
    /* ------------------------------------------------------------------
     * Reinvest MMF Dividend (Buy)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reinvest MMF dividend buy", async () => {
      const BUY_AMOUNT_CENTS = 300; // 300 cents = £3.00
      const BUY_AMOUNT_EUROS = Decimal.div(BUY_AMOUNT_CENTS, 100).toNumber(); // 3 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });
      const savingsTopup = await buildSavingsTopup({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: BUY_AMOUNT_CENTS,
          amountSubmitted: BUY_AMOUNT_CENTS,
          amount: BUY_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        filledAt: new Date(TODAY),
        isin: "IE00B404XK09" // MMF ISIN
      });

      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-rt-1",
              status: "Matched",
              accountingBrokerFxFee: 0 // Ensure no broker fee for reinvestment order
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6);

      // Validate that invoice documents were created (even with 0 commission)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(order.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // movements and revenues (no expenses since broker fee is 0)
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Order");
      expect(accountingRecords[1].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[1].sourceDocumentType).toBe("Order");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "credit", aa: 1, reference_number: null },
        // Revenues (2 entries) - aa: 2, with invoice reference (even though commission is 0)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 2, reference_number: "1" },
        { account: LedgerAccounts.COMMISSION_FEES_WH, side: "credit", aa: 2, reference_number: "1" }
      ];

      const description = `${user.id}|${order.id}|IE00B404XK09|asset buy`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: idx < 4 ? BUY_AMOUNT_EUROS : 0, // First 4 entries use BUY_AMOUNT_EUROS, revenues use 0
            reference_number: expected[idx].reference_number,
            article_date: TODAY,
            description,
            document_id: order.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Stock Sell
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for a stock sell", async () => {
      const SELL_AMOUNT_CENTS = 9700; // 9700 cents = £97.00
      const SELL_AMOUNT_EUROS = Decimal.div(SELL_AMOUNT_CENTS, 100).toNumber(); // 97 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetTx = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: assetTx.id,
        side: "Sell",
        status: "Pending",
        consideration: { originalAmount: SELL_AMOUNT_CENTS, amount: SELL_AMOUNT_CENTS, currency: "EUR" },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        filledAt: new Date(TODAY),
        isin: "US0378331005"
      });

      const oldOrder = order.toObject();
      order.set(
        { status: "Matched", providers: { wealthkernel: { id: "wk-sell-1", status: "Matched" } } },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6);

      // Validate that invoice documents were created (even with 0 commission)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(order.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // movements and revenues (no expenses since broker fee is 0)
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Order");
      expect(accountingRecords[1].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[1].sourceDocumentType).toBe("Order");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "credit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1, reference_number: null },
        // Revenues (2 entries) - aa: 2, with invoice reference (even though commission is 0)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 2, reference_number: "1" },
        { account: LedgerAccounts.COMMISSION_FEES_WH, side: "credit", aa: 2, reference_number: "1" }
      ];

      const description = `${user.id}|${order.id}|US0378331005|asset sell`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: idx < 4 ? SELL_AMOUNT_EUROS : 0, // First 4 entries use SELL_AMOUNT_EUROS, revenues use 0
            reference_number: expected[idx].reference_number,
            article_date: TODAY,
            description,
            document_id: order.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * MMF Sell
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for an MMF sell", async () => {
      const SELL_AMOUNT_CENTS = 1300; // 1300 cents = £13.00
      const SELL_AMOUNT_EUROS = Decimal.div(SELL_AMOUNT_CENTS, 100).toNumber(); // 13 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetTx = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: assetTx.id,
        side: "Sell",
        status: "Pending",
        consideration: { originalAmount: SELL_AMOUNT_CENTS, amount: SELL_AMOUNT_CENTS, currency: "EUR" },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        filledAt: new Date(TODAY),
        isin: "IE00B404XK09"
      });

      const oldOrder = order.toObject();
      order.set(
        { status: "Matched", providers: { wealthkernel: { id: "wk-sell-2", status: "Matched" } } },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6);

      // Validate that invoice documents were created (even with 0 commission)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(order.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // movements and revenues (no expenses since broker fee is 0)
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Order");
      expect(accountingRecords[1].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[1].sourceDocumentType).toBe("Order");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "credit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1, reference_number: null },
        // Revenues (2 entries) - aa: 2, with invoice reference (even though commission is 0)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 2, reference_number: "1" },
        { account: LedgerAccounts.COMMISSION_FEES_WH, side: "credit", aa: 2, reference_number: "1" }
      ];

      const description = `${user.id}|${order.id}|IE00B404XK09|asset sell`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: idx < 4 ? SELL_AMOUNT_EUROS : 0, // First 4 entries use SELL_AMOUNT_EUROS, revenues use 0
            reference_number: expected[idx].reference_number,
            article_date: TODAY,
            description,
            document_id: order.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * MMF Buy matches MMF Sell completely (no net movement)
     * ------------------------------------------------------------------ */
    it("should generate no ledger entries when MMF buy matches MMF sell completely", async () => {
      const ORDER_AMOUNT_CENTS = 50000; // £500.00 in cents

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create topup and withdrawal transactions
      const savingsTopup = await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
      });
      const savingsWithdrawal = await buildSavingsWithdrawal({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
      });

      // Create internally filled orders (buy and sell that match completely)
      const buyOrder = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      const sellOrder = await buildOrder({
        transaction: savingsWithdrawal.id,
        side: "Sell",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Simulate order updates (from pending to internally filled)
      const oldBuyOrder = { ...buyOrder.toObject(), status: "Pending" };
      const oldSellOrder = { ...sellOrder.toObject(), status: "Pending" };

      await AccountingService.generateAccountingEntriesOnOrderUpdate(buyOrder, oldBuyOrder as any);
      await AccountingService.generateAccountingEntriesOnOrderUpdate(sellOrder, oldSellOrder as any);

      // Verify no ledger entries were created
      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(0);

      // Verify no accounting records were created
      const accountingRecords = await AccountingRecordIndex.find({});
      expect(accountingRecords.length).toBe(0);

      // Verify no invoice references were created
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0);
    });

    /* ------------------------------------------------------------------
     * Savings topup larger than withdrawal (net buy with partial internal fill)
     * ------------------------------------------------------------------ */
    it("should generate ledger entries only for the net buy amount when topup is larger than withdrawal", async () => {
      const WITHDRAWAL_AMOUNT_CENTS = 30000; // £300.00 in cents
      const TOPUP_AMOUNT_CENTS = 50000; // £500.00 in cents
      const NET_BUY_AMOUNT_CENTS = Decimal.sub(TOPUP_AMOUNT_CENTS, WITHDRAWAL_AMOUNT_CENTS).toNumber(); // £200.00 in cents
      const NET_BUY_AMOUNT_EUROS = Decimal.div(NET_BUY_AMOUNT_CENTS, 100).toNumber(); // 200 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create topup and withdrawal transactions
      const savingsTopup = await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: TOPUP_AMOUNT_CENTS, currency: "EUR" }
      });
      const savingsWithdrawal = await buildSavingsWithdrawal({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: WITHDRAWAL_AMOUNT_CENTS, currency: "EUR" }
      });

      // Create internally filled orders (partial amounts)
      const internallyFilledBuyOrder = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: WITHDRAWAL_AMOUNT_CENTS,
          amountSubmitted: WITHDRAWAL_AMOUNT_CENTS,
          amount: WITHDRAWAL_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      const internallyFilledSellOrder = await buildOrder({
        transaction: savingsWithdrawal.id,
        side: "Sell",
        status: "InternallyFilled",
        consideration: {
          originalAmount: WITHDRAWAL_AMOUNT_CENTS,
          amountSubmitted: WITHDRAWAL_AMOUNT_CENTS,
          amount: WITHDRAWAL_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Create external order for the net buy amount
      const externalBuyOrder = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: NET_BUY_AMOUNT_CENTS,
          amountSubmitted: NET_BUY_AMOUNT_CENTS,
          amount: NET_BUY_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Simulate internally filled orders (should generate no entries)
      const oldInternalBuyOrder = { ...internallyFilledBuyOrder.toObject(), status: "Pending" };
      const oldInternalSellOrder = { ...internallyFilledSellOrder.toObject(), status: "Pending" };

      await AccountingService.generateAccountingEntriesOnOrderUpdate(
        internallyFilledBuyOrder,
        oldInternalBuyOrder as any
      );
      await AccountingService.generateAccountingEntriesOnOrderUpdate(
        internallyFilledSellOrder,
        oldInternalSellOrder as any
      );

      // Simulate external order matching (should generate entries for net amount)
      const oldExternalBuyOrder = externalBuyOrder.toObject();
      externalBuyOrder.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-external-buy-1",
              status: "Matched",
              accountingBrokerFxFee: 0
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(externalBuyOrder, oldExternalBuyOrder as any);

      // Verify entries were created only for the net buy amount
      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6); // 4 movements + 2 revenues (no expenses since broker fee is 0)

      // Verify accounting records were created correctly
      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // One for movements, one for revenues
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(externalBuyOrder.id);
        expect(record.sourceDocumentType).toBe("Order");
      });

      // Verify invoice reference was created for revenue entries
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(externalBuyOrder.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");

      // Verify all entries are for the net buy amount
      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);
      const expectedEntries = [
        // Movement entries (aa: 1, no reference_number)
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: NET_BUY_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: NET_BUY_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: NET_BUY_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: NET_BUY_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Revenue entries (aa: 2, with reference_number)
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: 0,
          aa: 2,
          reference_number: invoiceRefs[0].invoiceId.toString()
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0,
          aa: 2,
          reference_number: invoiceRefs[0].invoiceId.toString()
        }
      ];

      expectedEntries.forEach((expected, index) => {
        expect(ordered[index]).toEqual(
          expect.objectContaining({
            account_code: expected.account,
            side: expected.side,
            amount: expected.amount,
            aa: expected.aa,
            reference_number: expected.reference_number,
            document_id: externalBuyOrder.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Savings withdrawal larger than topup (net sell with partial internal fill)
     * ------------------------------------------------------------------ */
    it("should generate ledger entries only for the net sell amount when withdrawal is larger than topup", async () => {
      const TOPUP_AMOUNT_CENTS = 25000; // £250.00 in cents
      const WITHDRAWAL_AMOUNT_CENTS = 40000; // £400.00 in cents
      const NET_SELL_AMOUNT_CENTS = WITHDRAWAL_AMOUNT_CENTS - TOPUP_AMOUNT_CENTS; // £150.00 in cents
      const NET_SELL_AMOUNT_EUROS = Decimal.div(NET_SELL_AMOUNT_CENTS, 100).toNumber(); // 150 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create topup and withdrawal transactions
      const savingsTopup = await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: TOPUP_AMOUNT_CENTS, currency: "EUR" }
      });
      const savingsWithdrawal = await buildSavingsWithdrawal({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: WITHDRAWAL_AMOUNT_CENTS, currency: "EUR" }
      });

      // Create internally filled orders (partial amounts)
      const internallyFilledBuyOrder = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: TOPUP_AMOUNT_CENTS,
          amountSubmitted: TOPUP_AMOUNT_CENTS,
          amount: TOPUP_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      const internallyFilledSellOrder = await buildOrder({
        transaction: savingsWithdrawal.id,
        side: "Sell",
        status: "InternallyFilled",
        consideration: {
          originalAmount: TOPUP_AMOUNT_CENTS,
          amountSubmitted: TOPUP_AMOUNT_CENTS,
          amount: TOPUP_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Create external order for the net sell amount
      const externalSellOrder = await buildOrder({
        transaction: savingsWithdrawal.id,
        side: "Sell",
        status: "Pending",
        consideration: {
          originalAmount: NET_SELL_AMOUNT_CENTS,
          amountSubmitted: NET_SELL_AMOUNT_CENTS,
          amount: NET_SELL_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Simulate internally filled orders (should generate no entries)
      const oldInternalBuyOrder = { ...internallyFilledBuyOrder.toObject(), status: "Pending" };
      const oldInternalSellOrder = { ...internallyFilledSellOrder.toObject(), status: "Pending" };

      await AccountingService.generateAccountingEntriesOnOrderUpdate(
        internallyFilledBuyOrder,
        oldInternalBuyOrder as any
      );
      await AccountingService.generateAccountingEntriesOnOrderUpdate(
        internallyFilledSellOrder,
        oldInternalSellOrder as any
      );

      // Simulate external order matching (should generate entries for net amount)
      const oldExternalSellOrder = externalSellOrder.toObject();
      externalSellOrder.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-external-sell-1",
              status: "Matched",
              accountingBrokerFxFee: 0
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(
        externalSellOrder,
        oldExternalSellOrder as any
      );

      // Verify entries were created only for the net sell amount
      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6); // 4 movements + 2 revenues (no expenses since broker fee is 0)

      // Verify accounting records were created correctly
      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // One for movements, one for revenues
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(externalSellOrder.id);
        expect(record.sourceDocumentType).toBe("Order");
      });

      // Verify invoice reference was created for revenue entries
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(externalSellOrder.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");

      // Verify all entries are for the net sell amount
      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);
      const expectedEntries = [
        // Movement entries (aa: 1, no reference_number)
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "debit",
          amount: NET_SELL_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "credit",
          amount: NET_SELL_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: NET_SELL_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: NET_SELL_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Revenue entries (aa: 2, with reference_number)
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: 0,
          aa: 2,
          reference_number: invoiceRefs[0].invoiceId.toString()
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0,
          aa: 2,
          reference_number: invoiceRefs[0].invoiceId.toString()
        }
      ];

      expectedEntries.forEach((expected, index) => {
        expect(ordered[index]).toEqual(
          expect.objectContaining({
            account_code: expected.account,
            side: expected.side,
            amount: expected.amount,
            aa: expected.aa,
            reference_number: expected.reference_number,
            document_id: externalSellOrder.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Asset Transaction with Remainder
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for asset buy order with remainder", async () => {
      const ORDER_AMOUNT_SUBMITTED_CENTS = 100_000; // €1,000.00 submitted amount
      const ORDER_AMOUNT_SETTLED_CENTS = 98_500; // €985.00 settled amount
      const REMAINDER_AMOUNT_CENTS = ORDER_AMOUNT_SUBMITTED_CENTS - ORDER_AMOUNT_SETTLED_CENTS; // €15.00 remainder

      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_SETTLED_CENTS).div(100).toNumber();
      const REMAINDER_AMOUNT_EUROS = new Decimal(REMAINDER_AMOUNT_CENTS).div(100).toNumber();

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create asset transaction
      const assetTransaction = await buildAssetTransaction({
        consideration: { amount: ORDER_AMOUNT_SUBMITTED_CENTS, currency: "EUR" },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled"
      });

      // Create order with remainder (amountSubmitted > amount due to fractional shares)
      const order = await buildOrder({
        transaction: assetTransaction.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: ORDER_AMOUNT_SUBMITTED_CENTS,
          amountSubmitted: ORDER_AMOUNT_SUBMITTED_CENTS,
          amount: ORDER_AMOUNT_SETTLED_CENTS, // Less than submitted due to fractional shares
          currency: "EUR"
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        filledAt: new Date(TODAY),
        isin: "US0378331005" // Stock ISIN
      });

      // Link order to transaction (AssetTransaction model allows orders to be set)
      await assetTransaction.updateOne({ orders: [order.id] });

      // Transition order from pending to matched (triggering remainder logic)
      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-asset-order-1",
              status: "Matched",
              accountingBrokerFxFee: 0
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(8); // 4 normal order entries + 2 remainder entries + 2 revenue entries

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const description = `${user.id}|${order.id}|${order.isin}|asset buy`;

      // Expected entries: normal order + remainder + revenue
      const expected = [
        // Normal order movement entries (4 entries)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "debit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "credit", amount: ORDER_AMOUNT_EUROS },
        // Remainder entries (2 entries)
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", amount: REMAINDER_AMOUNT_EUROS },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", amount: REMAINDER_AMOUNT_EUROS },
        // Revenue entries (2 entries) - commission is 0 but still created
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: 0 },
        { account: LedgerAccounts.COMMISSION_FEES_WH, side: "credit", amount: 0 }
      ];

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: idx < 6 ? 1 : 2, // First 6 entries are movements (including remainders), last 2 are revenues
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: expected[idx].amount,
            reference_number: idx >= 6 ? "1" : null, // Only revenue entries have reference numbers
            article_date: TODAY,
            description,
            document_id: order.id,
            owner_id: user.id
          })
        );
      });

      // Validate that accounting documents were created correctly
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1); // One for revenue entries

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // movements (including remainders) and revenues
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(order.id);
        expect(record.sourceDocumentType).toBe("Order");
      });
    });

    /* ------------------------------------------------------------------
     * Savings Transaction (Should Skip Remainder Logic)
     * ------------------------------------------------------------------ */
    it("should not generate remainder entries for savings transactions", async () => {
      const ORDER_AMOUNT_CENTS = 100_000; // €1,000.00 order amount

      // Note: ORDER_AMOUNT_EUROS not used in this test as we're verifying no remainder logic

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create savings topup transaction
      const savingsTransaction = await buildSavingsTopup({
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        savingsProduct: "mmf_dist_eur"
      });

      // Create order for savings transaction (remainder logic should not apply)
      const order = await buildOrder({
        transaction: savingsTransaction.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        filledAt: new Date(TODAY),
        isin: "IE00B404XK09" // MMF ISIN
      });

      // Link order to transaction
      await savingsTransaction.updateOne({ orders: [order.id] });

      // Transition order from pending to matched
      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-savings-order-2",
              status: "Matched",
              accountingBrokerFxFee: 0
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6); // 4 normal order entries + 2 revenue entries (no remainder entries)

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      // Should only have normal order entries, no remainder entries
      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber();
      const expected = [
        // Normal order movement entries (4 entries)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "debit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "credit", amount: ORDER_AMOUNT_EUROS },
        // Revenue entries (2 entries) - commission is 0 but still created
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: 0 },
        { account: LedgerAccounts.COMMISSION_FEES_WH, side: "credit", amount: 0 }
      ];

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: expected[idx].amount,
            reference_number: idx >= 4 ? "1" : null // Only revenue entries have reference numbers
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Savings Transaction (Should Skip Remainder Logic Even With Different Amounts)
     * ------------------------------------------------------------------ */
    it("should not generate remainder entries for savings transactions even with different amounts", async () => {
      const ORDER_AMOUNT_CENTS = 98_500; // €985.00 order amount

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create savings topup transaction
      const savingsTransaction = await buildSavingsTopup({
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        savingsProduct: "mmf_dist_eur"
      });

      // Create order for savings transaction
      const order = await buildOrder({
        transaction: savingsTransaction.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        filledAt: new Date(TODAY),
        isin: "IE00B404XK09" // MMF ISIN
      });

      // Link order to transaction
      await savingsTransaction.updateOne({ orders: [order.id] });

      // Transition order from pending to matched
      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-savings-order-3",
              status: "Matched",
              accountingBrokerFxFee: 0
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6); // 4 normal order entries + 2 revenue entries (no remainder entries)

      // Verify no remainder entries were created (should be normal savings order entries only)
      const omnibusDebits = entries.filter(
        (entry) => entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit"
      );
      expect(omnibusDebits.length).toBe(0); // No omnibus debits for savings orders (remainder pattern)

      const clientCredits = entries.filter(
        (entry) => entry.account_code === LedgerAccounts.CLIENT_DOMESTIC && entry.side === "credit"
      );
      expect(clientCredits.length).toBe(0); // No client credits for savings orders (remainder pattern)
    });

    /* ------------------------------------------------------------------
     * Asset Transaction with No Remainder
     * ------------------------------------------------------------------ */
    it("should not write remainder entries when there is no remainder", async () => {
      const ORDER_AMOUNT_CENTS = 100_000; // €1,000.00 order amount (submitted = settled, no remainder)

      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber();

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Create asset transaction
      const assetTransaction = await buildAssetTransaction({
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled"
      });

      // Create order with no remainder (amountSubmitted = amount)
      const order = await buildOrder({
        transaction: assetTransaction.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS, // Same as submitted, no remainder
          currency: "EUR"
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        filledAt: new Date(TODAY),
        isin: "US0378331005" // Stock ISIN
      });

      // Link order to transaction
      await assetTransaction.updateOne({ orders: [order.id] });

      // Transition order from pending to matched
      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-asset-order-2",
              status: "Matched",
              accountingBrokerFxFee: 0
            }
          }
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6); // 4 normal order entries + 2 revenue entries (no remainder entries)

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const description = `${user.id}|${order.id}|${order.isin}|asset buy`;

      // Expected entries: normal order + revenue (no remainder)
      const expected = [
        // Normal order movement entries (4 entries)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "debit", amount: ORDER_AMOUNT_EUROS },
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "credit", amount: ORDER_AMOUNT_EUROS },
        // Revenue entries (2 entries) - commission is 0 but still created
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: 0 },
        { account: LedgerAccounts.COMMISSION_FEES_WH, side: "credit", amount: 0 }
      ];

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: idx < 4 ? 1 : 2, // First 4 entries are movements (AA=1), last 2 are revenues (AA=2)
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: expected[idx].amount,
            reference_number: idx >= 4 ? "1" : null, // Only revenue entries have reference numbers
            article_date: TODAY,
            description,
            document_id: order.id,
            owner_id: user.id
          })
        );
      });

      // Validate that accounting documents were created correctly
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1); // One for revenue entries

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // movements and revenues
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(order.id);
        expect(record.sourceDocumentType).toBe("Order");
      });
    });
  });

  describe("generateAccountingEntriesOnRewardUpdate", () => {
    /* ------------------------------------------------------------------
     * Reward Deposit Settlement
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reward deposit settlement", async () => {
      const REWARD_AMOUNT_CENTS = 500_000; // £5,000.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 5000 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build reward with initial state (deposit not settled)
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: REWARD_AMOUNT_CENTS, // For deposit-only test, bonusAmount = amount
          orderAmount: REWARD_AMOUNT_CENTS
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Created" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: {} }
        }
      });

      // Simulate deposit settlement
      const oldReward = reward.toObject();
      reward.set(
        {
          "deposit.providers.wealthkernel.status": "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(2);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit" },
        { account: LedgerAccounts.BONUS_EXPENSE, side: "credit" }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: 1,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: REWARD_AMOUNT_EUROS,
            reference_number: null,
            article_date: TODAY,
            description,
            document_id: reward.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Reward Order Settlement
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reward order settlement with commission and broker fees", async () => {
      const REWARD_AMOUNT_CENTS = 1_000_000; // £10,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 5.5; // £5.50 FX fee
      const BROKER_FEE_AMOUNT_EUROS = 2.5; // £2.50 broker fee
      const BROKER_FEE_AMOUNT_CENTS = Decimal.mul(BROKER_FEE_AMOUNT_EUROS, 100).toNumber(); // 250 cents
      const NET_AMOUNT_CENTS = REWARD_AMOUNT_CENTS - BROKER_FEE_AMOUNT_CENTS; // 999750 cents
      const NET_AMOUNT_EUROS = Decimal.div(NET_AMOUNT_CENTS, 100).toNumber(); // 9997.50 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Calculate bonusAmount and orderAmount
      const BONUS_AMOUNT_CENTS = Decimal.add(
        REWARD_AMOUNT_CENTS,
        Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber()
      ).toNumber(); // amount + fx fee

      // Build reward with deposit settled but order not settled
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: BONUS_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-1",
              status: "Pending",
              accountingBrokerFxFee: BROKER_FEE_AMOUNT_EUROS
            }
          }
        }
      });

      // Simulate order settlement
      const oldReward = reward.toObject();
      reward.set(
        {
          "order.providers.wealthkernel.status": "Matched",
          status: "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});

      // Now we're generating 10 balanced entries for order settlement
      expect(entries.length).toBe(10);

      // Validate that accounting documents were created correctly for rewards
      const invoiceRefs = await InvoiceReferenceNumber.find({}).sort({ createdAt: 1 });
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(reward.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Reward");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(3); // movements, revenues, expenses
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(reward.id);
        expect(record.sourceDocumentType).toBe("Reward");
      });

      // Test virtual population of linkedReward
      const populatedInvoiceRef = await InvoiceReferenceNumber.findOne({}).populate("linkedReward");
      expect(populatedInvoiceRef?.linkedReward).toBeDefined();
      expect(populatedInvoiceRef?.linkedReward._id.toString()).toBe(reward.id);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Revenues (2 entries) - aa: 2, with invoice reference
        // Total commission = FX fee + broker fee
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: Decimal.add(FX_FEE_AMOUNT_EUROS, BROKER_FEE_AMOUNT_EUROS).toNumber(),
          aa: 2,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: Decimal.add(FX_FEE_AMOUNT_EUROS, BROKER_FEE_AMOUNT_EUROS).toNumber(),
          aa: 2,
          reference_number: "1"
        },
        // Expenses (4 entries) - aa: 3, no invoice reference
        {
          account: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description,
            document_id: reward.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Reward Order Settlement (No Fees)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reward order settlement with no fees", async () => {
      const REWARD_AMOUNT_CENTS = 250_000; // £2,500.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 2500 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build reward with no fees
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: REWARD_AMOUNT_CENTS, // No fees, so bonusAmount = amount
          orderAmount: REWARD_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-1",
              status: "Pending",
              accountingBrokerFxFee: 0
            }
          }
        }
      });

      // Simulate order settlement
      const oldReward = reward.toObject();
      reward.set(
        {
          "order.providers.wealthkernel.status": "Matched",
          status: "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6);

      // Validate that invoice documents were created (even with 0 commission)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(reward.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Reward");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(2); // movements and revenues (no expenses since broker fee is 0)
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(reward.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Reward");
      expect(accountingRecords[1].linkedDocumentId.toString()).toBe(reward.id);
      expect(accountingRecords[1].sourceDocumentType).toBe("Reward");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Revenues (2 entries) - aa: 2, with invoice reference (even though commission is 0)
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: 0,
          aa: 2,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0,
          aa: 2,
          reference_number: "1"
        }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description,
            document_id: reward.id,
            owner_id: user.id
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Complete Reward Flow (Both Deposit and Order Settlement)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for complete reward flow", async () => {
      const REWARD_AMOUNT_CENTS = 750_000; // £7,500.00 in cents
      const FX_FEE_AMOUNT_EUROS = 3.75; // £3.75 FX fee
      const BROKER_FEE_AMOUNT_EUROS = 1.88; // £1.88 broker fee
      const BROKER_FEE_AMOUNT_CENTS = Decimal.mul(BROKER_FEE_AMOUNT_EUROS, 100).toNumber();
      const NET_AMOUNT_CENTS = REWARD_AMOUNT_CENTS - BROKER_FEE_AMOUNT_CENTS;
      const NET_AMOUNT_EUROS = Decimal.div(NET_AMOUNT_CENTS, 100).toNumber();

      // Calculate bonusAmount and orderAmount
      const BONUS_AMOUNT_CENTS = Decimal.add(
        REWARD_AMOUNT_CENTS,
        Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber()
      ).toNumber(); // amount + fx fee
      const BONUS_AMOUNT_EUROS = Decimal.div(BONUS_AMOUNT_CENTS, 100).toNumber();

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build initial reward (neither deposit nor order settled)
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: BONUS_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Created" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: {} }
        }
      });

      // Step 1: Simulate deposit settlement
      const oldReward1 = reward.toObject();
      reward.set(
        {
          "deposit.providers.wealthkernel.status": "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward1 as any);

      // Step 2: Simulate order settlement
      const oldReward2 = reward.toObject();

      reward.set(
        {
          "order.providers.wealthkernel.id": "wk-reward-order-1",
          "order.providers.wealthkernel.status": "Matched",
          "order.providers.wealthkernel.accountingBrokerFxFee": BROKER_FEE_AMOUNT_EUROS,
          status: "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward2 as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});

      expect(entries.length).toBe(12); // 2 from deposit + 10 from order (now properly balanced)

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Deposit settlement (AA 1) - bonus expense correctly recorded here
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: BONUS_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: BONUS_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Order settlement - Movements (AA 2) - Main asset movement (4 entries) - no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        // Order settlement - Revenues (AA 3) - Total Commission (2 entries) - with invoice reference
        // Total commission = FX fee + broker fee
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: Decimal.add(FX_FEE_AMOUNT_EUROS, BROKER_FEE_AMOUNT_EUROS).toNumber(),
          aa: 3,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: Decimal.add(FX_FEE_AMOUNT_EUROS, BROKER_FEE_AMOUNT_EUROS).toNumber(),
          aa: 3,
          reference_number: "1"
        },
        // Order settlement - Expenses (AA 4) - Broker fee (4 entries) - no invoice reference
        {
          account: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description,
            document_id: reward.id,
            owner_id: user.id
          })
        );
      });
    });
  });

  describe("generateAccountingEntriesOnGiftUpdate", () => {
    /* ------------------------------------------------------------------
     * Gift Deposit Settlement
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for gift deposit settlement", async () => {
      const GIFT_AMOUNT_CENTS = 1000_000; // £10,000.00 in cents
      const GIFT_AMOUNT_EUROS = Decimal.div(GIFT_AMOUNT_CENTS, 100).toNumber(); // 10000 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build gift with initial state (deposit not settled)
      const gift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Created",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Simulate deposit settlement
      const oldGift = gift.toObject();
      gift.set(
        {
          "deposit.providers.wealthkernel.status": "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnGiftUpdate(gift, oldGift as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(2);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit" },
        { account: LedgerAccounts.BONUS_EXPENSE, side: "credit" }
      ];

      expected.forEach((exp, i) => {
        expect(ordered[i].account_code).toBe(exp.account);
        expect(ordered[i].side).toBe(exp.side);
        expect(ordered[i].amount).toBe(GIFT_AMOUNT_EUROS);
        expect(ordered[i].reference_number).toBeNull();
        expect(ordered[i].owner_id).toBe(user.id);
      });

      // Validate that accounting documents were created correctly for gifts
      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(1);
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(gift.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Gift");

      // Validate that no invoice documents were created (gifts don't generate revenue)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0);
    });

    /* ------------------------------------------------------------------
     * Gift Deposit Already Settled (No Change)
     * ------------------------------------------------------------------ */
    it("should not write ledger entries when gift deposit was already settled", async () => {
      const GIFT_AMOUNT_CENTS = 500_000; // £5,000.00 in cents

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build gift with deposit already settled
      const gift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Simulate another update (but deposit status doesn't change)
      const oldGift = gift.toObject();
      gift.set(
        {
          hasViewedAppModal: true // Some other field change
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnGiftUpdate(gift, oldGift as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(0); // No entries should be created

      const accountingRecords = await AccountingRecordIndex.find({});
      expect(accountingRecords.length).toBe(0);
    });

    /* ------------------------------------------------------------------
     * Gift Non-EUR Currency (Should Skip)
     * ------------------------------------------------------------------ */
    it("should not write ledger entries for non-EUR gift", async () => {
      const GIFT_AMOUNT_CENTS = 2000; // £20.00 in cents

      // UK user (non-EUR)
      const user = await buildUser({
        residencyCountry: "GB",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      });

      // Build gift with GBP currency
      const gift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "GBP" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Created",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Simulate deposit settlement
      const oldGift = gift.toObject();
      gift.set(
        {
          "deposit.providers.wealthkernel.status": "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnGiftUpdate(gift, oldGift as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(0); // No entries for non-EUR

      const accountingRecords = await AccountingRecordIndex.find({});
      expect(accountingRecords.length).toBe(0);
    });
  });
});
