import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildDepositCashTransaction,
  buildCreditTicket,
  buildUser,
  buildPortfolio
} from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { DepositMethodEnum } from "../../../types/transactions";
import { LedgerAccounts, AccountingEventType } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateDepositsDbWithLedger", () => {
  const TODAY = "2025-06-12";
  const YESTERDAY = "2025-06-11";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateDepositsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should pass validation for all deposit stages with complete ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Stage 1 Deposit: Devengo acquisition confirmed (External → Intermediary #1)
    const stage1Deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 2 Deposit: Devengo collection confirmed + instant flow (Intermediary #1 → Intermediary #2)
    const stage2Deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: (await buildCreditTicket({ status: "Credited" })).id, // This makes inInstantMoneyFlow = true
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-2",
                  status: "confirmed",
                  accountId: "acc-2",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-2",
                  status: "confirmed",
                  settledAt: new Date(TODAY) // Stage 2 happens today
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 3 Deposit - Standard Flow: WealthKernel settled (Intermediary #1 → Omnibus)
    const stage3StandardDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-3-standard",
                  status: "confirmed",
                  accountId: "acc-3-standard",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-3-standard",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Stage 3 Deposit - Instant Flow: WealthKernel settled (Intermediary #2 → Omnibus)
    const stage3InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: (await buildCreditTicket({ status: "Credited" })).id, // This makes inInstantMoneyFlow = true
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-3-instant",
                  status: "confirmed",
                  accountId: "acc-3-instant",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-3-instant",
                  status: "confirmed",
                  settledAt: new Date(YESTERDAY) // Stage 2 happened yesterday
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-3-instant",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for each stage
    const stage1Description = `${user.id}|${stage1Deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage2Description = `${user.id}|${stage2Deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3StandardDescription = `${user.id}|${stage3StandardDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3InstantDescription = `${user.id}|${stage3InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Stage 1: External → Intermediary #1 (Devengo acquisition confirmed)
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description,
        document_id: stage1Deposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description,
        document_id: stage1Deposit.id,
        owner_id: user.id
      },
      // Stage 2: Intermediary #1 → Intermediary #2 (Devengo collection confirmed, instant flow)
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2Description,
        document_id: stage2Deposit.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2Description,
        document_id: stage2Deposit.id,
        owner_id: user.id
      },
      // Stage 3 - Standard Flow: Intermediary #1 → Omnibus (WealthKernel settled)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3StandardDescription,
        document_id: stage3StandardDeposit.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, // Standard flow uses INTERMEDIARY_DEPOSITS_1
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3StandardDescription,
        document_id: stage3StandardDeposit.id,
        owner_id: user.id
      },
      // Stage 3 - Instant Flow: Intermediary #2 → Omnibus (WealthKernel settled)
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3InstantDescription,
        document_id: stage3InstantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 4,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, // Instant flow uses INTERMEDIARY_DEPOSITS_2
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3InstantDescription,
        document_id: stage3InstantDeposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    // Expects 5 results: 3 bank transfer stages + 2 direct debit stages (even if no direct debit transactions exist)
    expect(results).toHaveLength(5);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 validation (External → Intermediary #1)
    expect(stage1Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage1",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage1Result.discrepancies).toBeUndefined();

    // Stage 2 validation (Intermediary #1 → Intermediary #2)
    expect(stage2Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage2",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage2Result.discrepancies).toBeUndefined();

    // Stage 3 validation (Intermediary → Omnibus) - Now includes both standard and instant flow
    expect(stage3Result).toEqual({
      isValid: true,
      ledgerEntryCount: 4, // 2 entries per deposit (standard + instant) = 4 total
      transactionType: "deposits_stage3",
      dbTotalAmount: Decimal.mul(DEPOSIT_AMOUNT_EUROS, 2).toNumber(), // 2 deposits = 2x amount
      ledgerTotalAmount: Decimal.mul(DEPOSIT_AMOUNT_EUROS, 2).toNumber(),
      difference: 0,
      transactionCount: 2 // 1 standard + 1 instant flow deposit
    });
    expect(stage3Result.discrepancies).toBeUndefined();
  });

  it("should fail validation when deposit amounts don't match ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();
    const LEDGER_AMOUNT_EUROS = 999.5; // Different amount in ledger

    // Create domestic user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Stage 1 deposit with Devengo acquisition confirmed
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entry with different amount
    const description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should fail due to amount mismatch
    expect(stage1Result.isValid).toBe(false);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
    expect(stage1Result.difference).toBe(DEPOSIT_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
    expect(stage1Result.transactionCount).toBe(1);
    expect(stage1Result.discrepancies).toEqual([
      {
        transactionId: deposit.id,
        dbAmount: DEPOSIT_AMOUNT_EUROS,
        ledgerAmount: LEDGER_AMOUNT_EUROS,
        description: `Stage 1: DB amount (€${DEPOSIT_AMOUNT_EUROS}) doesn't match client account credits (€${LEDGER_AMOUNT_EUROS})`,
        difference: Decimal.sub(DEPOSIT_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber()
      }
    ]);

    // Stage 2 and 3 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should handle multiple deposits with different client segments", async () => {
    const AMOUNT_1_CENTS = 500_00; // €500.00
    const AMOUNT_2_CENTS = 750_00; // €750.00
    const AMOUNT_1_EUROS = Decimal.div(AMOUNT_1_CENTS, 100).toNumber();
    const AMOUNT_2_EUROS = Decimal.div(AMOUNT_2_CENTS, 100).toNumber();

    // Create domestic user (GR)
    const userGR: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioGR: PortfolioDocument = await buildPortfolio({ owner: userGR.id });

    // Create EU user (DE)
    const userEU: UserDocument = await buildUser({
      residencyCountry: "DE",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioEU: PortfolioDocument = await buildPortfolio({ owner: userEU.id });

    // Create Stage 1 deposits with Devengo acquisition confirmed
    const depositGR = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: AMOUNT_1_CENTS },
        portfolio: portfolioGR.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-gr",
                  status: "confirmed",
                  accountId: "acc-gr",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      userGR,
      portfolioGR
    );

    const depositEU = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: AMOUNT_2_CENTS },
        portfolio: portfolioEU.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-eu",
                  status: "confirmed",
                  accountId: "acc-eu",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      userEU,
      portfolioEU
    );

    // Create corresponding ledger entries
    const descriptionGR = `${userGR.id}|${depositGR.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const descriptionEU = `${userEU.id}|${depositEU.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // GR deposit entries
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR,
        document_id: depositGR.id,
        owner_id: userGR.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR,
        document_id: depositGR.id,
        owner_id: userGR.id
      },
      // EU deposit entries
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU,
        document_id: depositEU.id,
        owner_id: userEU.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_EU_EEA,
        side: "credit",
        amount: AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU,
        document_id: depositEU.id,
        owner_id: userEU.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should validate both deposits
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(AMOUNT_1_EUROS + AMOUNT_2_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(AMOUNT_1_EUROS + AMOUNT_2_EUROS);
    expect(stage1Result.difference).toBe(0);
    expect(stage1Result.transactionCount).toBe(2);

    // Stage 2 and 3 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should ignore non-settled deposits", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents

    // Create user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create deposits with various non-settled statuses (all should be ignored)
    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Pending"
      },
      user,
      portfolio
    );

    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "PendingDeposit"
      },
      user,
      portfolio
    );

    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Cancelled"
      },
      user,
      portfolio
    );

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result, directDebitStage1Result, directDebitStage2Result] = results;

    // All stages should be valid with no transactions (non-settled deposits are ignored)
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);
    expect(stage1Result.dbTotalAmount).toBe(0);
    expect(stage1Result.ledgerTotalAmount).toBe(0);

    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);

    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);

    // Direct debit stages should also be valid with no transactions
    expect(directDebitStage1Result.isValid).toBe(true);
    expect(directDebitStage1Result.transactionCount).toBe(0);

    expect(directDebitStage2Result.isValid).toBe(true);
    expect(directDebitStage2Result.transactionCount).toBe(0);
  });

  it("should handle validation with no transactions", async () => {
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result, directDebitStage1Result, directDebitStage2Result] = results;

    // All stages should be valid with no transactions
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);
    expect(stage1Result.dbTotalAmount).toBe(0);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
    expect(stage1Result.difference).toBe(0);

    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);

    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);

    // Direct debit stages should also be valid with no transactions
    expect(directDebitStage1Result.isValid).toBe(true);
    expect(directDebitStage1Result.transactionCount).toBe(0);
    expect(directDebitStage1Result.dbTotalAmount).toBe(0);
    expect(directDebitStage1Result.ledgerTotalAmount).toBe(0);
    expect(directDebitStage1Result.difference).toBe(0);

    expect(directDebitStage2Result.isValid).toBe(true);
    expect(directDebitStage2Result.transactionCount).toBe(0);
  });

  it("should handle validation with no ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00;
    const DEPOSIT_AMOUNT_EUROS = new Decimal(DEPOSIT_AMOUNT_CENTS).div(100).toNumber();

    // Create user and deposit without ledger entries
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Stage 1 deposit but don't create any ledger entries
    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Validate deposits (no ledger entries created)
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should fail due to missing ledger entries
    expect(stage1Result.isValid).toBe(false);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
    expect(stage1Result.difference).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage1Result.transactionCount).toBe(1);
    expect(stage1Result.ledgerEntryCount).toBe(0);

    // Stage 2 and 3 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should pass validation for deposits without instant money flow (stage 2 skipped)", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = new Decimal(DEPOSIT_AMOUNT_CENTS).div(100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Single Deposit: No instant money flow (stage 2 skipped)
    // Has both Devengo acquisition (Stage 1) and WealthKernel settlement (Stage 3)
    // No linkedCreditTicket means no instant money flow, so Stage 2 is skipped
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
          // No collection section - stage 2 is skipped
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for stage 1 and stage 3 using the same transaction (no stage 2)
    const stage1Description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3Description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Stage 1: External → Intermediary #1 (Devengo acquisition confirmed)
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description,
        document_id: deposit.id,
        owner_id: user.id
      },
      // Stage 3: Intermediary → Omnibus (WealthKernel settled)
      // Direct from INTERMEDIARY_DEPOSITS_1 to CLIENTS_ACCOUNTS_OMNIBUS (no stage 2)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3Description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3Description,
        document_id: deposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result, directDebitStage1Result, directDebitStage2Result] = results;

    // Stage 1 validation (External → Intermediary #1)
    expect(stage1Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage1",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage1Result.discrepancies).toBeUndefined();

    // Stage 2 validation should be valid but empty (no instant money flow transactions)
    expect(stage2Result).toEqual({
      isValid: true,
      ledgerEntryCount: 0,
      transactionType: "deposits_stage2",
      dbTotalAmount: 0,
      ledgerTotalAmount: 0,
      difference: 0,
      transactionCount: 0
    });
    expect(stage2Result.discrepancies).toBeUndefined();

    // Stage 3 validation (Intermediary → Omnibus)
    expect(stage3Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage3",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage3Result.discrepancies).toBeUndefined();

    // Direct debit stages should be valid but empty
    expect(directDebitStage1Result.isValid).toBe(true);
    expect(directDebitStage1Result.transactionCount).toBe(0);
    expect(directDebitStage2Result.isValid).toBe(true);
    expect(directDebitStage2Result.transactionCount).toBe(0);
  });

  it("should handle mixed scenarios with and without instant money flow", async () => {
    const DEPOSIT_AMOUNT_CENTS = 500_00; // €500.00 in cents
    const DEPOSIT_AMOUNT_EUROS = new Decimal(DEPOSIT_AMOUNT_CENTS).div(100).toNumber();

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create credit ticket for one of the deposits (instant money flow)
    const creditTicket = await buildCreditTicket({
      status: "Credited"
    });

    // Instant flow deposits - separate deposits for each stage (as they happen on different days)

    // Stage 1: Devengo acquisition confirmed (External → Intermediary #1)
    const stage1InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant-1",
                  status: "confirmed",
                  accountId: "acc-instant-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 2: Devengo collection confirmed + instant flow (Intermediary #1 → Intermediary #2)
    const stage2InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: creditTicket.id, // This makes inInstantMoneyFlow = true
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant-1",
                  status: "confirmed",
                  accountId: "acc-instant-1",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-instant-2",
                  status: "confirmed",
                  settledAt: new Date(TODAY) // Stage 2 happens today
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 3: WealthKernel settled (Intermediary #2 → Omnibus) - Instant Flow
    const stage3InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: creditTicket.id, // This makes inInstantMoneyFlow = true
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant-1",
                  status: "confirmed",
                  accountId: "acc-instant-1",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-instant-2",
                  status: "confirmed",
                  settledAt: new Date(YESTERDAY) // Stage 2 happened yesterday
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-instant-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // No instant flow deposits - separate deposits for stage 1 and 3 (stage 2 skipped)

    // Stage 1: Devengo acquisition confirmed (External → Intermediary #1)
    const stage1NoInstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-no-instant-1",
                  status: "confirmed",
                  accountId: "acc-no-instant-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
          // No collection section - stage 2 is skipped
        }
      },
      user,
      portfolio
    );

    // Stage 3: WealthKernel settled (Intermediary #1 → Omnibus) - Standard Flow (direct from stage 1)
    const stage3NoInstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-no-instant-1",
                  status: "confirmed",
                  accountId: "acc-no-instant-1",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          }
          // No collection section - stage 2 is skipped
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-no-instant-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for all deposits
    const stage1InstantDesc = `${user.id}|${stage1InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage2InstantDesc = `${user.id}|${stage2InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3InstantDesc = `${user.id}|${stage3InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage1NoInstantDesc = `${user.id}|${stage1NoInstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3NoInstantDesc = `${user.id}|${stage3NoInstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Instant flow deposits - Stage 1
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1InstantDesc,
        document_id: stage1InstantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1InstantDesc,
        document_id: stage1InstantDeposit.id,
        owner_id: user.id
      },
      // Instant flow deposits - Stage 2
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2InstantDesc,
        document_id: stage2InstantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2InstantDesc,
        document_id: stage2InstantDeposit.id,
        owner_id: user.id
      },
      // Instant flow deposits - Stage 3 (Intermediary #2 → Omnibus)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3InstantDesc,
        document_id: stage3InstantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, // Instant flow uses INTERMEDIARY_DEPOSITS_2
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3InstantDesc,
        document_id: stage3InstantDeposit.id,
        owner_id: user.id
      },
      // No instant flow deposits - Stage 1
      {
        aa: 4,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1NoInstantDesc,
        document_id: stage1NoInstantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1NoInstantDesc,
        document_id: stage1NoInstantDeposit.id,
        owner_id: user.id
      },
      // Standard flow deposits - Stage 3 (Intermediary #1 → Omnibus, direct from stage 1)
      {
        aa: 5,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3NoInstantDesc,
        document_id: stage3NoInstantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 5,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, // Standard flow uses INTERMEDIARY_DEPOSITS_1
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3NoInstantDesc,
        document_id: stage3NoInstantDeposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result, directDebitStage1Result, directDebitStage2Result] = results;

    // Stage 1 should validate both instant and non-instant deposits
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2); // Two stage 1 deposits
    expect(stage1Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2);
    expect(stage1Result.difference).toBe(0);
    expect(stage1Result.transactionCount).toBe(2);

    // Stage 2 should only validate the instant flow deposit
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionType).toBe("deposits_stage2");
    expect(stage2Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS); // Only one stage 2 deposit
    expect(stage2Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage2Result.difference).toBe(0);
    expect(stage2Result.transactionCount).toBe(1);

    // Stage 3 should validate both instant and non-instant deposits
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionType).toBe("deposits_stage3");
    expect(stage3Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2); // Two stage 3 deposits
    expect(stage3Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2);
    expect(stage3Result.difference).toBe(0);
    expect(stage3Result.transactionCount).toBe(2);

    // Direct debit stages should be valid but empty
    expect(directDebitStage1Result.isValid).toBe(true);
    expect(directDebitStage1Result.transactionCount).toBe(0);
    expect(directDebitStage2Result.isValid).toBe(true);
    expect(directDebitStage2Result.transactionCount).toBe(0);
  });

  it("should exclude deposits with non-credited credit tickets from stage 2 validation", async () => {
    const DEPOSIT_AMOUNT_CENTS = 500_00; // €500.00 in cents
    const DEPOSIT_AMOUNT_EUROS = new Decimal(DEPOSIT_AMOUNT_CENTS).div(100).toNumber();

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create credit tickets with different statuses
    const creditedTicket = await buildCreditTicket({ status: "Credited" });
    const rejectedTicket = await buildCreditTicket({ status: "Rejected" });
    const pendingTicket = await buildCreditTicket({ status: "Pending" });

    // Deposit with credited ticket (should be included in stage 2)
    const depositWithCreditedTicket = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: creditedTicket.id,
        transferWithIntermediary: {
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-credited",
                  status: "confirmed",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Deposit with rejected ticket (should be excluded from stage 2)
    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: rejectedTicket.id,
        transferWithIntermediary: {
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-rejected",
                  status: "confirmed",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Deposit with pending ticket (should be excluded from stage 2)
    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: pendingTicket.id,
        transferWithIntermediary: {
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-pending",
                  status: "confirmed",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries only for the credited ticket deposit
    const stage2Description = `${user.id}|${depositWithCreditedTicket.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2Description,
        document_id: depositWithCreditedTicket.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2Description,
        document_id: depositWithCreditedTicket.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result, directDebitStage1Result, directDebitStage2Result] = results;

    // Stage 1 should be valid (no stage 1 deposits)
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);

    // Stage 2 should only include the deposit with credited ticket
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionType).toBe("deposits_stage2");
    expect(stage2Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS); // Only one deposit (credited ticket)
    expect(stage2Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage2Result.difference).toBe(0);
    expect(stage2Result.transactionCount).toBe(1); // Only credited ticket deposit

    // Stage 3 should be valid (no stage 3 deposits)
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);

    // Direct debit stages should be valid but empty
    expect(directDebitStage1Result.isValid).toBe(true);
    expect(directDebitStage1Result.transactionCount).toBe(0);
    expect(directDebitStage2Result.isValid).toBe(true);
    expect(directDebitStage2Result.transactionCount).toBe(0);
  });

  it("should validate instant-flow deposit when all stages share the same transaction id", async () => {
    const DEPOSIT_AMOUNT_CENTS = 500_00; // €500.00
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create deposit that already completed all three stages (instant flow)
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: (await buildCreditTicket({ status: "Credited" })).id,
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out",
                  status: "confirmed",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep",
            status: "Settled",
            settledAt: new Date(TODAY)
          }
        }
      },
      user,
      portfolio
    );

    const description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    // Ledger postings: Stage-1 (aa 1), Stage-2 (aa 2) and Stage-3 (aa 3)
    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Stage-1
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      // Stage-2  (instant-flow)
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      // Stage-3
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: deposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const [stage1Result, stage2Result, stage3Result] =
      await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(stage1Result.isValid).toBe(true);
    expect(stage2Result.isValid).toBe(true);
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.difference).toBe(0);
  });

  it("should detect flow-specific Stage 3 discrepancies for instant flow deposits", async () => {
    const DEPOSIT_AMOUNT_CENTS = 11000; // €110.00 in cents (matching real transaction)
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create CreditTicket with "Credited" status (instant flow)
    const creditTicket = await buildCreditTicket({
      status: "Credited",
      creditedAt: new Date("2025-07-25T18:57:38.506Z"),
      settledAt: new Date("2025-07-29T07:38:22.400Z")
    });

    // Create instant flow deposit matching real transaction 6883d39a7e3c62f9a6af6fa2
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: creditTicket.id, // This makes it instant flow
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "pyi_7ZrT0PsQBkvwPyqquW2PDL",
                  status: "confirmed",
                  accountId: "acc_7LJknctZkuyBSocFCPa2Kz",
                  settledAt: new Date("2025-07-25T18:57:36.414Z")
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "pyo_6y5zVwir5ryPvCmsJ62L19",
                  status: "confirmed",
                  settledAt: new Date("2025-07-28T05:51:51.011Z")
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "dep-3a2y6djmm244cy",
            status: "Settled",
            settledAt: new Date("2025-07-29T07:38:22.364Z")
          }
        }
      },
      user,
      portfolio
    );

    const description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    // Create ledger entries that reproduce the BUG found in production
    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Stage 1: External → Intermediary #1 (CORRECT)
      {
        aa: 2199,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: "2025-07-25",
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 2199,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: "2025-07-25",
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      // Stage 2: Intermediary #1 → Intermediary #2 (CORRECT for instant flow)
      {
        aa: 2307,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: "2025-07-28",
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 2307,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: "2025-07-28",
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      // Stage 3: Omnibus ← WRONG intermediary account (BUG!)
      // Should credit INTERMEDIARY_DEPOSITS_2 for instant flow, but credits INTERMEDIARY_DEPOSITS_1
      {
        aa: 2726,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: "2025-07-29",
        description,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 2726,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, // ❌ WRONG! Should be INTERMEDIARY_DEPOSITS_2
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: "2025-07-29",
        description,
        document_id: deposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits - should now DETECT the flow-specific discrepancy
    const results = await AccountingValidationService.validateDepositsDbWithLedger("2025-07-25");

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should be valid
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(1);

    // Stage 2 should be valid
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(1);

    // Stage 3 should now FAIL due to wrong intermediary account for instant flow
    expect(stage3Result.isValid).toBe(false);
    expect(stage3Result.transactionType).toBe("deposits_stage3");
    expect(stage3Result.transactionCount).toBe(1);
    expect(stage3Result.discrepancies).toBeDefined();
    expect(stage3Result.discrepancies!.length).toBe(1);

    const discrepancy = stage3Result.discrepancies![0];
    expect(discrepancy.transactionId).toBe(deposit.id);
    expect(discrepancy.dbAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(discrepancy.ledgerAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    // Difference is 0 for amounts, but validation fails due to wrong intermediary account
    expect(discrepancy.difference).toBe(0);
  });

  it("should correctly validate instant vs standard flow deposits", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create instant flow deposit (with credited ticket)
    const instantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: (await buildCreditTicket({ status: "Credited" })).id,
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant",
                  status: "confirmed",
                  accountId: "acc-instant",
                  settledAt: new Date(TODAY)
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-instant",
                  status: "confirmed",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-instant",
            status: "Settled",
            settledAt: new Date(TODAY)
          }
        }
      },
      user,
      portfolio
    );

    // Create standard flow deposit (no credited ticket)
    const standardDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-standard",
                  status: "confirmed",
                  accountId: "acc-standard",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-standard",
            status: "Settled",
            settledAt: new Date(TODAY)
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for instant flow (all 3 stages)
    const instantDescription = `${user.id}|${instantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const standardDescription = `${user.id}|${standardDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Instant flow - Stage 1
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: instantDescription,
        document_id: instantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: instantDescription,
        document_id: instantDeposit.id,
        owner_id: user.id
      },
      // Instant flow - Stage 2
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: instantDescription,
        document_id: instantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: instantDescription,
        document_id: instantDeposit.id,
        owner_id: user.id
      },
      // Instant flow - Stage 3 (from intermediary 2)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: instantDescription,
        document_id: instantDeposit.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: instantDescription,
        document_id: instantDeposit.id,
        owner_id: user.id
      },
      // Standard flow - Stage 1
      {
        aa: 4,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: standardDescription,
        document_id: standardDeposit.id,
        owner_id: user.id
      },
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: standardDescription,
        document_id: standardDeposit.id,
        owner_id: user.id
      },
      // Standard flow - Stage 3 (directly from intermediary 1, skipping stage 2)
      {
        aa: 5,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: standardDescription,
        document_id: standardDeposit.id,
        owner_id: user.id
      },
      {
        aa: 5,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: standardDescription,
        document_id: standardDeposit.id,
        owner_id: user.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should validate both deposits
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2); // Both deposits
    expect(stage1Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2);
    expect(stage1Result.difference).toBe(0);
    expect(stage1Result.transactionCount).toBe(2);

    // Stage 2 should validate only the instant flow deposit (credited ticket)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionType).toBe("deposits_stage2");
    expect(stage2Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS); // Only one stage 2 deposit
    expect(stage2Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage2Result.difference).toBe(0);
    expect(stage2Result.transactionCount).toBe(1); // Only credited ticket deposit

    // Stage 3 should validate both instant and non-instant deposits
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionType).toBe("deposits_stage3");
    expect(stage3Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2); // Both deposits
    expect(stage3Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2);
    expect(stage3Result.difference).toBe(0);
  });

  it("should validate only Devengo direct debit deposits with DIRECT_DEBIT_AND_BANK_TRANSFER method for stage 1", async () => {
    const AMOUNT_1_CENTS = 40000; // €400.00
    const AMOUNT_2_CENTS = 60000; // €600.00
    const AMOUNT_1_EUROS = Decimal.div(AMOUNT_1_CENTS, 100).toNumber();
    const AMOUNT_2_EUROS = Decimal.div(AMOUNT_2_CENTS, 100).toNumber();

    // Domestic user (GR)
    const userGR: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioGR: PortfolioDocument = await buildPortfolio({ owner: userGR.id });

    // EU user (DE)
    const userEU: UserDocument = await buildUser({
      residencyCountry: "DE",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioEU: PortfolioDocument = await buildPortfolio({ owner: userEU.id });

    // Devengo direct debit deposit (should be validated)
    const devengoDepositGR = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
        consideration: { currency: "EUR", amount: AMOUNT_1_CENTS },
        portfolio: portfolioGR.id,
        status: "Pending",
        transferWithIntermediary: {
          collection: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-gr",
                  status: "confirmed",
                  accountId: "acc-gr"
                }
              }
            }
          }
        },
        createdAt: new Date(TODAY)
      },
      userGR,
      portfolioGR
    );

    // Devengo direct debit deposit (should be validated)
    const devengoDepositEU = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
        consideration: { currency: "EUR", amount: AMOUNT_2_CENTS },
        portfolio: portfolioEU.id,
        status: "Pending",
        transferWithIntermediary: {
          collection: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-eu",
                  status: "confirmed",
                  accountId: "acc-eu"
                }
              }
            }
          }
        },
        createdAt: new Date(TODAY)
      },
      userEU,
      portfolioEU
    );

    // Devengo direct debit deposit with DIRECT_DEBIT (should be ignored)
    const devengoDepositIgnored = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.DIRECT_DEBIT,
        consideration: { currency: "EUR", amount: 99900 },
        portfolio: portfolioEU.id,
        status: "Pending",
        transferWithIntermediary: {
          collection: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-ignored",
                  status: "confirmed",
                  accountId: "acc-ignored"
                }
              }
            }
          }
        },
        createdAt: new Date(TODAY)
      },
      userEU,
      portfolioEU
    );

    // Create corresponding ledger entries for the two valid deposits
    const descriptionGR = `${userGR.id}|${devengoDepositGR.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const descriptionEU = `${userEU.id}|${devengoDepositEU.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // GR deposit entries
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR,
        document_id: devengoDepositGR.id,
        owner_id: userGR.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR,
        document_id: devengoDepositGR.id,
        owner_id: userGR.id
      },
      // EU deposit entries
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU,
        document_id: devengoDepositEU.id,
        owner_id: userEU.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_EU_EEA,
        side: "credit",
        amount: AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU,
        document_id: devengoDepositEU.id,
        owner_id: userEU.id
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(5); // 3 bank transfer stages + 2 direct debit stages
    const [, , , directDebitStage1Result, directDebitStage2Result] = results;

    // Direct debit stage 1 should validate only the two DIRECT_DEBIT_AND_BANK_TRANSFER deposits
    expect(directDebitStage1Result.isValid).toBe(true);
    expect(directDebitStage1Result.transactionType).toBe("deposits_direct_debit_stage1");
    expect(directDebitStage1Result.dbTotalAmount).toBe(AMOUNT_1_EUROS + AMOUNT_2_EUROS);
    expect(directDebitStage1Result.ledgerTotalAmount).toBe(AMOUNT_1_EUROS + AMOUNT_2_EUROS);
    expect(directDebitStage1Result.difference).toBe(0);
    expect(directDebitStage1Result.transactionCount).toBe(2);
    // The ignored deposit should not be included
    expect(
      directDebitStage1Result.discrepancies?.find((d) => d.transactionId === devengoDepositIgnored.id)
    ).toBeUndefined();

    // Direct debit stage 2 should be valid (no settled transactions)
    expect(directDebitStage2Result.isValid).toBe(true);
    expect(directDebitStage2Result.transactionCount).toBe(0);
  });
});
