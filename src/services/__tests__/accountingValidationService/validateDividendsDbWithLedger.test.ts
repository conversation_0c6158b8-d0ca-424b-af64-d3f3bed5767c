import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildUser,
  buildPortfolio,
  buildDividendTransaction,
  buildSavingsDividend
} from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { LedgerAccounts, AccountingEventType } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateDividendsDbWithLedger", () => {
  const TODAY = "2025-06-12";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateDividendsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  describe("when we validate asset dividends", () => {
    it("should pass validation for asset dividends with complete ledger entries", async () => {
      const DIVIDEND_AMOUNT_CENTS = 300_00; // €300.00 in cents
      const DIVIDEND_AMOUNT_EUROS = new Decimal(DIVIDEND_AMOUNT_CENTS).div(100).toNumber();

      // Create domestic user (GR)
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create settled asset dividend
      const dividend = await buildDividendTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { currency: "EUR", amount: DIVIDEND_AMOUNT_CENTS },
        asset: "equities_apple",
        providers: {
          wealthkernel: {
            id: "wk-div-1",
            status: "Settled"
          }
        },
        settledAt: new Date(TODAY)
      });

      const description = `${user.id}|${dividend.id}|${AccountingEventType.ASSET_DIVIDEND}`;

      // Create corresponding ledger entries for asset dividend
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: DIVIDEND_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: dividend.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: DIVIDEND_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: dividend.id,
          owner_id: user.id
        }
      ]);

      // Validate dividends
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult, mmfReceiptResult, mmfCommissionResult] = results;

      // Asset dividend validation
      expect(assetResult.isValid).toBe(true);
      expect(assetResult.transactionType).toBe("dividends_asset");
      expect(assetResult.dbTotalAmount).toBe(DIVIDEND_AMOUNT_EUROS);
      expect(assetResult.ledgerTotalAmount).toBe(DIVIDEND_AMOUNT_EUROS);
      expect(assetResult.difference).toBe(0);
      expect(assetResult.transactionCount).toBe(1);
      expect(assetResult.discrepancies).toBeUndefined();

      // MMF results should be valid but empty (no MMF dividends)
      expect(mmfReceiptResult.isValid).toBe(true);
      expect(mmfReceiptResult.transactionType).toBe("dividends_mmf_receipt");
      expect(mmfReceiptResult.transactionCount).toBe(0);

      expect(mmfCommissionResult.isValid).toBe(true);
      expect(mmfCommissionResult.transactionType).toBe("dividends_mmf_commission");
      expect(mmfCommissionResult.transactionCount).toBe(0);
    });

    it("should fail validation when asset dividend amounts don't match ledger entries", async () => {
      const DIVIDEND_AMOUNT_CENTS = 200_00; // €200.00 in cents
      const LEDGER_AMOUNT_EUROS = 199.5; // Different amount in ledger
      const DIVIDEND_AMOUNT_EUROS = new Decimal(DIVIDEND_AMOUNT_CENTS).div(100).toNumber();

      // Create domestic user
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create settled asset dividend
      const dividend = await buildDividendTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { currency: "EUR", amount: DIVIDEND_AMOUNT_CENTS },
        asset: "equities_microsoft",
        providers: {
          wealthkernel: {
            id: "wk-div-2",
            status: "Settled"
          }
        },
        settledAt: new Date(TODAY)
      });

      // Create ledger entry with different amount
      const description = `${user.id}|${dividend.id}|${AccountingEventType.ASSET_DIVIDEND}`;

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: dividend.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: dividend.id,
          owner_id: user.id
        }
      ]);

      // Validate dividends
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult] = results;

      expect(assetResult.isValid).toBe(false);
      expect(assetResult.transactionType).toBe("dividends_asset");
      expect(assetResult.dbTotalAmount).toBe(DIVIDEND_AMOUNT_EUROS);
      expect(assetResult.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
      expect(assetResult.difference).toBe(DIVIDEND_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
      expect(assetResult.transactionCount).toBe(1);
      expect(assetResult.discrepancies).toHaveLength(1);
      expect(assetResult.discrepancies![0]).toEqual({
        transactionId: dividend.id,
        dbAmount: DIVIDEND_AMOUNT_EUROS,
        ledgerAmount: LEDGER_AMOUNT_EUROS,
        description: `Asset dividend: DB amount (€${DIVIDEND_AMOUNT_EUROS}) doesn't match client account credits (€${LEDGER_AMOUNT_EUROS})`,
        difference: Decimal.sub(DIVIDEND_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber()
      });
    });

    it("should ignore non-settled asset dividends", async () => {
      const DIVIDEND_AMOUNT_CENTS = 100_00; // €100.00 in cents

      // Create user
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create dividend with non-settled status (should be ignored)
      await buildDividendTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { currency: "EUR", amount: DIVIDEND_AMOUNT_CENTS },
        asset: "equities_alphabet",
        providers: {
          wealthkernel: {
            id: "wk-div-4",
            status: "Cancelled"
          }
        }
      });

      // Validate dividends
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult] = results;

      expect(assetResult.isValid).toBe(true);
      expect(assetResult.transactionCount).toBe(0);
      expect(assetResult.dbTotalAmount).toBe(0);
      expect(assetResult.ledgerTotalAmount).toBe(0);
    });

    it("should handle validation with dividend transactions but no ledger entries", async () => {
      const DIVIDEND_AMOUNT_CENTS = 100_00;
      const DIVIDEND_AMOUNT_EUROS = new Decimal(DIVIDEND_AMOUNT_CENTS).div(100).toNumber();

      // Create user and asset dividend without ledger entries
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      await buildDividendTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { currency: "EUR", amount: DIVIDEND_AMOUNT_CENTS },
        asset: "equities_amazon",
        providers: {
          wealthkernel: {
            id: "wk-div-5",
            status: "Settled"
          }
        },
        settledAt: new Date(TODAY)
      });

      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult, mmfReceiptResult, mmfCommissionResult] = results;

      // Asset dividend should fail validation (no ledger entries)
      expect(assetResult.isValid).toBe(false);
      expect(assetResult.transactionCount).toBe(1);
      expect(assetResult.dbTotalAmount).toBe(DIVIDEND_AMOUNT_EUROS);
      expect(assetResult.ledgerTotalAmount).toBe(0);
      expect(assetResult.difference).toBe(DIVIDEND_AMOUNT_EUROS);

      // MMF results should be valid but empty
      expect(mmfReceiptResult.isValid).toBe(true);
      expect(mmfReceiptResult.transactionCount).toBe(0);
      expect(mmfCommissionResult.isValid).toBe(true);
      expect(mmfCommissionResult.transactionCount).toBe(0);
    });
  });

  describe("when we validate MMF dividends", () => {
    it("should pass validation for MMF dividends with receipt and commission flows", async () => {
      const GROSS_DIVIDEND_CENTS = 500; // 5.00 euros
      const COMMISSION_CENTS = 50; // 0.50 euros
      const GROSS_DIVIDEND_EUROS = new Decimal(GROSS_DIVIDEND_CENTS).div(100).toNumber();
      const COMMISSION_EUROS = new Decimal(COMMISSION_CENTS).div(100).toNumber();

      // Create domestic user (GR)
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create MMF dividend with commission
      const mmfDividend = await buildSavingsDividend({
        owner: user.id,
        portfolio: portfolio.id,
        originalDividendAmount: GROSS_DIVIDEND_CENTS,
        consideration: { amount: GROSS_DIVIDEND_CENTS - COMMISSION_CENTS, currency: "EUR" },
        fees: {
          commission: { amount: COMMISSION_EUROS, currency: "EUR" },
          fx: { amount: 0, currency: "EUR" }
        },
        savingsProduct: "mmf_dist_eur",
        status: "Pending",
        createdAt: new Date(TODAY)
      });

      const receiptDescription = `${user.id}|${mmfDividend.id}|${AccountingEventType.ASSET_DIVIDEND}`;
      const commissionDescription = `${user.id}|${mmfDividend.id}|${AccountingEventType.MMF_DIVIDEND_COMMISSION}`;

      // Create ledger entries for MMF dividend receipt (gross amount)
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: GROSS_DIVIDEND_EUROS,
          article_date: TODAY,
          description: receiptDescription,
          document_id: mmfDividend.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: GROSS_DIVIDEND_EUROS,
          article_date: TODAY,
          description: receiptDescription,
          document_id: mmfDividend.id,
          owner_id: user.id
        }
      ]);

      // Create ledger entries for MMF dividend commission
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: COMMISSION_EUROS,
          article_date: TODAY,
          description: commissionDescription,
          document_id: mmfDividend.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.MMF_DIVIDEND_FEES_WH,
          side: "credit",
          amount: COMMISSION_EUROS,
          article_date: TODAY,
          description: commissionDescription,
          document_id: mmfDividend.id,
          owner_id: user.id
        }
      ]);

      // Validate dividends
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult, mmfReceiptResult, mmfCommissionResult] = results;

      // Asset result should be valid but empty (no asset dividends)
      expect(assetResult.isValid).toBe(true);
      expect(assetResult.transactionType).toBe("dividends_asset");
      expect(assetResult.transactionCount).toBe(0);

      // MMF receipt validation
      expect(mmfReceiptResult.isValid).toBe(true);
      expect(mmfReceiptResult.transactionType).toBe("dividends_mmf_receipt");
      expect(mmfReceiptResult.dbTotalAmount).toBe(GROSS_DIVIDEND_EUROS);
      expect(mmfReceiptResult.ledgerTotalAmount).toBe(GROSS_DIVIDEND_EUROS);
      expect(mmfReceiptResult.difference).toBe(0);
      expect(mmfReceiptResult.transactionCount).toBe(1);
      expect(mmfReceiptResult.discrepancies).toBeUndefined();

      // MMF commission validation
      expect(mmfCommissionResult.isValid).toBe(true);
      expect(mmfCommissionResult.transactionType).toBe("dividends_mmf_commission");
      expect(mmfCommissionResult.dbTotalAmount).toBe(COMMISSION_EUROS);
      expect(mmfCommissionResult.ledgerTotalAmount).toBe(COMMISSION_EUROS);
      expect(mmfCommissionResult.difference).toBe(0);
      expect(mmfCommissionResult.transactionCount).toBe(1);
      expect(mmfCommissionResult.discrepancies).toBeUndefined();
    });

    it("should handle MMF dividends with no commission", async () => {
      const GROSS_DIVIDEND_CENTS = 300; // €3.00
      const GROSS_DIVIDEND_EUROS = new Decimal(GROSS_DIVIDEND_CENTS).div(100).toNumber();

      // Create domestic user
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create MMF dividend with no commission
      const mmfDividend = await buildSavingsDividend({
        owner: user.id,
        portfolio: portfolio.id,
        originalDividendAmount: GROSS_DIVIDEND_CENTS,
        consideration: { amount: GROSS_DIVIDEND_CENTS, currency: "EUR" },
        fees: {
          commission: { amount: 0, currency: "EUR" },
          fx: { amount: 0, currency: "EUR" }
        },
        savingsProduct: "mmf_dist_eur",
        status: "Pending",
        createdAt: new Date(TODAY)
      });

      const receiptDescription = `${user.id}|${mmfDividend.id}|${AccountingEventType.ASSET_DIVIDEND}`;

      // Create ledger entries for MMF dividend receipt only (no commission)
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: GROSS_DIVIDEND_EUROS,
          article_date: TODAY,
          description: receiptDescription,
          document_id: mmfDividend.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: GROSS_DIVIDEND_EUROS,
          article_date: TODAY,
          description: receiptDescription,
          document_id: mmfDividend.id,
          owner_id: user.id
        }
      ]);

      // Validate dividends
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult, mmfReceiptResult, mmfCommissionResult] = results;

      // Asset result should be empty
      expect(assetResult.isValid).toBe(true);
      expect(assetResult.transactionCount).toBe(0);

      // MMF receipt validation should pass
      expect(mmfReceiptResult.isValid).toBe(true);
      expect(mmfReceiptResult.transactionType).toBe("dividends_mmf_receipt");
      expect(mmfReceiptResult.dbTotalAmount).toBe(GROSS_DIVIDEND_EUROS);
      expect(mmfReceiptResult.ledgerTotalAmount).toBe(GROSS_DIVIDEND_EUROS);
      expect(mmfReceiptResult.difference).toBe(0);
      expect(mmfReceiptResult.transactionCount).toBe(1);

      // MMF commission validation should pass with zero amounts
      expect(mmfCommissionResult.isValid).toBe(true);
      expect(mmfCommissionResult.transactionType).toBe("dividends_mmf_commission");
      expect(mmfCommissionResult.dbTotalAmount).toBe(0);
      expect(mmfCommissionResult.ledgerTotalAmount).toBe(0);
      expect(mmfCommissionResult.difference).toBe(0);
      expect(mmfCommissionResult.transactionCount).toBe(0); // No transactions with commission > 0
    });
  });

  describe("multiple dividend types and client segments", () => {
    it("should handle multiple dividend types with different client segments", async () => {
      const ASSET_DIVIDEND_CENTS = 150_00; // €150.00
      const MMF_GROSS_CENTS = 400; // €4.00
      const MMF_COMMISSION_CENTS = 40; // €0.40

      const ASSET_DIVIDEND_EUROS = new Decimal(ASSET_DIVIDEND_CENTS).div(100).toNumber();
      const MMF_GROSS_EUROS = new Decimal(MMF_GROSS_CENTS).div(100).toNumber();
      const MMF_COMMISSION_EUROS = new Decimal(MMF_COMMISSION_CENTS).div(100).toNumber();

      // Create domestic user (GR)
      const userGR: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolioGR: PortfolioDocument = await buildPortfolio({ owner: userGR.id });

      // Create EU user (DE)
      const userEU: UserDocument = await buildUser({
        residencyCountry: "DE",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolioEU: PortfolioDocument = await buildPortfolio({ owner: userEU.id });

      // Create asset dividend for GR user
      const assetDividend = await buildDividendTransaction({
        owner: userGR.id,
        portfolio: portfolioGR.id,
        consideration: { currency: "EUR", amount: ASSET_DIVIDEND_CENTS },
        asset: "equities_tesla",
        providers: {
          wealthkernel: {
            id: "wk-div-3",
            status: "Settled"
          }
        },
        settledAt: new Date(TODAY)
      });

      // Create MMF dividend for EU user
      const mmfDividend = await buildSavingsDividend({
        owner: userEU.id,
        portfolio: portfolioEU.id,
        originalDividendAmount: MMF_GROSS_CENTS,
        consideration: { amount: MMF_GROSS_CENTS - MMF_COMMISSION_CENTS, currency: "EUR" },
        fees: {
          commission: { amount: MMF_COMMISSION_EUROS, currency: "EUR" },
          fx: { amount: 0, currency: "EUR" }
        },
        savingsProduct: "mmf_dist_eur",
        status: "Pending",
        createdAt: new Date(TODAY)
      });

      // Create ledger entries
      const assetDescription = `${userGR.id}|${assetDividend.id}|${AccountingEventType.ASSET_DIVIDEND}`;
      const mmfReceiptDescription = `${userEU.id}|${mmfDividend.id}|${AccountingEventType.ASSET_DIVIDEND}`;
      const mmfCommissionDescription = `${userEU.id}|${mmfDividend.id}|${AccountingEventType.MMF_DIVIDEND_COMMISSION}`;

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Asset dividend entries (GR user → CLIENT_DOMESTIC)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: ASSET_DIVIDEND_EUROS,
          article_date: TODAY,
          description: assetDescription,
          document_id: assetDividend.id,
          owner_id: userGR.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: ASSET_DIVIDEND_EUROS,
          article_date: TODAY,
          description: assetDescription,
          document_id: assetDividend.id,
          owner_id: userGR.id
        },
        // MMF dividend receipt entries (EU user → CLIENT_EU_EEA)
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: MMF_GROSS_EUROS,
          article_date: TODAY,
          description: mmfReceiptDescription,
          document_id: mmfDividend.id,
          owner_id: userEU.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_EU_EEA,
          side: "credit",
          amount: MMF_GROSS_EUROS,
          article_date: TODAY,
          description: mmfReceiptDescription,
          document_id: mmfDividend.id,
          owner_id: userEU.id
        },
        // MMF dividend commission entries
        {
          aa: 3,
          account_code: LedgerAccounts.CLIENT_EU_EEA,
          side: "debit",
          amount: MMF_COMMISSION_EUROS,
          article_date: TODAY,
          description: mmfCommissionDescription,
          document_id: mmfDividend.id,
          owner_id: userEU.id
        },
        {
          aa: 3,
          account_code: LedgerAccounts.MMF_DIVIDEND_FEES_WH,
          side: "credit",
          amount: MMF_COMMISSION_EUROS,
          article_date: TODAY,
          description: mmfCommissionDescription,
          document_id: mmfDividend.id,
          owner_id: userEU.id
        }
      ]);

      // Validate dividends
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      const [assetResult, mmfReceiptResult, mmfCommissionResult] = results;

      // All validations should pass
      expect(assetResult.isValid).toBe(true);
      expect(assetResult.transactionCount).toBe(1);
      expect(assetResult.dbTotalAmount).toBe(ASSET_DIVIDEND_EUROS);

      expect(mmfReceiptResult.isValid).toBe(true);
      expect(mmfReceiptResult.transactionCount).toBe(1);
      expect(mmfReceiptResult.dbTotalAmount).toBe(MMF_GROSS_EUROS);

      expect(mmfCommissionResult.isValid).toBe(true);
      expect(mmfCommissionResult.transactionCount).toBe(1);
      expect(mmfCommissionResult.dbTotalAmount).toBe(MMF_COMMISSION_EUROS);
    });
  });

  describe("edge cases", () => {
    it("should handle validation with no dividend transactions", async () => {
      const results = await AccountingValidationService.validateDividendsDbWithLedger(TODAY);

      expect(results).toHaveLength(3);
      results.forEach((result) => {
        expect(result.isValid).toBe(true);
        expect(result.transactionCount).toBe(0);
        expect(result.dbTotalAmount).toBe(0);
        expect(result.ledgerTotalAmount).toBe(0);
        expect(result.difference).toBe(0);
      });
    });
  });
});
