import "jest";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { LedgerAccounts } from "../../../types/accounting";
import { CASH_ACCOUNTS_WK_MAPPING } from "../../../configs/accountingConfig";

describe("AccountingValidationService.validateCashLedgerReconciliation", () => {
  beforeAll(async () => {
    await createSqliteDb();
    await connectDb("validateCashLedgerReconciliation");

    // Mock the WK mapping to include test accounts
    (CASH_ACCOUNTS_WK_MAPPING as any)[LedgerAccounts.CUSTODY_FEES_WH] = "test-custody-portfolio";
    (CASH_ACCOUNTS_WK_MAPPING as any)[LedgerAccounts.COMMISSION_FEES_WH] = "test-commission-portfolio";
  });

  beforeEach(async () => {
    await clearDb();
    await clearSqliteDb();
  });

  afterAll(async () => {
    await closeDb();

    // Clean up the mocked mapping
    (CASH_ACCOUNTS_WK_MAPPING as any)[LedgerAccounts.CUSTODY_FEES_WH] = undefined;
    (CASH_ACCOUNTS_WK_MAPPING as any)[LedgerAccounts.COMMISSION_FEES_WH] = undefined;
  });

  it("should reconcile cash balances correctly when WK and ledger deltas match", async () => {
    const fromDate = "2024-01-01";
    const asOfDate = "2024-01-15";

    // Create starting cash balance snapshots
    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1000.0,
        asOfDate: fromDate
      },
      {
        accountCode: LedgerAccounts.COMMISSION_FEES_WH,
        balance: 500.0,
        asOfDate: fromDate
      }
    ]);

    // Create ending cash balance snapshots (15 days later)
    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1100.0, // +100 increase
        asOfDate: asOfDate
      },
      {
        accountCode: LedgerAccounts.COMMISSION_FEES_WH,
        balance: 650.0, // +150 increase
        asOfDate: asOfDate
      }
    ]);

    // Create matching ledger entries that would account for these changes
    await AccountingLedgerStorageService.addLedgerEntries([
      // Custody fees: +100 net increase (debit)
      {
        aa: 1001,
        account_code: LedgerAccounts.CUSTODY_FEES_WH,
        side: "debit",
        amount: 100.0,
        article_date: "2024-01-10",
        description: "user123 | txn456 | custody fee",
        document_id: "txn456",
        owner_id: "user123"
      },
      // Commission fees: +150 net increase (debit)
      {
        aa: 1002,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "debit",
        amount: 150.0,
        article_date: "2024-01-12",
        description: "user123 | txn789 | commission fee",
        document_id: "txn789",
        owner_id: "user123"
      }
    ]);

    // Run the reconciliation
    const result = await AccountingValidationService.validateCashLedgerReconciliation(fromDate);

    // Validate results
    expect(result.fromDate).toBe(fromDate);

    // Check custody fees account
    expect(result.perAccount[LedgerAccounts.CUSTODY_FEES_WH]).toEqual({
      deltaWK: 100.0,
      deltaLedger: 100.0,
      difference: 0
    });

    // Check commission fees account
    expect(result.perAccount[LedgerAccounts.COMMISSION_FEES_WH]).toEqual({
      deltaWK: 150.0,
      deltaLedger: 150.0,
      difference: 0
    });

    // Check aggregate
    expect(result.aggregate).toEqual({
      deltaWK: 250.0, // 100 + 150
      deltaLedger: 250.0, // 100 + 150
      difference: 0
    });
  });

  it("should detect discrepancies when WK and ledger deltas don't match", async () => {
    const fromDate = "2024-01-01";
    const asOfDate = "2024-01-15";

    // Create starting cash balance snapshots
    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1000.0,
        asOfDate: fromDate
      }
    ]);

    // Create ending cash balance snapshots with different change
    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1100.0, // +100 increase
        asOfDate: asOfDate
      }
    ]);

    // Create ledger entries that don't match (only +50 instead of +100)
    await AccountingLedgerStorageService.addLedgerEntries([
      {
        aa: 1001,
        account_code: LedgerAccounts.CUSTODY_FEES_WH,
        side: "debit",
        amount: 50.0, // Only 50, but WK shows 100 increase
        article_date: "2024-01-10",
        description: "user123 | txn456 | custody fee",
        document_id: "txn456",
        owner_id: "user123"
      }
    ]);

    // Run the reconciliation
    const result = await AccountingValidationService.validateCashLedgerReconciliation(fromDate);

    // Should detect discrepancy
    expect(result.perAccount[LedgerAccounts.CUSTODY_FEES_WH]).toEqual({
      deltaWK: 100.0,
      deltaLedger: 50.0,
      difference: 50.0 // Discrepancy!
    });

    expect(result.aggregate).toEqual({
      deltaWK: 100.0,
      deltaLedger: 50.0,
      difference: 50.0
    });
  });

  it("should handle missing snapshots gracefully", async () => {
    const fromDate = "2024-01-01";

    // No snapshots created - should handle gracefully
    const result = await AccountingValidationService.validateCashLedgerReconciliation(fromDate);

    // Should return zero values for accounts with missing snapshots
    expect(result.fromDate).toBe(fromDate);
    expect(result.aggregate.deltaWK).toBe(0);
    expect(result.aggregate.deltaLedger).toBe(0);
    expect(result.aggregate.difference).toBe(0);
  });

  it("should handle credit entries correctly in ledger calculation", async () => {
    const fromDate = "2024-01-01";
    const asOfDate = "2024-01-15";

    // Create snapshots showing decrease
    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1000.0,
        asOfDate: fromDate
      },
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 900.0, // -100 decrease
        asOfDate: asOfDate
      }
    ]);

    // Create matching credit entry (which decreases the account balance)
    await AccountingLedgerStorageService.addLedgerEntries([
      {
        aa: 1001,
        account_code: LedgerAccounts.CUSTODY_FEES_WH,
        side: "credit",
        amount: 100.0,
        article_date: "2024-01-10",
        description: "user123 | txn456 | fee refund",
        document_id: "txn456",
        owner_id: "user123"
      }
    ]);

    const result = await AccountingValidationService.validateCashLedgerReconciliation(fromDate);

    // Credit of 100 should result in -100 delta ledger (debit - credit = 0 - 100 = -100)
    expect(result.perAccount[LedgerAccounts.CUSTODY_FEES_WH]).toEqual({
      deltaWK: -100.0,
      deltaLedger: -100.0,
      difference: 0
    });
  });

  it("should calculate aggregate correctly across multiple accounts", async () => {
    const fromDate = "2024-01-01";
    const asOfDate = "2024-01-15";

    // Multiple accounts with different changes
    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      // Account 1: starts at 1000
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1000.0,
        asOfDate: fromDate
      },
      // Account 2: starts at 500
      {
        accountCode: LedgerAccounts.COMMISSION_FEES_WH,
        balance: 500.0,
        asOfDate: fromDate
      }
    ]);

    await AccountingLedgerStorageService.addCashBalanceSnapshots([
      // Account 1: ends at 1200 (+200)
      {
        accountCode: LedgerAccounts.CUSTODY_FEES_WH,
        balance: 1200.0,
        asOfDate: asOfDate
      },
      // Account 2: ends at 400 (-100)
      {
        accountCode: LedgerAccounts.COMMISSION_FEES_WH,
        balance: 400.0,
        asOfDate: asOfDate
      }
    ]);

    // Matching ledger entries
    await AccountingLedgerStorageService.addLedgerEntries([
      // Account 1: +200 (debit)
      {
        aa: 1001,
        account_code: LedgerAccounts.CUSTODY_FEES_WH,
        side: "debit",
        amount: 200.0,
        article_date: "2024-01-10",
        description: "user123 | txn456 | custody fee",
        document_id: "txn456",
        owner_id: "user123"
      },
      // Account 2: -100 (credit)
      {
        aa: 1002,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: 100.0,
        article_date: "2024-01-12",
        description: "user123 | txn789 | commission refund",
        document_id: "txn789",
        owner_id: "user123"
      }
    ]);

    const result = await AccountingValidationService.validateCashLedgerReconciliation(fromDate);

    // Aggregate: WK delta = +200 + (-100) = +100
    // Aggregate: Ledger delta = +200 + (-100) = +100
    expect(result.aggregate).toEqual({
      deltaWK: 100.0,
      deltaLedger: 100.0,
      difference: 0
    });
  });
});
