import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildDepositCashTransaction,
  buildUser,
  buildPortfolio,
  buildWithdrawalCashTransaction
} from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { DepositMethodEnum } from "../../../types/transactions";
import { LedgerAccounts, AccountingEventType } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateAllDbWithLedger", () => {
  const TODAY = "2025-06-12";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateAllDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should validate both deposits and withdrawals", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00;
    const WITHDRAWAL_AMOUNT_CENTS = 500_00;
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();
    const WITHDRAWAL_AMOUNT_EUROS = Decimal.div(WITHDRAWAL_AMOUNT_CENTS, 100).toNumber();

    // Create user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Stage 1 deposit with Devengo acquisition confirmed
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    const withdrawal = await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS },
      providers: { wealthkernel: { id: "wk-wd-1", status: "Settled", settledAt: new Date(TODAY) } }
    });

    // Create corresponding ledger entries
    const depositDescription = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const withdrawalDescription = `${user.id}|${withdrawal.id}|${AccountingEventType.BANK_TRANSACTION_WITHDRAWAL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Deposit entries (complete double-entry)
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: depositDescription,
        document_id: deposit.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: depositDescription,
        document_id: deposit.id,
        owner_id: user.id
      },
      // Withdrawal entries (Stage 1: Omnibus → Intermediary)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: WITHDRAWAL_AMOUNT_EUROS,
        article_date: TODAY,
        description: withdrawalDescription,
        document_id: withdrawal.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_WITHDRAWALS,
        side: "debit",
        amount: WITHDRAWAL_AMOUNT_EUROS,
        article_date: TODAY,
        description: withdrawalDescription,
        document_id: withdrawal.id,
        owner_id: user.id
      }
    ]);

    // Validate all
    const results = await AccountingValidationService.validateAllDbWithLedger(TODAY);

    // Check categorized structure
    expect(results.deposits).toHaveLength(5); // deposits_stage1 + deposits_stage2 + deposits_stage3 + deposits_direct_debit_stage1 + deposits_direct_debit_stage2
    expect(results.withdrawals).toHaveLength(2); // withdrawals_stage1 + withdrawals_stage2
    expect(results.dividends).toHaveLength(3); // dividends_asset + dividends_mmf_receipt + dividends_mmf_commission
    expect(results.orders).toHaveLength(5); // asset_buy + asset_sell + mmf_buy + mmf_sell + mmf_internally_filled
    expect(results.rewards).toHaveLength(2); // rewards_deposit + rewards_order
    expect(results.gifts).toHaveLength(1); // gifts_deposit

    const [depositsStage1] = results.deposits;
    expect(depositsStage1?.isValid).toBe(true);
    expect(depositsStage1?.transactionCount).toBe(1);
    expect(depositsStage1?.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);

    const [withdrawalsStage1] = results.withdrawals;
    expect(withdrawalsStage1?.isValid).toBe(true);
    expect(withdrawalsStage1?.transactionCount).toBe(1);
    expect(withdrawalsStage1?.dbTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
  });

  it("should handle validation with no transactions", async () => {
    const results = await AccountingValidationService.validateAllDbWithLedger(TODAY);

    // Check categorized structure
    expect(results.deposits).toHaveLength(5); // deposits_stage1 + deposits_stage2 + deposits_stage3 + deposits_direct_debit_stage1 + deposits_direct_debit_stage2
    expect(results.withdrawals).toHaveLength(2); // withdrawals_stage1 + withdrawals_stage2
    expect(results.dividends).toHaveLength(3); // dividends_asset + dividends_mmf_receipt + dividends_mmf_commission
    expect(results.orders).toHaveLength(5); // asset_buy + asset_sell + mmf_buy + mmf_sell + mmf_internally_filled
    expect(results.rewards).toHaveLength(2); // rewards_deposit + rewards_order
    expect(results.gifts).toHaveLength(1); // gifts_deposit

    // All stages should be valid with no transactions
    results.deposits.forEach((result) => {
      expect(result.isValid).toBe(true);
      expect(result.transactionCount).toBe(0);
      expect(result.dbTotalAmount).toBe(0);
      expect(result.ledgerTotalAmount).toBe(0);
      expect(result.difference).toBe(0);
    });

    results.withdrawals.forEach((result) => {
      expect(result.isValid).toBe(true);
      expect(result.transactionCount).toBe(0);
    });
  });

  it("should handle validation with no ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00;
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create user and deposit without ledger entries
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    const results = await AccountingValidationService.validateAllDbWithLedger(TODAY);

    // Check categorized structure
    expect(results.deposits).toHaveLength(5); // deposits_stage1 + deposits_stage2 + deposits_stage3 + deposits_direct_debit_stage1 + deposits_direct_debit_stage2
    expect(results.withdrawals).toHaveLength(2); // withdrawals_stage1 + withdrawals_stage2
    expect(results.dividends).toHaveLength(3); // dividends_asset + dividends_mmf_receipt + dividends_mmf_commission
    expect(results.orders).toHaveLength(5); // asset_buy + asset_sell + mmf_buy + mmf_sell + mmf_internally_filled
    expect(results.rewards).toHaveLength(2); // rewards_deposit + rewards_order
    expect(results.gifts).toHaveLength(1); // gifts_deposit

    // Deposits stage 1 should fail due to no ledger entries
    const depositsStage1 = results.deposits.find((r) => r.transactionType === "deposits_stage1");
    expect(depositsStage1?.isValid).toBe(false);
    expect(depositsStage1?.transactionCount).toBe(1);
    expect(depositsStage1?.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(depositsStage1?.ledgerTotalAmount).toBe(0);
    expect(depositsStage1?.difference).toBe(DEPOSIT_AMOUNT_EUROS);

    // Deposits stages 2 and 3 should be valid (no transactions)
    const depositsStage2 = results.deposits.find((r) => r.transactionType === "deposits_stage2");
    const depositsStage3 = results.deposits.find((r) => r.transactionType === "deposits_stage3");
    expect(depositsStage2?.isValid).toBe(true);
    expect(depositsStage2?.transactionCount).toBe(0);
    expect(depositsStage3?.isValid).toBe(true);
    expect(depositsStage3?.transactionCount).toBe(0);

    // Withdrawals stages should be valid (no transactions)
    results.withdrawals.forEach((result) => {
      expect(result.isValid).toBe(true);
      expect(result.transactionCount).toBe(0);
    });
  });
});
