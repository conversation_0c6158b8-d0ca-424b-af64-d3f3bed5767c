import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { buildAssetTransaction, buildOrder, buildPortfolio, buildUser } from "../../../tests/utils/generateModels";
import OrderService from "../../orderService";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";
import { AssetTransactionDocument } from "../../../models/Transaction";
import { OrderDocument } from "../../../models/Order";
import DateUtil from "../../../utils/dateUtil";

describe("OrderService.getLatestMatchedOrderId", () => {
  beforeAll(async () => {
    await connectDb("getLatestMatchedOrderId");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("when user has no matched orders", () => {
    let user: UserDocument;

    beforeEach(async () => {
      user = await buildUser();
    });

    it("should return undefined", async () => {
      const result = await OrderService.getLatestMatchedOrderId(user.id);
      expect(result).toBeUndefined();
    });
  });

  describe("when user has matched orders from asset transactions", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction: AssetTransactionDocument;
    let matchedOrder1: OrderDocument;
    let matchedOrder2: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      // Create first matched order (older)
      matchedOrder1 = await buildOrder({
        status: "Matched",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: DateUtil.getDateOfHoursAgo(new Date(), 2),
        updatedAt: DateUtil.getDateOfHoursAgo(new Date(), 2)
      });

      // Create second matched order (newer)
      matchedOrder2 = await buildOrder({
        status: "Matched",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 15000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: DateUtil.getDateOfHoursAgo(new Date(), 1),
        updatedAt: DateUtil.getDateOfHoursAgo(new Date(), 1)
      });

      assetTransaction.orders = [matchedOrder1.id, matchedOrder2.id];
      await assetTransaction.save();
    });

    it("should return the ID of the most recently updated matched order", async () => {
      const result = await OrderService.getLatestMatchedOrderId(user.id);
      expect(result).toBe(matchedOrder2.id);
    });
  });

  describe("when user has orders older than 1 day", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction: AssetTransactionDocument;
    let oldMatchedOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      // Create matched order older than 1 day
      oldMatchedOrder = await buildOrder({
        status: "Matched",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
        updatedAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
      });

      assetTransaction.orders = [oldMatchedOrder.id];
      await assetTransaction.save();
    });

    it("should return undefined when no orders are within the last day", async () => {
      const result = await OrderService.getLatestMatchedOrderId(user.id);
      expect(result).toBeUndefined();
    });
  });

  describe("when user has pending orders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction: AssetTransactionDocument;
    let pendingOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending",
        portfolioTransactionCategory: "buy"
      });

      pendingOrder = await buildOrder({
        status: "Pending",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Pending",
            submittedAt: new Date()
          }
        },
        updatedAt: new Date()
      });

      assetTransaction.orders = [pendingOrder.id];
      await assetTransaction.save();
    });

    it("should return undefined when only pending orders exist", async () => {
      const result = await OrderService.getLatestMatchedOrderId(user.id);
      expect(result).toBeUndefined();
    });
  });

  describe("when user has settled orders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction: AssetTransactionDocument;
    let settledOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      settledOrder = await buildOrder({
        status: "Settled",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: DateUtil.getDateOfHoursAgo(new Date(), 1),
        updatedAt: DateUtil.getDateOfHoursAgo(new Date(), 1)
      });

      assetTransaction.orders = [settledOrder.id];
      await assetTransaction.save();
    });

    it("should return the ID of the settled order", async () => {
      const result = await OrderService.getLatestMatchedOrderId(user.id);
      expect(result).toBe(settledOrder.id);
    });
  });
});
