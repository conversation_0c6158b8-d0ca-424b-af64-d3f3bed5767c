import Decimal from "decimal.js";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildSubscription,
  buildUser
} from "../../../tests/utils/generateModels";
import OrderService from "../../orderService";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { Order, OrderDocument } from "../../../models/Order";
import { WealthkernelService, CurrencyEnum } from "../../../external-services/wealthkernelService";
import { faker } from "@faker-js/faker";
import { Portfolio, PortfolioDocument } from "../../../models/Portfolio";
import { UserDocument } from "../../../models/User";
import { AssetTransactionDocument } from "../../../models/Transaction";
import eventEmitter from "../../../loaders/eventEmitter";
import logger from "../../../external-services/loggerService";
import events from "../../../event-handlers/events";
import { buildWealthkernelOrderResponse } from "../../../tests/utils/generateWealthkernel";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("OrderService.syncOrderByWealthkernelId", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("syncOrderByWealthkernelId"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("order status transitions", () => {
    let order: OrderDocument;
    let transaction: AssetTransactionDocument;

    beforeEach(async () => {
      jest.spyOn(logger, "warn").mockImplementation(() => null);

      const user = await buildUser();
      await buildSubscription({
        owner: user.id
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
        holdings: [await buildHoldingDTO(true, "equities_us", 10, { price: 50 })]
      });
      transaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      // Mock WealthkernelService response
      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: "123",
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        status: "Matched",
        side: "Buy"
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);
    });

    afterEach(() => {
      jest.resetAllMocks();
    });

    it("should allow transition from Pending to Matched", async () => {
      order = await buildOrder({
        isin: ASSET_CONFIG["equities_us"].isin,
        status: "Pending",
        providers: { wealthkernel: { status: "Pending", id: "123", submittedAt: new Date() } },
        transaction: transaction.id
      });

      await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Matched");
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
    });

    it("should not allow transition from Pending to Settled", async () => {
      order = await buildOrder({
        status: "Pending",
        providers: { wealthkernel: { status: "Pending", id: "123", submittedAt: new Date() } },
        transaction: transaction.id
      });

      await expect(OrderService.syncOrderByWealthkernelId(order.id, "Settled")).rejects.toThrow(
        `Cannot update order ${order.id} as the transition from Pending to Settled is not valid`
      );

      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Pending");
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Pending");
      expect(logger.warn).toHaveBeenCalledWith(
        "Cannot update WK order as the transition from Pending to Settled is not valid",
        expect.any(Object)
      );
    });

    it("should allow transition from Matched to Settled", async () => {
      order = await buildOrder({
        status: "Matched",
        providers: { wealthkernel: { status: "Matched", id: "123", submittedAt: new Date() } },
        transaction: transaction.id
      });

      await OrderService.syncOrderByWealthkernelId(order.id, "Settled");

      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Settled");
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
    });

    it("should not allow transition from Settled to Matched", async () => {
      order = await buildOrder({
        status: "Settled",
        providers: { wealthkernel: { status: "Matched", id: "123", submittedAt: new Date() } },
        transaction: transaction.id
      });

      await expect(OrderService.syncOrderByWealthkernelId(order.id, "Matched")).rejects.toThrow(
        `Cannot update order ${order.id} as the transition from Settled to Matched is not valid`
      );

      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Settled");
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
      expect(logger.warn).toHaveBeenCalledWith(
        "Cannot update WK order as the transition from Settled to Matched is not valid",
        expect.any(Object)
      );
    });
  });

  describe("when orderId matches buy order and status is settled", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_BUY_ORDER_ID = faker.string.uuid();
    const WK_SETTLEMENT_DATE = "2022-09-02T14:30:00Z";

    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let order: OrderDocument;
    let transaction: AssetTransactionDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
        cash: { GBP: { available: 10, reserved: 0, settled: 0 } }
      });
      await buildSubscription({ owner: user.id });
      await buildInvestmentProduct(true, { assetId: "equities_us" });

      transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_BUY_ORDER_ID, status: "Matched", submittedAt: DATE } },
        status: "Matched",
        side: "Buy",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        consideration: {
          amount: 20000,
          originalAmount: 20000,
          amountSubmitted: 19700,
          currency: "GBP"
        }
      });

      transaction.orders = [order];
      await transaction.save();

      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_BUY_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        status: "Matched",
        side: "Buy",
        marketSettledAt: new Date(WK_SETTLEMENT_DATE)
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Settled");
    });

    it("should update order status to Settled", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Settled");
    });

    it("should keep providers.wealthkernel.status as Matched", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
    });

    it("should NOT update cash", async () => {
      const updatedPortfolio = await Portfolio.findById(portfolio.id);
      expect(updatedPortfolio!.cash).toMatchObject(
        expect.objectContaining({
          GBP: expect.objectContaining({ available: 10, reserved: 0, settled: 0 })
        })
      );
    });
  });

  describe("when orderId matches sell order and status is settled", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_SELL_ORDER_ID = faker.string.uuid();

    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let order: OrderDocument;
    let transaction: AssetTransactionDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
        cash: { GBP: { available: 200, reserved: 0, settled: 0 } }
      });
      await buildSubscription({ owner: user.id });
      await buildInvestmentProduct(true, { assetId: "equities_us" });

      transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_SELL_ORDER_ID, status: "Matched", submittedAt: DATE } },
        status: "Matched",
        side: "Sell",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        consideration: {
          amount: 20000,
          originalAmount: 20000,
          currency: "GBP"
        }
      });

      transaction.orders = [order];
      await transaction.save();

      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_SELL_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        status: "Matched",
        side: "Sell"
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Settled");
    });

    it("should update order status to Settled", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Settled");
    });

    it("should keep providers.wealthkernel.status as Matched", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
    });

    it("should update cash availability from available to settled", async () => {
      const updatedPortfolio = await Portfolio.findById(portfolio.id);
      expect(updatedPortfolio!.cash).toMatchObject(
        expect.objectContaining({
          GBP: expect.objectContaining({ available: 200, reserved: 0, settled: 200 })
        })
      );
    });
  });

  describe("when an order is rejected", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_SELL_ORDER_ID = faker.string.uuid();

    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let order: OrderDocument;
    let transaction: AssetTransactionDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
        cash: { GBP: { available: 200, reserved: 0, settled: 0 } }
      });
      await buildSubscription({ owner: user.id });
      await buildInvestmentProduct(true, { assetId: "equities_us" });

      transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_SELL_ORDER_ID, status: "Pending", submittedAt: DATE } },
        status: "Pending",
        side: "Sell",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        quantity: 1
      });

      transaction.orders = [order];
      await transaction.save();

      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_SELL_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        status: "Rejected",
        side: "Sell",
        reason: "Could not process transaction"
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Rejected");
    });

    it("should keep order status as Pending", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.status).toBe("Pending");
    });

    it("should change providers.wealthkernel.status to Rejected", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Rejected");
    });

    it("should emit an order rejected event", async () => {
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.order.orderRejection.eventId,
        expect.objectContaining({
          email: user.email
        }),
        {
          asset: "equities_us",
          amount: undefined,
          quantity: 1,
          currency: "GBP",
          side: "Sell",
          rejectionReason: "Could not process transaction"
        }
      );
    });
  });

  describe("when order is for asset with order currency different from traded currency (paid_mid plan)", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_ORDER_ID = "wk-order-123";
    const EXCHANGE_RATE = 1.25;
    const BASE_EXCHANGE_RATE = 1.2;

    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let order: OrderDocument;
    let transaction: AssetTransactionDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildSubscription({ owner: user.id, price: "paid_mid_monthly" });
      await buildInvestmentProduct(true, { assetId: "equities_apple" }); // Apple stock (foreign currency)

      transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
        status: "Pending",
        side: "Buy",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
        consideration: {
          amount: 10000, // £100
          originalAmount: 10000,
          currency: "GBP"
        }
      });

      transaction.orders = [order];
      await transaction.save();

      // Mock WK order response with exchange rate
      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
        status: "Matched",
        side: "Buy",
        fills: [
          {
            status: "Matched",
            exchangeRate: EXCHANGE_RATE,
            baseExchangeRate: BASE_EXCHANGE_RATE,
            price: { currency: CurrencyEnum.USD, amount: 12500 },
            consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
            quantity: 1
          }
        ]
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
    });

    it("should store broker FX rate from WealthKernel", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.brokerFxRate).toBe(EXCHANGE_RATE);
    });

    it("should store base exchange rate from WealthKernel", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.baseExchangeRate).toBe(BASE_EXCHANGE_RATE);
    });

    it("should calculate and store broker FX fee", async () => {
      const updatedOrder = await Order.findById(order.id);
      // For paid_mid plan (0.25% spread) on £100 = £0.25 (25 cents)
      expect(updatedOrder!.providers!.wealthkernel!.accountingBrokerFxFee).toBe(25);
    });

    it("should store exchange rate with spread for paid_mid plan", async () => {
      const updatedOrder = await Order.findById(order.id);

      // For paid_mid plan (0.325% spread) on BUY order: 1.2 * (1 - 0.00325) = 1.1961
      const expectedExchangeRateWithSpread = new Decimal(BASE_EXCHANGE_RATE)
        .mul(new Decimal(1).minus(0.00325))
        .toNumber();
      expect(updatedOrder!.exchangeRate).toBe(expectedExchangeRateWithSpread);
    });
  });

  describe("when order is for asset with order currency different from traded currency (free plan)", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_ORDER_ID = "wk-order-free-123";
    const EXCHANGE_RATE = 1.25;
    const BASE_EXCHANGE_RATE = 1.2;

    let user: UserDocument;
    let order: OrderDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      const portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildSubscription({ owner: user.id, price: "free_monthly" });
      await buildInvestmentProduct(true, { assetId: "equities_apple" });

      const transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
        status: "Pending",
        side: "Buy",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
        consideration: {
          amount: 10000, // £100
          originalAmount: 10000,
          currency: "GBP"
        }
      });

      transaction.orders = [order];
      await transaction.save();

      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
        status: "Matched",
        side: "Buy",
        fills: [
          {
            status: "Matched",
            exchangeRate: EXCHANGE_RATE,
            baseExchangeRate: BASE_EXCHANGE_RATE,
            price: { currency: CurrencyEnum.USD, amount: 12500 },
            consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
            quantity: 1
          }
        ]
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
    });

    it("should store exchange rate with spread for free plan", async () => {
      const updatedOrder = await Order.findById(order.id);

      // For free plan (0.55% spread) on BUY order: 1.2 * (1 - 0.0055) = 1.1934
      const expectedExchangeRateWithSpread = new Decimal(BASE_EXCHANGE_RATE)
        .mul(new Decimal(1).minus(0.0055))
        .toNumber();
      expect(updatedOrder!.exchangeRate).toBe(expectedExchangeRateWithSpread);
    });
  });

  describe("when order is for asset with order currency different from traded currency (paid_low plan)", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_ORDER_ID = "wk-order-paid-low-123";
    const EXCHANGE_RATE = 1.25;
    const BASE_EXCHANGE_RATE = 1.2;

    let user: UserDocument;
    let order: OrderDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      const portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildSubscription({ owner: user.id, price: "paid_low_monthly" });
      await buildInvestmentProduct(true, { assetId: "equities_apple" });

      const transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
        status: "Pending",
        side: "Buy",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
        consideration: {
          amount: 10000, // £100
          originalAmount: 10000,
          currency: "GBP"
        }
      });

      transaction.orders = [order];
      await transaction.save();

      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
        status: "Matched",
        side: "Buy",
        fills: [
          {
            status: "Matched",
            exchangeRate: EXCHANGE_RATE,
            baseExchangeRate: BASE_EXCHANGE_RATE,
            price: { currency: CurrencyEnum.USD, amount: 12500 },
            consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
            quantity: 1
          }
        ]
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
    });

    it("should store exchange rate with spread for paid_low plan", async () => {
      const updatedOrder = await Order.findById(order.id);

      // For paid_low plan (0.4% spread) on BUY order: 1.2 * (1 - 0.004) = 1.1952
      const expectedExchangeRateWithSpread = new Decimal(BASE_EXCHANGE_RATE)
        .mul(new Decimal(1).minus(0.004))
        .toNumber();
      expect(updatedOrder!.exchangeRate).toBe(expectedExchangeRateWithSpread);
    });
  });

  describe("when order is for asset with order currency same as traded currency", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const WK_ORDER_ID = "wk-order-456";

    let user: UserDocument;
    let order: OrderDocument;

    beforeEach(async () => {
      Date.now = jest.fn(() => DATE.valueOf());

      user = await buildUser();
      const portfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildSubscription({ owner: user.id, price: "paid_mid_monthly" });
      await buildInvestmentProduct(true, { assetId: "equities_us" });

      const transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id
      });

      order = await buildOrder({
        providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
        status: "Pending",
        side: "Buy",
        transaction: transaction.id,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
        consideration: {
          amount: 10000, // £100
          originalAmount: 10000,
          currency: "GBP"
        }
      });

      // Mock WK order response
      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
        id: WK_ORDER_ID,
        isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
        status: "Matched",
        side: "Buy",
        fills: [
          {
            status: "Matched",
            exchangeRate: 1,
            baseExchangeRate: 1,
            price: { currency: CurrencyEnum.GBP, amount: 10000 },
            consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
            quantity: 1
          }
        ]
      });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

      await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
    });

    it("should store broker exchange rate and have value of 1", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.brokerFxRate).toBe(1);
    });

    it("should store base exchange rate and have value of 1", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.baseExchangeRate).toBe(1);
    });

    it("should store broker FX fee and have value of 0", async () => {
      const updatedOrder = await Order.findById(order.id);
      expect(updatedOrder!.providers!.wealthkernel!.accountingBrokerFxFee).toBe(0);
    });
  });

  describe("remainder cash return functionality", () => {
    let user: UserDocument;

    beforeEach(async () => {
      // Create investment product once for all tests
      await buildInvestmentProduct(true, { assetId: "equities_us" });

      user = await buildUser();
      await buildSubscription({ owner: user.id });
    });

    describe("buy orders with remainder", () => {
      it("should return remainder as cash when buy order is matched", async () => {
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 100, reserved: 0, settled: 100 } }
        });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        const order = await buildOrder({
          providers: { wealthkernel: { id: "wk-buy-order", status: "Pending", submittedAt: new Date() } },
          consideration: {
            currency: "GBP",
            originalAmount: 5000,
            amountSubmitted: 5000,
            amount: 4800 // Remainder = 200 cents = 2.00 GBP
          },
          side: "Buy",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          transaction: transaction.id
        });

        transaction.orders = [order];
        await transaction.save();

        // Mock WealthKernel service to return order data with fills
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-buy-order",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: 1,
              baseExchangeRate: 1,
              price: { currency: CurrencyEnum.GBP, amount: 4800 },
              consideration: { currency: CurrencyEnum.GBP, amount: 48.0 }, // 4800 cents = 48.00 GBP
              quantity: 1,
              filledAt: new Date(),
              settlementDate: new Date()
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash amounts
        const initialPortfolio = await Portfolio.findById(portfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.GBP!.available;
        const initialSettledCash = initialPortfolio!.cash.GBP!.settled;

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

        // Verify remainder cash was returned
        // Remainder = 5000 - 4800 = 200 cents = 2.00 GBP
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash.GBP!.available).toBe(Decimal.add(initialAvailableCash, 2.0).toNumber());
        expect(updatedPortfolio!.cash.GBP!.settled).toBe(Decimal.add(initialSettledCash, 2.0).toNumber());
      });

      it("should handle remainder cash return in different currencies", async () => {
        const eurUser = await buildUser({ currency: "EUR" });
        const eurPortfolio = await buildPortfolio({
          owner: eurUser.id,
          providers: { wealthkernel: { id: "portfolio-id-eur", status: "Active" } },
          cash: { EUR: { available: 100, reserved: 0, settled: 100 } }
        });
        await buildSubscription({ owner: eurUser.id });

        const eurTransaction = await buildAssetTransaction({
          owner: eurUser.id,
          portfolio: eurPortfolio.id
        });

        const eurOrder = await buildOrder({
          providers: {
            wealthkernel: {
              id: "wk-order-eur",
              status: "Pending",
              submittedAt: new Date(),
              baseExchangeRate: 1.15
            }
          },
          consideration: {
            currency: "EUR",
            originalAmount: 10000,
            amountSubmitted: 10000,
            amount: 9750 // Remainder = 10000 - 9750 = 250 cents = 2.50 EUR
          },
          side: "Buy",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          transaction: eurTransaction.id
        });

        eurTransaction.orders = [eurOrder];
        await eurTransaction.save();

        // Mock WealthKernel service to return order data with fills including baseExchangeRate
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-order-eur",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: 1.15,
              baseExchangeRate: 1.15,
              price: { currency: CurrencyEnum.USD, amount: 9750 },
              consideration: { currency: CurrencyEnum.EUR, amount: 97.5 }, // 9750 cents = 97.50 EUR
              quantity: 1,
              filledAt: new Date(),
              settlementDate: new Date()
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash amounts
        const initialPortfolio = await Portfolio.findById(eurPortfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.EUR!.available;
        const initialSettledCash = initialPortfolio!.cash.EUR!.settled;

        await OrderService.syncOrderByWealthkernelId(eurOrder.id, "Matched");

        // Verify remainder cash was returned in EUR
        // Remainder = 10000 - 9750 = 250 cents = 2.50 EUR
        const updatedPortfolio = await Portfolio.findById(eurPortfolio.id);
        expect(updatedPortfolio!.cash.EUR!.available).toBe(Decimal.add(initialAvailableCash, 2.5).toNumber());
        expect(updatedPortfolio!.cash.EUR!.settled).toBe(Decimal.add(initialSettledCash, 2.5).toNumber());
      });
    });

    describe("buy orders without remainder", () => {
      it("should not return cash when buy order has no remainder", async () => {
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 100, reserved: 0, settled: 100 } }
        });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        const order = await buildOrder({
          providers: { wealthkernel: { id: "wk-order-no-remainder", status: "Pending", submittedAt: new Date() } },
          status: "Pending",
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 5000, // settled amount equals submitted amount
            originalAmount: 5000,
            amountSubmitted: 5000, // submitted amount
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-order-no-remainder",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy"
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash state
        const initialPortfolio = await Portfolio.findById(portfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.GBP!.available;
        const initialSettledCash = initialPortfolio!.cash.GBP!.settled;

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

        // Verify no remainder cash was returned (cash should remain the same)
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash.GBP!.available).toBe(initialAvailableCash);
        expect(updatedPortfolio!.cash.GBP!.settled).toBe(initialSettledCash);
      });
    });

    describe("sell orders", () => {
      it("should not return remainder cash for sell orders (remainder is always 0)", async () => {
        // Add holdings to portfolio for the asset we're going to sell
        // Use buildHoldingDTO to ensure consistency
        const holding = await buildHoldingDTO(true, "equities_us", 10, { price: 50 });

        // Create a portfolio that contains the holding we want to sell
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 100, reserved: 0, settled: 100 } },
          holdings: [{ asset: holding.asset, quantity: holding.quantity, assetCommonId: holding.assetCommonId }]
        });

        // Create a transaction linked to the portfolio with holdings
        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        const order = await buildOrder({
          providers: { wealthkernel: { id: "wk-sell-order", status: "Pending", submittedAt: new Date() } },
          status: "Pending",
          side: "Sell",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          quantity: 1, // Selling 1 share
          consideration: {
            amount: 4000, // settled amount
            originalAmount: 5000,
            amountSubmitted: 4500, // submitted amount
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-sell-order",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Sell",
          fills: [
            {
              status: "Matched",
              exchangeRate: 1,
              baseExchangeRate: 1,
              price: { currency: CurrencyEnum.GBP, amount: 4000 }, // 40 GBP per share
              consideration: { currency: CurrencyEnum.GBP, amount: 40.0 }, // 4000 cents = 40.00 GBP
              quantity: 1, // Selling 1 share
              filledAt: new Date(),
              settlementDate: new Date()
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash state
        const initialPortfolio = await Portfolio.findById(portfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.GBP!.available;
        const initialSettledCash = initialPortfolio!.cash.GBP!.settled;

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

        // Verify no remainder cash was returned, but regular sell cash flow happened
        // For sell orders: available cash increases by 40 GBP (4000 cents), settled stays same
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash.GBP!.available).toBe(Decimal.add(initialAvailableCash, 40).toNumber()); // Regular sell cash
        expect(updatedPortfolio!.cash.GBP!.settled).toBe(initialSettledCash); // No remainder cash
      });
    });

    describe("when there is a rebalance transaction with remainder on an order", () => {
      it("should return remainder as cash when rebalance buy order is matched", async () => {
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 100, reserved: 0, settled: 100 } }
        });

        const rebalanceTransaction = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          rebalanceStatus: "NotStarted"
        });

        const order = await buildOrder({
          providers: {
            wealthkernel: { id: "wk-rebalance-buy-order", status: "Pending", submittedAt: new Date() }
          },
          consideration: {
            currency: "GBP",
            originalAmount: 5000,
            amountSubmitted: 5000,
            amount: 5000 // Initially same as amount submitted but will be updated to 4800 due to WK response
          },
          side: "Buy",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          transaction: rebalanceTransaction.id
        });

        (rebalanceTransaction as any).orders = [order];
        await rebalanceTransaction.save();

        // Mock WealthKernel service to return order data with fills
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-rebalance-buy-order",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: 1,
              baseExchangeRate: 1,
              price: { currency: CurrencyEnum.GBP, amount: 4800 },
              consideration: { currency: CurrencyEnum.GBP, amount: 48.0 }, // 4800 cents = 48.00 GBP
              quantity: 1,
              filledAt: new Date(),
              settlementDate: new Date()
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash amounts
        const initialPortfolio = await Portfolio.findById(portfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.GBP!.available;
        const initialSettledCash = initialPortfolio!.cash.GBP!.settled;

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

        // Verify remainder cash was returned
        // Remainder = 5000 - 4800 = 200 cents = 2.00 GBP
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash.GBP!.available).toBe(Decimal.add(initialAvailableCash, 2.0).toNumber());
        expect(updatedPortfolio!.cash.GBP!.settled).toBe(Decimal.add(initialSettledCash, 2.0).toNumber());
      });

      it("should not return cash when rebalance buy order has no remainder", async () => {
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 100, reserved: 0, settled: 100 } }
        });

        const rebalanceTransaction = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          rebalanceStatus: "NotStarted"
        });

        const order = await buildOrder({
          providers: {
            wealthkernel: { id: "wk-rebalance-no-remainder", status: "Pending", submittedAt: new Date() }
          },
          status: "Pending",
          side: "Buy",
          transaction: rebalanceTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 5000, // settled amount equals submitted amount
            originalAmount: 5000,
            amountSubmitted: 5000, // submitted amount
            currency: "GBP"
          }
        });

        (rebalanceTransaction as any).orders = [order];
        await rebalanceTransaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-rebalance-no-remainder",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy"
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash state
        const initialPortfolio = await Portfolio.findById(portfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.GBP!.available;
        const initialSettledCash = initialPortfolio!.cash.GBP!.settled;

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

        // Verify no remainder cash was returned (cash should remain the same)
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash.GBP!.available).toBe(initialAvailableCash);
        expect(updatedPortfolio!.cash.GBP!.settled).toBe(initialSettledCash);
      });

      it("should handle remainder cash return in different currencies for rebalance orders", async () => {
        const eurUser = await buildUser({ currency: "EUR" });
        const eurPortfolio = await buildPortfolio({
          owner: eurUser.id,
          providers: { wealthkernel: { id: "portfolio-id-eur", status: "Active" } },
          cash: { EUR: { available: 100, reserved: 0, settled: 100 } }
        });
        await buildSubscription({ owner: eurUser.id });

        const eurRebalanceTransaction = await buildRebalanceTransaction({
          owner: eurUser.id,
          portfolio: eurPortfolio.id,
          rebalanceStatus: "NotStarted"
        });

        const eurOrder = await buildOrder({
          providers: {
            wealthkernel: {
              id: "wk-rebalance-eur-order",
              status: "Pending",
              submittedAt: new Date(),
              baseExchangeRate: 1.15
            }
          },
          consideration: {
            currency: "EUR",
            originalAmount: 10000,
            amountSubmitted: 10000,
            amount: 9750 // Remainder = 10000 - 9750 = 250 cents = 2.50 EUR
          },
          side: "Buy",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          transaction: eurRebalanceTransaction.id
        });

        (eurRebalanceTransaction as any).orders = [eurOrder];
        await eurRebalanceTransaction.save();

        // Mock WealthKernel service to return order data with fills including baseExchangeRate
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "wk-rebalance-eur-order",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: 1.15,
              baseExchangeRate: 1.15,
              price: { currency: CurrencyEnum.USD, amount: 9750 },
              consideration: { currency: CurrencyEnum.EUR, amount: 97.5 }, // 9750 cents = 97.50 EUR
              quantity: 1,
              filledAt: new Date(),
              settlementDate: new Date()
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        // Get initial cash amounts
        const initialPortfolio = await Portfolio.findById(eurPortfolio.id);
        const initialAvailableCash = initialPortfolio!.cash.EUR!.available;
        const initialSettledCash = initialPortfolio!.cash.EUR!.settled;

        await OrderService.syncOrderByWealthkernelId(eurOrder.id, "Matched");

        // Verify remainder cash was returned in EUR
        // Remainder = 10000 - 9750 = 250 cents = 2.50 EUR
        const updatedPortfolio = await Portfolio.findById(eurPortfolio.id);
        expect(updatedPortfolio!.cash.EUR!.available).toBe(Decimal.add(initialAvailableCash, 2.5).toNumber());
        expect(updatedPortfolio!.cash.EUR!.settled).toBe(Decimal.add(initialSettledCash, 2.5).toNumber());
      });
    });
  });
});
