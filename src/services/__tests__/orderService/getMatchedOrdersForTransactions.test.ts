import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildChargeTransaction,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildSubscription,
  buildUser
} from "../../../tests/utils/generateModels";
import OrderService from "../../orderService";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";
import {
  AssetTransactionDocument,
  ChargeTransactionDocument,
  RebalanceTransactionDocument
} from "../../../models/Transaction";
import { OrderDocument } from "../../../models/Order";
import DateUtil from "../../../utils/dateUtil";

describe("OrderService.getMatchedOrdersForTransactions", () => {
  beforeAll(async () => {
    await connectDb("getMatchedOrdersForTransactions");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("when user has no orders", () => {
    let user: UserDocument;

    beforeEach(async () => {
      user = await buildUser();
    });

    it("should return empty array for AssetTransaction", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["AssetTransaction"]);
      expect(result).toEqual([]);
    });

    it("should return empty array for RebalanceTransaction", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["RebalanceTransaction"]);
      expect(result).toEqual([]);
    });

    it("should return empty array for ChargeTransaction", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["ChargeTransaction"]);
      expect(result).toEqual([]);
    });
  });

  describe("when user has asset transactions with matched orders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction: AssetTransactionDocument;
    let matchedOrder: OrderDocument;
    let pendingOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      matchedOrder = await buildOrder({
        status: "Matched",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });

      pendingOrder = await buildOrder({
        status: "Pending",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 5000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Pending",
            submittedAt: new Date()
          }
        }
      });

      assetTransaction.orders = [matchedOrder.id, pendingOrder.id];
      await assetTransaction.save();
    });

    it("should return only matched orders for AssetTransaction", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["AssetTransaction"]);
      expect(result).toHaveLength(1);
      expect(result[0]._id.toString()).toBe(matchedOrder.id);
    });
  });

  describe("when user has rebalance transactions with matched orders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let rebalanceTransaction: RebalanceTransactionDocument;
    let settledOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      rebalanceTransaction = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });

      settledOrder = await buildOrder({
        status: "Settled",
        transaction: rebalanceTransaction.id,
        side: "Sell",
        consideration: {
          amount: 8000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });
    });

    it("should return settled orders for RebalanceTransaction", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["RebalanceTransaction"]);
      expect(result).toHaveLength(1);
      expect(result[0]._id.toString()).toBe(settledOrder.id);
    });
  });

  describe("when user has charge transactions with matched orders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let chargeTransaction: ChargeTransactionDocument;
    let matchedOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });
      const subscription = await buildSubscription({ owner: user.id });

      chargeTransaction = await buildChargeTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        subscription: subscription.id,
        chargeMethod: "holdings",
        chargeType: "subscription",
        status: "Settled"
      });

      matchedOrder = await buildOrder({
        status: "Matched",
        transaction: chargeTransaction.id,
        side: "Sell",
        consideration: {
          amount: 200,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });
    });

    it("should return matched orders for ChargeTransaction", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["ChargeTransaction"]);
      expect(result).toHaveLength(1);
      expect(result[0]._id.toString()).toBe(matchedOrder.id);
    });
  });

  describe("when filtering by filledAfter date", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction1: AssetTransactionDocument;
    let assetTransaction2: AssetTransactionDocument;
    let oldOrder: OrderDocument;
    let recentOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      const oldDate = DateUtil.getDateOfDaysAgo(new Date(), 5);
      const recentDate = DateUtil.getDateOfDaysAgo(new Date(), 1);

      // Transaction created 5 days ago
      assetTransaction1 = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy",
        createdAt: oldDate
      });

      oldOrder = await buildOrder({
        status: "Matched",
        transaction: assetTransaction1.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: oldDate
      });

      assetTransaction1.orders = [oldOrder.id];
      await assetTransaction1.save();

      // Transaction created 1 day ago
      assetTransaction2 = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy",
        createdAt: recentDate
      });

      recentOrder = await buildOrder({
        status: "Matched",
        transaction: assetTransaction2.id,
        side: "Buy",
        consideration: {
          amount: 15000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: recentDate
      });

      assetTransaction2.orders = [recentOrder.id];
      await assetTransaction2.save();
    });

    it("should return all orders when no filledAfter date is provided", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["AssetTransaction"]);
      expect(result).toHaveLength(2);
    });

    it("should filter orders by filledAfter date", async () => {
      const filledAfter = DateUtil.getDateOfDaysAgo(new Date(), 2);
      const result = await OrderService.getMatchedOrdersForTransactions(
        user.id,
        ["AssetTransaction"],
        filledAfter
      );
      expect(result).toHaveLength(1);
      expect(result[0]._id.toString()).toBe(recentOrder.id);
    });

    it("should return empty array when filledAfter is very recent", async () => {
      const filledAfter = new Date();
      const result = await OrderService.getMatchedOrdersForTransactions(
        user.id,
        ["AssetTransaction"],
        filledAfter
      );
      expect(result).toHaveLength(0);
    });
  });

  describe("when querying multiple transaction types", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransaction: AssetTransactionDocument;
    let rebalanceTransaction: RebalanceTransactionDocument;
    let assetOrder: OrderDocument;
    let rebalanceOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      assetOrder = await buildOrder({
        status: "Matched",
        transaction: assetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });

      assetTransaction.orders = [assetOrder.id];
      await assetTransaction.save();

      rebalanceTransaction = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });

      rebalanceOrder = await buildOrder({
        status: "Settled",
        transaction: rebalanceTransaction.id,
        side: "Sell",
        consideration: {
          amount: 5000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });
    });

    it("should return orders from multiple transaction types", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, [
        "AssetTransaction",
        "RebalanceTransaction"
      ]);
      expect(result).toHaveLength(2);
      const orderIds = result.map((order) => order._id.toString());
      expect(orderIds).toContain(assetOrder.id);
      expect(orderIds).toContain(rebalanceOrder.id);
    });
  });

  describe("when transactions have different statuses", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let pendingAssetTransaction: AssetTransactionDocument;
    let settledAssetTransaction: AssetTransactionDocument;
    let pendingOrder: OrderDocument;
    let settledOrder: OrderDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });

      // Pending asset transaction (should be included)
      pendingAssetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending",
        portfolioTransactionCategory: "buy"
      });

      pendingOrder = await buildOrder({
        status: "Matched",
        transaction: pendingAssetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 10000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });

      pendingAssetTransaction.orders = [pendingOrder.id];
      await pendingAssetTransaction.save();

      // Settled asset transaction (should be included)
      settledAssetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      settledOrder = await buildOrder({
        status: "Matched",
        transaction: settledAssetTransaction.id,
        side: "Buy",
        consideration: {
          amount: 15000,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date()
          }
        },
        filledAt: new Date()
      });

      settledAssetTransaction.orders = [settledOrder.id];
      await settledAssetTransaction.save();
    });

    it("should return orders from both Pending and Settled asset transactions", async () => {
      const result = await OrderService.getMatchedOrdersForTransactions(user.id, ["AssetTransaction"]);
      expect(result).toHaveLength(2);
      const orderIds = result.map((order) => order._id.toString());
      expect(orderIds).toContain(pendingOrder.id);
      expect(orderIds).toContain(settledOrder.id);
    });
  });
});
