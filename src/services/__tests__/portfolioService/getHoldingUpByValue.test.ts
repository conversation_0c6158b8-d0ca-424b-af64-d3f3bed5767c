import { faker } from "@faker-js/faker";
import Decimal from "decimal.js";
import { currenciesConfig, entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { ProviderEnum } from "../../../configs/providersConfig";
import {
  buildAssetTransaction,
  buildChargeTransaction,
  buildHoldingDTO,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildSubscription,
  buildUser
} from "../../../tests/utils/generateModels";
import PortfolioService from "../../portfolioService";
import { RewardDocument } from "../../../models/Reward";
import { PortfolioDocument, PortfolioModeEnum } from "../../../models/Portfolio";
import { DividendTransactionDocument } from "../../../models/Transaction";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("PortfolioService.getHoldingUpByValue", () => {
  beforeAll(async () => connectDb("getHoldingUpByValue"));
  afterAll(async () => closeDb());
  afterEach(async () => await clearDb());

  describe("when the asset has asset transactions, rebalances, charges & rewards", () => {
    const HOLDING_QUANTITY = 0.05;
    const PRICE = 50;
    const ASSET_TRANSACTION_AMOUNT = 1100;
    const ORDER_ASSET_BUY_AMOUNT = 1000;
    const ORDER_ASSET_SELL_AMOUNT = 1000;
    const ORDER_ASSET_BUY_AMOUNT_POST_FEES = 1000 - 50;
    const REBALANCE_BUY_AMOUNT = 2000;
    const REBALANCE_SELL_AMOUNT = 500;
    const CHARGE_SELL_AMOUNT = 500;
    const REWARD_AMOUNT = 100;
    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        currency: "GBP" as currenciesConfig.MainCurrencyType
      });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY, { price: PRICE })],
        currency: "GBP" as currenciesConfig.MainCurrencyType
      });
      await portfolio.populate([
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);

      // Settled transaction (eligible to upBy calculation) - will use original amount
      // for one order because we have execution spread fee and post-fees amount for the
      // order with 0 execution spread.
      const settledTransactionWithBuyOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: ORDER_ASSET_BUY_AMOUNT,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const matchedBuyOrderPreFeesAmount = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ORDER_ASSET_BUY_AMOUNT,
          amount: ORDER_ASSET_BUY_AMOUNT - 1
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: 1,
            currency: "GBP"
          }
        }
      });
      const matchedBuyOrderPostFeesAmount = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ORDER_ASSET_BUY_AMOUNT,
          amount: ORDER_ASSET_BUY_AMOUNT_POST_FEES
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 50,
            currency: "GBP"
          },
          executionSpread: {
            amount: 0,
            currency: "GBP"
          }
        }
      });
      settledTransactionWithBuyOrder.orders = [matchedBuyOrderPreFeesAmount.id, matchedBuyOrderPostFeesAmount.id];

      await settledTransactionWithBuyOrder.save();
      await settledTransactionWithBuyOrder.populate("orders");

      // Settled transaction (eligible to upBy calculation)
      const settledTransactionWithSellOrder = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const matchedSellOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          amount: ORDER_ASSET_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-sell-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      settledTransactionWithSellOrder.orders = [matchedSellOrder.id];
      await settledTransactionWithSellOrder.save();
      await settledTransactionWithSellOrder.populate("orders");

      // Pending transaction (NOT eligible to upBy calculation)
      const pendingTransactionWithBuyOrder = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending"
      });
      const pendingBuyOrder = await buildOrder({
        status: "Pending",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ASSET_TRANSACTION_AMOUNT,
          amount: ASSET_TRANSACTION_AMOUNT - 1
        }
      });
      pendingTransactionWithBuyOrder.orders = [pendingBuyOrder.id];
      await pendingTransactionWithBuyOrder.save();
      await pendingTransactionWithBuyOrder.populate("orders");

      // Settled rebalance transaction (with a buy order) - will use original amount
      // because we have execution spread fee
      const settledRebalanceTransactionWithBuyOrder = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledRebalanceTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: REBALANCE_BUY_AMOUNT,
          amount: REBALANCE_BUY_AMOUNT - 1
        },
        providers: {
          wealthkernel: {
            id: "matched-rebalance-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: 1,
            currency: "GBP"
          }
        }
      });
      await settledRebalanceTransactionWithBuyOrder.populate("orders");

      // Another settled rebalance transaction (with a sell order)
      const settledRebalanceTransactionWithSellOrder = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledRebalanceTransactionWithSellOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          amount: REBALANCE_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-rebalance-sell-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      await settledRebalanceTransactionWithSellOrder.populate("orders");

      const subscription = await buildSubscription({
        category: "FeeBasedSubscription",
        active: true,
        price: "free_monthly",
        owner: user.id
      });

      // Pending charge transaction with unmatched orders (should not affect calculation)
      const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        subscription: subscription.id,
        chargeMethod: "combined",
        status: "Pending"
      });
      await buildOrder({
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: chargeTransactionWithUnmatchedOrders.id,
        side: "Sell",
        status: "Pending",
        consideration: {
          currency: "GBP",
          originalAmount: CHARGE_SELL_AMOUNT,
          amount: CHARGE_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "open-charge-buy-order-id",
            status: "Open",
            submittedAt: new Date()
          }
        }
      });
      await chargeTransactionWithUnmatchedOrders.populate("orders");

      // Pending charge transaction with matched orders
      const chargeTransactionWithMatchedOrders = await buildChargeTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        subscription: subscription.id,
        chargeMethod: "combined",
        status: "PendingWealthkernelCharge"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: chargeTransactionWithMatchedOrders.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          originalAmount: CHARGE_SELL_AMOUNT,
          amount: CHARGE_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-charge-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      await chargeTransactionWithMatchedOrders.populate("orders");

      // Settled reward (eligible to upBy calculation)
      const settledReward = await buildReward({
        targetUser: user.id,
        asset: "equities_eu",
        consideration: {
          currency: "GBP",
          amount: REWARD_AMOUNT,
          orderAmount: REWARD_AMOUNT,
          bonusAmount: REWARD_AMOUNT
        },
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
        }
      });

      await buildReward({
        targetUser: user.id,
        status: "Pending",
        asset: "equities_eu",
        consideration: {
          currency: "GBP",
          amount: REWARD_AMOUNT,
          orderAmount: REWARD_AMOUNT,
          bonusAmount: REWARD_AMOUNT
        }
      });

      transactions.rewards = [settledReward];
    });

    it("should properly calculate up by value for asset 'equities_eu'", async () => {
      const holding = portfolio.holdings[0];
      const holdingUpBy = await PortfolioService.getHoldingUpByValue(portfolio, holding, transactions);
      expect(holdingUpBy).toBe(
        new Decimal(HOLDING_QUANTITY)
          .mul(PRICE)
          .plus(Decimal.div(ORDER_ASSET_SELL_AMOUNT, 100).toNumber())
          .plus(Decimal.div(REBALANCE_SELL_AMOUNT, 100).toNumber())
          .plus(Decimal.div(CHARGE_SELL_AMOUNT, 100).toNumber())
          // for both transactions we should take into account the order amount pre-fees for buys
          .minus(Decimal.div(2 * ORDER_ASSET_BUY_AMOUNT, 100).toNumber())
          .minus(Decimal.div(REWARD_AMOUNT, 100).toNumber())
          .minus(Decimal.div(REBALANCE_BUY_AMOUNT, 100).toNumber())
          .toNumber()
      );
    });
  });

  describe("when the asset was fully sold, and then bought again", () => {
    const HOLDING_QUANTITY = 0.05;
    const PRICE = 50;
    const BEFORE_SELLING_REWARD_AMOUNT = 100;
    const BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT = 1000;
    const BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT = 1100; //cover both buy order and reward
    const AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT = 2000;
    const AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT = 500;
    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        currency: "GBP" as currenciesConfig.MainCurrencyType
      });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY, { price: PRICE })],
        currency: "GBP" as currenciesConfig.MainCurrencyType
      });
      await portfolio.populate([
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);

      // Settled reward (eligible to upBy calculation)
      const beforeFullSellSettledReward = await buildReward({
        targetUser: user.id,
        asset: "equities_eu",
        consideration: {
          currency: "GBP",
          amount: BEFORE_SELLING_REWARD_AMOUNT,
          orderAmount: BEFORE_SELLING_REWARD_AMOUNT,
          bonusAmount: BEFORE_SELLING_REWARD_AMOUNT
        },
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
        }
      });

      await buildReward({
        targetUser: user.id,
        status: "Pending",
        asset: "equities_eu",
        consideration: {
          currency: "GBP",
          amount: BEFORE_SELLING_REWARD_AMOUNT,
          orderAmount: BEFORE_SELLING_REWARD_AMOUNT,
          bonusAmount: BEFORE_SELLING_REWARD_AMOUNT
        }
      });

      const beforeFullSellSettledTransactionWithBuyOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const beforeFullSellBuyOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT,
          amount: BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      beforeFullSellSettledTransactionWithBuyOrder.orders = [beforeFullSellBuyOrder.id];

      await beforeFullSellSettledTransactionWithBuyOrder.save();
      await beforeFullSellSettledTransactionWithBuyOrder.populate("orders");

      const beforeFullSellSettledTransactionWithSellOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "sell",
        originalInvestmentAmount: BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const beforeFullSellSellOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          originalAmount: BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT,
          amount: BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      beforeFullSellSettledTransactionWithSellOrder.orders = [beforeFullSellSellOrder.id];

      await beforeFullSellSettledTransactionWithSellOrder.save();
      await beforeFullSellSettledTransactionWithSellOrder.populate("orders");

      const afterFullSellSettledTransactionWithBuyOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const afterFullSellBuyOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT,
          amount: AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      afterFullSellSettledTransactionWithBuyOrder.orders = [afterFullSellBuyOrder.id];

      await afterFullSellSettledTransactionWithBuyOrder.save();
      await afterFullSellSettledTransactionWithBuyOrder.populate("orders");

      const afterFullSellSettledTransactionWithSellOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "sell",
        originalInvestmentAmount: AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const afterFullSellSellOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          originalAmount: AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT,
          amount: AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      afterFullSellSettledTransactionWithSellOrder.orders = [afterFullSellSellOrder.id];

      await afterFullSellSettledTransactionWithSellOrder.save();
      await afterFullSellSettledTransactionWithSellOrder.populate("orders");

      transactions.rewards = [beforeFullSellSettledReward];
    });

    it("should properly calculate up by value for asset 'equities_eu' ignoring the orders before the full sell", async () => {
      const holding = portfolio.holdings[0];
      const holdingUpBy = await PortfolioService.getHoldingUpByValue(portfolio, holding, transactions);
      expect(holdingUpBy).toBe(
        new Decimal(HOLDING_QUANTITY)
          .mul(PRICE)
          .plus(AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT / 100)
          .minus(AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT / 100)
          .toNumber()
      );
    });
  });

  describe("when the asset has deprecated isin", () => {
    const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro_deprecated_1";
    const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro";
    const HOLDING_QUANTITY = 0.05;
    const PRICE = 50;
    const ASSET_TRANSACTION_AMOUNT = 1100;
    const ORDER_ASSET_BUY_AMOUNT = 1000;
    const ORDER_ASSET_SELL_AMOUNT = 1000;
    const ORDER_ASSET_BUY_AMOUNT_POST_FEES = 1000 - 50;
    const REBALANCE_BUY_AMOUNT = 2000;
    const REBALANCE_SELL_AMOUNT = 500;
    const CHARGE_SELL_AMOUNT = 500;
    const REWARD_AMOUNT = 100;
    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        currency: "GBP" as currenciesConfig.MainCurrencyType
      });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, ACTIVE_ASSET_ID, HOLDING_QUANTITY, { price: PRICE })],
        currency: "GBP" as currenciesConfig.MainCurrencyType
      });
      await portfolio.populate([
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);

      // Settled transaction (eligible to upBy calculation) - will use original amount
      // for one order because we have execution spread fee and post-fees amount for the
      // order with 0 execution spread.
      const settledTransactionWithBuyOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: ORDER_ASSET_BUY_AMOUNT,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const matchedBuyOrderPreFeesAmount = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ORDER_ASSET_BUY_AMOUNT,
          amount: ORDER_ASSET_BUY_AMOUNT - 1
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: 1,
            currency: "GBP"
          }
        }
      });
      const matchedBuyOrderPostFeesAmount = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ORDER_ASSET_BUY_AMOUNT,
          amount: ORDER_ASSET_BUY_AMOUNT_POST_FEES
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 50,
            currency: "GBP"
          },
          executionSpread: {
            amount: 0,
            currency: "GBP"
          }
        }
      });
      settledTransactionWithBuyOrder.orders = [matchedBuyOrderPreFeesAmount.id, matchedBuyOrderPostFeesAmount.id];

      await settledTransactionWithBuyOrder.save();
      await settledTransactionWithBuyOrder.populate("orders");

      // Settled transaction (eligible to upBy calculation)
      const settledTransactionWithSellOrder = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const matchedSellOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          amount: ORDER_ASSET_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-sell-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      settledTransactionWithSellOrder.orders = [matchedSellOrder.id];
      await settledTransactionWithSellOrder.save();
      await settledTransactionWithSellOrder.populate("orders");

      // Pending transaction (NOT eligible to upBy calculation)
      const pendingTransactionWithBuyOrder = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending"
      });
      const pendingBuyOrder = await buildOrder({
        status: "Pending",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ASSET_TRANSACTION_AMOUNT,
          amount: ASSET_TRANSACTION_AMOUNT - 1
        }
      });
      pendingTransactionWithBuyOrder.orders = [pendingBuyOrder.id];
      await pendingTransactionWithBuyOrder.save();
      await pendingTransactionWithBuyOrder.populate("orders");

      // Settled rebalance transaction (with a buy order) - will use original amount
      // because we have execution spread fee
      const settledRebalanceTransactionWithBuyOrder = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: settledRebalanceTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: REBALANCE_BUY_AMOUNT,
          amount: REBALANCE_BUY_AMOUNT - 1
        },
        providers: {
          wealthkernel: {
            id: "matched-rebalance-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: 1,
            currency: "GBP"
          }
        }
      });
      await settledRebalanceTransactionWithBuyOrder.populate("orders");

      // Another settled rebalance transaction (with a sell order)
      const settledRebalanceTransactionWithSellOrder = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: settledRebalanceTransactionWithSellOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          amount: REBALANCE_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-rebalance-sell-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      await settledRebalanceTransactionWithSellOrder.populate("orders");

      const subscription = await buildSubscription({
        category: "FeeBasedSubscription",
        active: true,
        price: "free_monthly",
        owner: user.id
      });

      // Pending charge transaction with unmatched orders (should not affect calculation)
      const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        subscription: subscription.id,
        chargeMethod: "combined",
        status: "Pending"
      });
      await buildOrder({
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: chargeTransactionWithUnmatchedOrders.id,
        side: "Sell",
        status: "Pending",
        consideration: {
          currency: "GBP",
          originalAmount: CHARGE_SELL_AMOUNT,
          amount: CHARGE_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "open-charge-buy-order-id",
            status: "Open",
            submittedAt: new Date()
          }
        }
      });
      await chargeTransactionWithUnmatchedOrders.populate("orders");

      // Pending charge transaction with matched orders
      const chargeTransactionWithMatchedOrders = await buildChargeTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        subscription: subscription.id,
        chargeMethod: "combined",
        status: "PendingWealthkernelCharge"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
        transaction: chargeTransactionWithMatchedOrders.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          originalAmount: CHARGE_SELL_AMOUNT,
          amount: CHARGE_SELL_AMOUNT
        },
        providers: {
          wealthkernel: {
            id: "matched-charge-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        }
      });
      await chargeTransactionWithMatchedOrders.populate("orders");

      // Settled reward (eligible to upBy calculation)
      const settledReward = await buildReward({
        targetUser: user.id,
        asset: ACTIVE_ASSET_ID,
        consideration: {
          currency: "GBP",
          amount: REWARD_AMOUNT,
          orderAmount: REWARD_AMOUNT,
          bonusAmount: REWARD_AMOUNT
        },
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
        }
      });

      await buildReward({
        targetUser: user.id,
        status: "Pending",
        asset: ACTIVE_ASSET_ID,
        consideration: {
          currency: "GBP",
          amount: REWARD_AMOUNT,
          orderAmount: REWARD_AMOUNT,
          bonusAmount: REWARD_AMOUNT
        }
      });

      transactions.rewards = [settledReward];
    });

    it("should calculate the up by value", async () => {
      const holding = portfolio.holdings[0];
      const holdingUpBy = await PortfolioService.getHoldingUpByValue(portfolio, holding, transactions);
      expect(holdingUpBy).toBe(
        new Decimal(HOLDING_QUANTITY)
          .mul(PRICE)
          .plus(ORDER_ASSET_SELL_AMOUNT / 100)
          .plus(REBALANCE_SELL_AMOUNT / 100)
          .plus(CHARGE_SELL_AMOUNT / 100)
          // for both transactions we should take into account the order amount pre-fees for buys
          .minus((2 * ORDER_ASSET_BUY_AMOUNT) / 100)
          .minus(REWARD_AMOUNT / 100)
          .minus(REBALANCE_BUY_AMOUNT / 100)
          .toNumber()
      );
    });
  });

  describe("when the asset is first bought and has FX fees and Real Time Execution fees", () => {
    const PRICE = 50;
    const ORDER_ASSET_BUY_AMOUNT_PRE_FEES = 1000;
    const REAL_TIME_EXECUTION_FEE = 100;
    const FX_FEE = 10;
    const ORDER_ASSET_BUY_AMOUNT_POST_FEES = ORDER_ASSET_BUY_AMOUNT_PRE_FEES - REAL_TIME_EXECUTION_FEE - FX_FEE;
    const HOLDING_QUANTITY_AFTER_BUY = ORDER_ASSET_BUY_AMOUNT_POST_FEES / (PRICE * 100);

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        currency: "EUR" as currenciesConfig.MainCurrencyType
      });
      await buildSubscription({
        category: "FeeBasedSubscription",
        active: true,
        price: "free_monthly",
        owner: user.id
      });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY_AFTER_BUY, { price: PRICE })],
        currency: "EUR" as currenciesConfig.MainCurrencyType
      });
      await portfolio.populate([
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);

      // Settled transaction (eligible to upBy calculation)
      const settledTransactionWithBuyOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: ORDER_ASSET_BUY_AMOUNT_PRE_FEES,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const matchedBuyOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledTransactionWithBuyOrder.id,
        side: "Buy",
        consideration: {
          currency: "GBP",
          originalAmount: ORDER_ASSET_BUY_AMOUNT_PRE_FEES,
          amount: ORDER_ASSET_BUY_AMOUNT_POST_FEES
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: FX_FEE / 100,
            currency: "GBP"
          },
          realtimeExecution: {
            amount: REAL_TIME_EXECUTION_FEE / 100,
            currency: "GBP"
          }
        }
      });
      settledTransactionWithBuyOrder.orders = [matchedBuyOrder.id];

      await settledTransactionWithBuyOrder.save();
      await settledTransactionWithBuyOrder.populate("orders");
    });

    it("should properly calculate up by value for asset 'equities_eu' taking into account the fx fee, but not the real time execution fee", async () => {
      const holding = portfolio.holdings[0];
      const holdingUpBy = await PortfolioService.getHoldingUpByValue(portfolio, holding, transactions);
      expect(holdingUpBy).toBe(
        new Decimal(HOLDING_QUANTITY_AFTER_BUY)
          .mul(PRICE)
          // For buys we use the amount pre-fees, but remove the real time commission fee
          .minus(Decimal.sub(ORDER_ASSET_BUY_AMOUNT_PRE_FEES, REAL_TIME_EXECUTION_FEE).div(100).toNumber())
          .toNumber()
      );
    });
  });

  describe("when the asset is sold and has FX fees and Real Time Execution fees", () => {
    const PRICE = 50;
    const HOLDING_QUANTITY = 1;
    const ORDER_ASSET_SELL_AMOUNT_PRE_FEES = 1000;
    const REAL_TIME_EXECUTION_FEE = 100;
    const FX_FEE = 10;
    const ORDER_ASSET_SELL_AMOUNT_POST_FEES = ORDER_ASSET_SELL_AMOUNT_PRE_FEES - REAL_TIME_EXECUTION_FEE - FX_FEE;

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        currency: "EUR" as currenciesConfig.MainCurrencyType
      });
      await buildSubscription({
        category: "FeeBasedSubscription",
        active: true,
        price: "free_monthly",
        owner: user.id
      });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY, { price: PRICE })],
        currency: "EUR" as currenciesConfig.MainCurrencyType
      });
      await portfolio.populate([
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);

      // Settled transaction (eligible to upBy calculation)
      const settledTransactionWithSellOrder = await buildAssetTransaction({
        portfolioTransactionCategory: "sell",
        originalInvestmentAmount: ORDER_ASSET_SELL_AMOUNT_PRE_FEES,
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      const matchedSellOrder = await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledTransactionWithSellOrder.id,
        side: "Sell",
        consideration: {
          currency: "GBP",
          originalAmount: ORDER_ASSET_SELL_AMOUNT_PRE_FEES,
          amount: ORDER_ASSET_SELL_AMOUNT_POST_FEES
        },
        providers: {
          wealthkernel: {
            id: "matched-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: FX_FEE / 100,
            currency: "GBP"
          },
          realtimeExecution: {
            amount: REAL_TIME_EXECUTION_FEE / 100,
            currency: "GBP"
          }
        }
      });
      settledTransactionWithSellOrder.orders = [matchedSellOrder.id];

      await settledTransactionWithSellOrder.save();
      await settledTransactionWithSellOrder.populate("orders");
    });

    it("should properly calculate up by value for asset 'equities_eu' taking into account the fx_fee, but not the real time execution fee", async () => {
      const holding = portfolio.holdings[0];
      const holdingUpBy = await PortfolioService.getHoldingUpByValue(portfolio, holding, transactions);
      expect(holdingUpBy).toBe(
        new Decimal(HOLDING_QUANTITY)
          .mul(PRICE)
          // For sells we use the amount post-fees, but we add back the real time commission fee
          .plus(Decimal.add(ORDER_ASSET_SELL_AMOUNT_POST_FEES, REAL_TIME_EXECUTION_FEE).div(100).toNumber())
          .toNumber()
      );
    });
  });
});
