import { faker } from "@faker-js/faker";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildUser,
  buildStockSplitCorporateEvent
} from "../../../tests/utils/generateModels";
import PortfolioService from "../../portfolioService";
import { Order } from "../../../models/Order";
import { PortfolioDocument } from "../../../models/Portfolio";
import { KycStatusEnum, UserDocument } from "../../../models/User";
import DateUtil from "../../../utils/dateUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("PortfolioService.getAveragePricePerShare", () => {
  let user: UserDocument;
  let portfolio: PortfolioDocument;

  beforeAll(async () => {
    await connectDb("getAveragePricePerShare");
    user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
    portfolio = await buildPortfolio({ owner: user.id });
  });
  afterAll(async () => {
    jest.clearAllMocks();
    await closeDb();
  });
  afterEach(async () => await clearDb());

  it("should return 0 when there are no orders for the asset", async () => {
    const orders = await Order.find();
    expect(orders.length).toBe(0);
    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", portfolio, {
      rewards: []
    });
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 0,
      priceInSettlementCurrency: 0
    });
  });

  it("should return 0 when there are only pending orders for the asset", async () => {
    const pendingTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Pending"
    });
    pendingTransaction.orders = [
      await buildOrder({
        status: "Pending",
        providers: {
          wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
        },
        side: "Buy",
        transaction: pendingTransaction.id,
        isin: ASSET_CONFIG["equities_airbnb"]?.isin,
        quantity: 2
      })
    ];
    await pendingTransaction.save();
    await pendingTransaction.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", portfolio, {
      rewards: []
    });
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 0,
      priceInSettlementCurrency: 0
    });
  });

  it("should return 0 when there are no buy orders for the asset", async () => {
    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });
    assetTransaction.orders = [
      await buildOrder({
        status: "Matched",
        providers: {
          wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
        },
        filledAt: new Date(),
        side: "Sell",
        transaction: assetTransaction.id,
        isin: ASSET_CONFIG["equities_airbnb"]?.isin,
        quantity: 2
      })
    ];
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", portfolio, {
      rewards: []
    });
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 0,
      priceInSettlementCurrency: 0
    });
  });

  it("should return the average share price for matched buy orders", async () => {
    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1
        // here we intentionally leave exchange rate empty (it will use fallback of 1)
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_eu"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_eu", portfolio, {
      rewards: []
    });
    // see comment on top for explanation on 15
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 15,
      priceInSettlementCurrency: 15
    });
  });

  it("should take into account the traded currency", async () => {
    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 33 + 24 = 57 / 5 = 11.4

    // 3 * 11 + 2 * 12 / ( 3 + 2 ) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 * 1.1 = 11
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1.1
      },
      {
        // price => 40 / 2 * 1.2 = 12
        amount: 20 * 100,
        quantity: 2,
        exchangeRate: 1.2
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_airbnb", portfolio, {
      rewards: []
    });
    // see comment on top for explanation on $11.4
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 11.4,
      priceInSettlementCurrency: 10
    });
  });

  it("should take into account rewards", async () => {
    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      }
    ];

    const reward = await buildReward({
      status: "Settled",
      asset: "equities_eu",
      // price => 20 / 1 = 20
      consideration: {
        amount: 20 * 100,
        orderAmount: 20 * 100,
        bonusAmount: 20 * 100,
        currency: "GBP"
      },
      quantity: 1
      // here we intentionally leave exchange rate empty (it will use fallback of 1)
    });
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_eu"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_eu", portfolio, {
      rewards: [reward]
    });
    // see comment on top for explanation on 15
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 15,
      priceInSettlementCurrency: 15
    });
  });

  it("should take into account rebalance buy orders", async () => {
    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_eu"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    // Settled rebalance transaction (with a buy order) - will use original amount
    // because we have execution spread fee
    const settledRebalanceTransactionWithBuyOrder = await buildRebalanceTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      rebalanceStatus: "Settled"
    });
    await buildOrder({
      status: "Matched",
      isin: ASSET_CONFIG["equities_eu"].isin,
      transaction: settledRebalanceTransactionWithBuyOrder.id,
      side: "Buy",
      filledAt: new Date(),
      consideration: {
        currency: "GBP",
        originalAmount: 20 * 100,
        amount: 20 * 100
      },
      quantity: 1,
      providers: {
        wealthkernel: {
          id: "matched-rebalance-buy-order-id",
          status: "Matched",
          submittedAt: new Date()
        }
      },
      fees: {
        fx: {
          amount: 0,
          currency: "GBP"
        },
        executionSpread: {
          amount: 0,
          currency: "GBP"
        }
      }
    });
    await settledRebalanceTransactionWithBuyOrder.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_eu", portfolio, {
      rewards: []
    });
    // see comment on top for explanation on 15
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 15,
      priceInSettlementCurrency: 15
    });
  });

  it("should take into account orders with deprecated isin", async () => {
    const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro_deprecated_1";
    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1
        // here we intentionally leave exchange rate empty (it will use fallback of 1)
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const averagePrice = await PortfolioService.getAveragePricePerShare(DEPRECATED_ASSET_ID, portfolio, {
      rewards: []
    });
    // see comment on top for explanation on 15
    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 15,
      priceInSettlementCurrency: 15
    });
  });

  it("should take into account stock splits", async () => {
    const TODAY = new Date("2024-01-10");
    const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
    const SIX_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(TODAY, 6);
    const ONE_YEAR_AGO = DateUtil.getDateOfYearsAgo(TODAY, 1);
    const TWO_YEARS_AGO = DateUtil.getDateOfYearsAgo(TODAY, 2);
    const THREE_YEARS_AGO = DateUtil.getDateOfYearsAgo(TODAY, 3);

    // The timeline we simulate for this test is:
    // User bought one share of Apple (£10) three years ago
    // Stock split (1 -> 10) two years ago
    // User bought one share of Apple (£1) one year ago
    // Stock split (1 -> 2) six months ago
    // User bought one share of Apple (£0.5) yesterday

    Date.now = jest.fn(() => +TODAY);

    const [firstAssetTransaction, secondAssetTransaction, thirdAssetTransaction] = await Promise.all([
      buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      }),
      buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      }),
      buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      }),
      buildStockSplitCorporateEvent({
        asset: "equities_apple",
        splitRatio: "10.000000/1.000000", // 1 -> 10 split ratio
        date: TWO_YEARS_AGO
      }),
      buildStockSplitCorporateEvent({
        asset: "equities_apple",
        splitRatio: "2.000000/1.000000", // 1 -> 2 split ratio
        date: SIX_MONTHS_AGO
      })
    ]);

    firstAssetTransaction.orders = [
      await buildOrder({
        status: "Matched",
        providers: {
          wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
        },
        filledAt: THREE_YEARS_AGO,
        side: "Buy",
        transaction: firstAssetTransaction.id,
        isin: ASSET_CONFIG["equities_apple"]?.isin,
        consideration: {
          amountSubmitted: 1000,
          amount: 1000,
          currency: "GBP"
        },
        quantity: 1
      })
    ];

    secondAssetTransaction.orders = [
      await buildOrder({
        status: "Matched",
        providers: {
          wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
        },
        filledAt: ONE_YEAR_AGO,
        side: "Buy",
        transaction: secondAssetTransaction.id,
        isin: ASSET_CONFIG["equities_apple"]?.isin,
        consideration: {
          amountSubmitted: 100,
          amount: 100,
          currency: "GBP"
        },
        quantity: 1
      })
    ];

    thirdAssetTransaction.orders = [
      await buildOrder({
        status: "Matched",
        providers: {
          wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
        },
        filledAt: YESTERDAY,
        side: "Buy",
        transaction: thirdAssetTransaction.id,
        isin: ASSET_CONFIG["equities_apple"]?.isin,
        consideration: {
          amountSubmitted: 50,
          amount: 50,
          currency: "GBP"
        },
        quantity: 1
      })
    ];

    await Promise.all([firstAssetTransaction.save(), secondAssetTransaction.save(), thirdAssetTransaction.save()]);

    const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", portfolio, {
      rewards: []
    });

    expect(averagePrice).toMatchObject({
      priceInTradedCurrency: 0.5,
      priceInSettlementCurrency: 0.5
    });
  });
});
