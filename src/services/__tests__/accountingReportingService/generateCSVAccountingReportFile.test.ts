import "jest";
import fs from "fs";
import path from "path";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingReportingService } from "../../accountingReportingService";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../../external-services/accountingLedgerStorageService";
import DateUtil from "../../../utils/dateUtil";

describe("AccountingReportingService.generateCSVAccountingReportFile", () => {
  const REPORT_DATE_STR = "2025-06-17";
  const REPORT_DATE = new Date(REPORT_DATE_STR);
  const OUTPUT_DIR = path.join(__dirname, "test_output_accounting_reporting");

  beforeAll(async () => {
    Date.now = jest.fn(() => REPORT_DATE.getTime());
    await connectDb("generateCSVAccountingReportFile");
    await createSqliteDb();
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  });

  afterAll(async () => {
    await closeDb();
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
  });

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should generate a CSV report with details from ledger storage", async () => {
    const jsonDataPath = path.resolve(__dirname, "../data/end-to-end-ledger-entries.json");
    const jsonContent = fs.readFileSync(jsonDataPath, "utf-8");
    const reportData: Array<Omit<AccountingLedgerEntry, "id">> = JSON.parse(jsonContent);

    const ledgerEntriesToInsert: AccountingLedgerEntry[] = reportData.map((entry) => ({
      ...entry,
      article_date: REPORT_DATE_STR,
      reference_number: entry.reference_number === "" ? undefined : entry.reference_number,
      owner_id: "test-user-id",
      document_id: "test-document-id"
    }));

    await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntriesToInsert);

    const generatedReportPath = await AccountingReportingService.generateCSVAccountingReportFile({
      fromDate: DateUtil.getStartOfDay(REPORT_DATE),
      toDate: DateUtil.getEndOfDay(REPORT_DATE),
      outputDir: OUTPUT_DIR,
      uploadToCloud: false
    });

    expect(generatedReportPath).not.toBe("");
    expect(fs.existsSync(generatedReportPath)).toBe(true);

    const generatedCsv = fs.readFileSync(generatedReportPath, "utf-8");
    const generatedLines = generatedCsv.split("\n").filter((line) => line.trim() !== "");

    // Load expected CSV data for exact comparison
    const expectedCsvPath = path.resolve(__dirname, "../data/accounting-data.csv");
    const expectedCsv = fs.readFileSync(expectedCsvPath, "utf-8");
    const expectedLines = expectedCsv.split("\n").filter((line) => line.trim() !== "");

    // Verify we have the same number of lines
    expect(generatedLines.length).toBe(expectedLines.length);

    // Compare each line exactly
    for (let i = 0; i < expectedLines.length; i++) {
      expect(generatedLines[i]).toBe(expectedLines[i]);
    }
  });

  it("should create a CSV with headers only if no entries are found for the date", async () => {
    const dateWithNoEntries = new Date("2024-01-01");
    const reportPath = await AccountingReportingService.generateCSVAccountingReportFile({
      fromDate: DateUtil.getStartOfDay(dateWithNoEntries),
      toDate: DateUtil.getEndOfDay(dateWithNoEntries),
      outputDir: OUTPUT_DIR,
      uploadToCloud: false
    });

    // Read the generated CSV and check it contains only headers
    const generatedCsv = fs.readFileSync(reportPath, "utf-8");
    const lines = generatedCsv.split("\n").filter((line) => line.trim() !== "");

    // Should have exactly 1 line (header only)
    expect(lines.length).toBe(1);
    expect(lines[0]).toBe("article_index,invoice_number,article_date,description,amount,side,account_code");
  });
});
