import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildContentEntry,
  buildNotification,
  buildNotificationSettings,
  buildUser
} from "../../tests/utils/generateModels";
import NotificationService from "../notificationService";
import { UserDocument, UserPopulationFieldsEnum } from "../../models/User";
import { Notification, NotificationDocument, NotificationMetadataTypeEnum } from "../../models/Notification";
import {
  LearningNotificationEventEnum,
  TransactionalNotificationEventEnum
} from "../../event-handlers/notificationEvents";
import {
  AppNotificationSettingEnum,
  AppNotificationSettings,
  EmailNotificationSettingEnum,
  EmailNotificationSettings
} from "../../models/NotificationSettings";
import {
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument
} from "../../models/ContentEntry";
import OneSignalService from "../../external-services/onesignalService";

describe("NotificationService", () => {
  beforeAll(async () => await connectDb("NotificationService"));
  afterAll(async () => await closeDb());

  describe("createAppNotification", () => {
    describe("when notification is NOT allowed because user has turned off device notifications", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        owner = await buildUser();

        // Build an all-allowed notification settings object but with device notifications disabled.
        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: false,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
                [AppNotificationSettingEnum.QUICK_TAKE]: true,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          }
        });

        await NotificationService.createAppNotification(
          owner.id,
          { notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED },
          { sendImmediately: true }
        );
      });
      afterAll(async () => await clearDb());

      it("should create a notification document in pending status", async () => {
        const notification = await Notification.findOne();

        expect(notification).toMatchObject(
          expect.objectContaining({
            status: "Pending"
          })
        );
      });

      it("should NOT send the notification through OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when notification is NOT allowed because user has turned off that particular setting", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        owner = await buildUser();

        // Build an all-allowed notification settings object but with device notifications disabled.
        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: false,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
                [AppNotificationSettingEnum.QUICK_TAKE]: true,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          }
        });

        await NotificationService.createAppNotification(
          owner.id,
          { notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED },
          { sendImmediately: true }
        );
      });
      afterAll(async () => await clearDb());

      it("should create a notification document in pending status", async () => {
        const notification = await Notification.findOne();

        expect(notification).toMatchObject(
          expect.objectContaining({
            status: "Pending"
          })
        );
      });

      it("should NOT send the notification through OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when notification is allowed and it should be sent immediately", () => {
      let owner: UserDocument;

      const TODAY = new Date("2022-08-04T11:00:00Z");

      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();

        // Build an all-allowed notification settings object.
        await buildNotificationSettings({
          owner: owner.id
        });

        await NotificationService.createAppNotification(
          owner.id,
          {
            notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED,
            properties: new Map(Object.entries({ amount: "100" }))
          },
          { sendImmediately: true }
        );
      });
      afterAll(async () => await clearDb());

      it("should create a notification document in sent status", async () => {
        const notification = await Notification.findOne();

        expect(notification).toMatchObject({
          status: "Sent",
          sentAt: TODAY
        });
      });

      it("should send the notification through OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledWith(
          [owner.id],
          TransactionalNotificationEventEnum.ORDER_SETTLED,
          { amount: "100" },
          {}
        );
      });
    });

    describe("when notification should not be sent immediately", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        owner = await buildUser();

        // Build an all-allowed notification settings object.
        await buildNotificationSettings({
          owner: owner.id
        });

        await NotificationService.createAppNotification(owner.id, {
          notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED
        });
      });
      afterAll(async () => await clearDb());

      it("should create a notification document in pending status", async () => {
        const notification = await Notification.findOne();

        expect(notification).toMatchObject({
          status: "Pending"
        });
      });

      it("should not send the notification through OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });
  });

  describe("createContentEntryNotificationForUsers", () => {
    describe("when the user does not have that setting enabled", () => {
      let user: UserDocument;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: "passed" });

        await buildNotificationSettings({
          owner: user.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: false,
                [AppNotificationSettingEnum.QUICK_TAKE]: true,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });

        contentEntry = await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          title: "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Bitcoin",
          shouldNotifyUsers: true
        });

        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
      });

      it("should NOT create notification for user", async () => {
        const notification = await Notification.findOne({ owner: user.id });
        expect(notification).toBeNull();
      });
    });

    describe("when the content entry should not notify users", () => {
      let user: UserDocument;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: "passed" });

        // User has analysis notifications enabled.
        await buildNotificationSettings({ owner: user.id });

        contentEntry = await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          title:
            "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Bitcoin" + ` - ${faker.number.int()}`,
          shouldNotifyUsers: false
        });

        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
      });

      it("should NOT create notification for user", async () => {
        const notification = await Notification.findOne({ owner: user.id });
        expect(notification).toBeNull();
      });
    });

    describe("when there is a user that is not KYC passed", () => {
      let user: UserDocument;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: "pending" });

        // User has analysis notifications enabled.
        await buildNotificationSettings({ owner: user.id });

        contentEntry = await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          title:
            "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Bitcoin" + ` - ${faker.number.int()}`,
          shouldNotifyUsers: true
        });

        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
      });

      it("should NOT create notification for user", async () => {
        const notifications = await Notification.find({ owner: user.id });
        expect(notifications.length).toBe(0);
      });
    });

    describe("when there is a user that should get the notification", () => {
      let user: UserDocument;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: "passed", deviceTokens: { ios: faker.string.uuid() } });

        // User has analysis notifications enabled.
        await buildNotificationSettings({ owner: user.id });

        contentEntry = await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          title:
            "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Bitcoin" + ` - ${faker.number.int()}`,
          shouldNotifyUsers: true
        });

        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
      });

      it("should create notification for user", async () => {
        const notification = await Notification.findOne({ owner: user.id });
        expect(notification.toObject()).toEqual(
          expect.objectContaining({
            status: "Pending",
            method: "app",
            notifyAt: contentEntry.publishAt,
            providers: {
              onesignal: {
                notificationId: LearningNotificationEventEnum.ANALYSIS_CREATED,
                properties: new Map(
                  Object.entries({
                    title: contentEntry.title
                  })
                ),
                metadata: {
                  documentId: contentEntry.id,
                  notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
                }
              }
            }
          })
        );
      });
    });

    describe("when there is a user that should get the notification and the method is called twice", () => {
      let user: UserDocument;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: "passed", deviceTokens: { ios: faker.string.uuid() } });

        // User has analysis notifications enabled.
        await buildNotificationSettings({ owner: user.id });

        contentEntry = await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          title:
            "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Bitcoin" + ` - ${faker.number.int()}`,
          shouldNotifyUsers: true
        });

        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
      });

      it("should create only one notification for user", async () => {
        const notifications = await Notification.find({ owner: user.id });
        expect(notifications.length).toBe(1);
      });
    });

    describe("when the content entry is a learning guide", () => {
      let user: UserDocument;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: "passed", deviceTokens: { ios: faker.string.uuid() } });

        // User has learning guide notifications enabled
        await buildNotificationSettings({ owner: user.id });

        contentEntry = await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.GUIDE,
          category: ContentEntryCategoryEnum.GUIDES,
          title: "Understanding Stock Market Basics",
          shouldNotifyUsers: true
        });

        await NotificationService.createContentEntryNotificationForUsers(contentEntry);
      });

      afterAll(async () => await clearDb());

      it("should create notification for user with correct learning guide event type", async () => {
        const notification = await Notification.findOne({ owner: user.id });
        expect(notification?.toObject()).toEqual(
          expect.objectContaining({
            status: "Pending",
            method: "app",
            notifyAt: contentEntry.publishAt,
            providers: {
              onesignal: {
                notificationId: LearningNotificationEventEnum.GUIDE_CREATED,
                properties: new Map(
                  Object.entries({
                    title: contentEntry.title
                  })
                ),
                metadata: {
                  documentId: contentEntry.id,
                  notificationType: NotificationMetadataTypeEnum.LEARNING_GUIDE
                }
              }
            }
          })
        );
      });
    });
  });

  describe("markSameTypeAndDayContentNotificationsAsSkipped", () => {
    describe("when a notification is sent", () => {
      let owner: UserDocument;
      let differentUser: UserDocument;
      const notifyAt = new Date("2022-08-04T11:00:00Z");

      beforeAll(async () => {
        owner = await buildUser();
        differentUser = await buildUser();

        // Create a sent learning notification
        const sentLearningNotification = await Notification.create({
          owner: owner.id,
          status: "Sent",
          method: "app",
          notifyAt,
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.GUIDE_CREATED,
              properties: new Map()
            }
          }
        });

        // Create a sent transactional notification
        const sentTransactionalNotification = await Notification.create({
          owner: owner.id,
          status: "Sent",
          method: "app",
          notifyAt,
          providers: {
            onesignal: {
              notificationId: TransactionalNotificationEventEnum.DEPOSIT_AVAILABLE,
              properties: new Map()
            }
          }
        });

        // Create pending notifications for the same day and learning type
        await Notification.create([
          {
            owner: owner.id,
            status: "Pending",
            method: "app",
            notifyAt: new Date("2022-08-04T14:00:00Z"),
            providers: {
              onesignal: {
                notificationId: LearningNotificationEventEnum.GUIDE_CREATED,
                properties: new Map()
              }
            }
          },
          {
            owner: owner.id,
            status: "Pending",
            method: "app",
            notifyAt: new Date("2022-08-04T16:00:00Z"),
            providers: {
              onesignal: {
                notificationId: LearningNotificationEventEnum.GUIDE_CREATED,
                properties: new Map()
              }
            }
          }
        ]);

        // Create a pending notification for a different user
        await Notification.create({
          owner: differentUser.id,
          status: "Pending",
          method: "app",
          notifyAt: new Date("2022-08-04T14:00:00Z"),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.GUIDE_CREATED,
              properties: new Map()
            }
          }
        });

        // Create a pending notification for a different day
        await Notification.create({
          owner: owner.id,
          status: "Pending",
          method: "app",
          notifyAt: new Date("2022-08-05T11:00:00Z"),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.GUIDE_CREATED,
              properties: new Map()
            }
          }
        });

        // Create a pending notification with same transactional type
        await Notification.create({
          owner: owner.id,
          status: "Pending",
          method: "app",
          notifyAt: new Date("2022-08-04T15:00:00Z"),
          providers: {
            onesignal: {
              notificationId: TransactionalNotificationEventEnum.DEPOSIT_AVAILABLE,
              properties: new Map()
            }
          }
        });

        await NotificationService.markSameTypeAndDayContentNotificationsAsSkipped(sentLearningNotification);
        await NotificationService.markSameTypeAndDayContentNotificationsAsSkipped(sentTransactionalNotification);
      });

      afterAll(async () => await clearDb());

      it("should mark all pending notifications of the same learning type and day as skipped", async () => {
        const skippedNotifications = await Notification.find({
          status: "Skipped",
          notifyAt: {
            $gte: new Date("2022-08-04T00:00:00Z"),
            $lt: new Date("2022-08-05T00:00:00Z")
          }
        });

        expect(skippedNotifications).toHaveLength(2);
        skippedNotifications.forEach((notification) => {
          expect(notification.providers.onesignal.notificationId).toBe(
            LearningNotificationEventEnum.GUIDE_CREATED
          );
          expect(notification.owner.toString()).toBe(owner.id);
        });
      });

      it("should not affect notifications on different days", async () => {
        const differentDayNotification = await Notification.findOne({
          notifyAt: new Date("2022-08-05T11:00:00Z")
        });

        expect(differentDayNotification?.status).toBe("Pending");
      });

      it("should not skip transactional notifications even when sent", async () => {
        const transactionalNotification = await Notification.findOne({
          status: "Pending",
          "providers.onesignal.notificationId": TransactionalNotificationEventEnum.DEPOSIT_AVAILABLE
        });

        expect(transactionalNotification?.status).toBe("Pending");
      });

      it("should not affect notifications of different users", async () => {
        const differentUserNotification = await Notification.findOne({
          owner: differentUser.id
        });

        expect(differentUserNotification?.status).toBe("Pending");
      });
    });

    describe("when a notification is not sent", () => {
      let owner: UserDocument;
      const notifyAt = new Date("2022-08-04T11:00:00Z");

      beforeAll(async () => {
        owner = await buildUser();

        // Create a pending notification
        const pendingNotification = await Notification.create({
          owner: owner.id,
          status: "Pending",
          method: "app",
          notifyAt,
          providers: {
            onesignal: {
              notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED,
              properties: new Map()
            }
          }
        });

        // Create another pending notification
        await Notification.create({
          owner: owner.id,
          status: "Pending",
          method: "app",
          notifyAt: new Date("2022-08-04T14:00:00Z"),
          providers: {
            onesignal: {
              notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED,
              properties: new Map()
            }
          }
        });

        await NotificationService.markSameTypeAndDayContentNotificationsAsSkipped(pendingNotification);
      });

      afterAll(async () => await clearDb());

      it("should not mark any notifications as skipped", async () => {
        const skippedNotifications = await Notification.find({ status: "Skipped" });
        expect(skippedNotifications).toHaveLength(0);
      });
    });
  });

  describe("sendBulkAppNotifications", () => {
    describe("when there are no notifications", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });
      });

      it("should NOT send notifications via OneSignal", async () => {
        await NotificationService.sendBulkAppNotifications([]);
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when notifications have different types", () => {
      let user: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });
        await user.populate(UserPopulationFieldsEnum.NOTIFICATION_SETTINGS);

        notification1 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        notification2 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });
      });

      it("should throw an error", async () => {
        await expect(NotificationService.sendBulkAppNotifications([notification1, notification2])).rejects.toThrow(
          "All notifications in bulk send must have the same type, properties and metadata"
        );
      });

      it("should NOT send notifications via OneSignal", () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when notifications have different properties", () => {
      let user: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });
        await user.populate(UserPopulationFieldsEnum.NOTIFICATION_SETTINGS);

        const documentId = faker.string.uuid();
        notification1 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take 1!" })),
              metadata: {
                documentId,
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        notification2 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take 2!" })),
              metadata: {
                documentId,
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });
      });

      it("should throw an error", async () => {
        await expect(NotificationService.sendBulkAppNotifications([notification1, notification2])).rejects.toThrow(
          "All notifications in bulk send must have the same type, properties and metadata"
        );
      });
    });

    describe("when notifications have different metadata", () => {
      let user: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });

        notification1 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        notification2 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });
      });

      it("should throw an error", async () => {
        await expect(NotificationService.sendBulkAppNotifications([notification1, notification2])).rejects.toThrow(
          "All notifications in bulk send must have the same type, properties and metadata"
        );
      });
    });

    describe("when notifications are valid and users have allowed notifications", () => {
      let user1: UserDocument;
      let user2: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;
      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });
        Date.now = jest.fn(() => TODAY.valueOf());

        user1 = await buildUser({ kycStatus: "passed" });
        user2 = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({
          owner: user1.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map([[AppNotificationSettingEnum.QUICK_TAKE, true]])
          }
        });
        await buildNotificationSettings({
          owner: user2.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map([[AppNotificationSettingEnum.QUICK_TAKE, true]])
          }
        });

        const documentId = faker.string.uuid();
        notification1 = await buildNotification({
          owner: user1.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId,
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        notification2 = await buildNotification({
          owner: user2.id,
          status: "Pending",
          method: "app",
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId,
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        await notification1.populate({
          path: "owner",
          populate: {
            path: "notificationSettings"
          }
        });
        await notification2.populate({
          path: "owner",
          populate: {
            path: "notificationSettings"
          }
        });

        await NotificationService.sendBulkAppNotifications([notification1, notification2]);
      });

      it("should send notifications via OneSignal with correct parameters", () => {
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledWith(
          [user1.id, user2.id],
          LearningNotificationEventEnum.QUICK_TAKE_CREATED,
          { title: "Quick Take!" },
          {
            documentId: notification1.providers.onesignal.metadata.documentId,
            notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
          }
        );
      });

      it("should mark all notifications as sent", async () => {
        const updatedNotification1 = await Notification.findById(notification1.id);
        const updatedNotification2 = await Notification.findById(notification2.id);

        expect(updatedNotification1.toObject()).toEqual(
          expect.objectContaining({
            status: "Sent",
            sentAt: TODAY
          })
        );
        expect(updatedNotification2.toObject()).toEqual(
          expect.objectContaining({
            status: "Sent",
            sentAt: TODAY
          })
        );
      });
    });
  });
});
