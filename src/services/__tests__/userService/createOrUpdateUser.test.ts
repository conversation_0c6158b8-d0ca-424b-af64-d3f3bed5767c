import { faker } from "@faker-js/faker";
import { closeDb, connectDb, clearDb } from "../../../tests/utils/db";
import { buildGift, buildReferralCode, buildParticipant, buildUser } from "../../../tests/utils/generateModels";
import UserService from "../../userService";
import {
  AppNotificationSettingEnum,
  EmailNotificationSettingEnum,
  NotificationSettings
} from "../../../models/NotificationSettings";
import { Account } from "../../../models/Account";
import { GiftDocument } from "../../../models/Gift";
import { Participant, ParticipantDocument, TrackingSourceType } from "../../../models/Participant";
import { Portfolio, PortfolioModeEnum } from "../../../models/Portfolio";
import { LifetimeEnum, ReferralCode, ReferralCodeDocument } from "../../../models/ReferralCode";
import { UserDocument, PlatformType, User, UserTypeEnum } from "../../../models/User";
import { CreateParticipantData } from "../../../types/requestBody";
import { PortfolioWrapperTypeEnum } from "../../brokerageService";
import eventEmitter from "../../../loaders/eventEmitter";
import events from "../../../event-handlers/events";
import { MixpanelAccountStatusEnum } from "../../../external-services/segmentAnalyticsService";

describe("UserService.createOrUpdateUser", () => {
  beforeAll(async () => await connectDb("createOrUpdateUser"));
  afterAll(async () => await closeDb());

  describe("when user is signing up via mobile platform and a participant exists already for the given email", () => {
    const WLTHD_ID = faker.string.uuid();
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();
    let participant: ParticipantDocument;
    let referrer: ParticipantDocument;
    let isNew: boolean;

    beforeAll(async () => {
      jest.resetAllMocks();

      const referralCodes = await ReferralCode.find();
      expect(referralCodes.length).toBe(0);

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      participant = await buildParticipant({
        email: EMAIL,
        participantRole: "BASIC",
        appsflyerId: faker.string.uuid(),
        appInstallInfo: { createdAt: new Date(), platform: "android" }
      });

      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        gaClientId: GA_CLIENT_ID,
        wlthd: WLTHD_ID
      };

      // create referrer
      referrer = await buildParticipant({
        participantRole: "BASIC",
        wlthdId: WLTHD_ID
      });

      const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      isNew = userResponse.isNew;
    });
    afterAll(async () => await clearDb());

    it("should return a flag that the user is new", () => {
      expect(isNew).toBe(true);
    });

    it("should change the last login platform of the user to ios", async () => {
      const newUser = (await User.findOne({})) as UserDocument;
      expect(newUser).toEqual(
        expect.objectContaining({
          lastLoginPlatform: "ios"
        })
      );
    });

    it("should not update the app install info for platform & installation time", async () => {
      const newUser = (await User.findOne({ email: EMAIL.toLowerCase() }).populate("participant")) as UserDocument;
      expect(newUser?.participant?.appInstallInfo?.platform).toEqual("android");
      expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
    });

    it("should emit a 'referralStatus' and a 'signUp' event", async () => {
      const newUser = (await User.findOne({ email: EMAIL.toLowerCase() })) as UserDocument;

      expect(eventEmitter.emit).not.toHaveBeenCalledWith(
        events.participant.emailSubmitted.eventId,
        expect.anything()
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        1,
        events.user.signUp.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
        }),
        expect.objectContaining({
          referredStatus: "True"
        })
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        2,
        events.user.whAccountStatusUpdate.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
        }),
        expect.objectContaining({
          accountStatus: MixpanelAccountStatusEnum.Pending
        })
      );
    });

    it("should update the participant with the referrer data without changing the other details", async () => {
      const updatedParticipant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(referrer.wlthdId);
    });

    it("should not update the app install info & appsflyer id", async () => {
      const updatedParticipant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect(updatedParticipant?.anonymousId).toEqual(participant?.anonymousId);
      expect(updatedParticipant?.appsflyerId).toEqual(participant.appsflyerId);
      expect(updatedParticipant?.gaClientId).toEqual(participant?.gaClientId);
    });

    it("should create a new referral code for the user", async () => {
      const createdUser = (await User.findOne({ email: EMAIL })) as UserDocument;
      const referralCodes = await ReferralCode.find({ owner: createdUser.id });

      expect(referralCodes.length).toBe(1);
      expect(referralCodes[0]).toMatchObject(
        expect.objectContaining({
          active: true,
          lifetime: LifetimeEnum.EXPIRING
        })
      );
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });

    it("should create notification settings", async () => {
      const user = await User.findOne({ email: EMAIL });
      const notificationSettings = await NotificationSettings.find({ owner: user!.id });
      expect(notificationSettings).toHaveLength(1);
      expect(notificationSettings[0].toObject()).toMatchObject({
        owner: user!._id,
        app: {
          deviceNotificationsEnabled: true,
          settings: new Map([
            [AppNotificationSettingEnum.TRANSACTIONAL, true],
            [AppNotificationSettingEnum.LEARNING_GUIDE, true],
            [AppNotificationSettingEnum.ANALYST_INSIGHT, true],
            [AppNotificationSettingEnum.QUICK_TAKE, true],
            [AppNotificationSettingEnum.DAILY_RECAP, true],
            [AppNotificationSettingEnum.WEEKLY_REVIEW, true],
            [AppNotificationSettingEnum.PROMOTIONAL, true]
          ])
        },
        email: {
          settings: new Map([
            [EmailNotificationSettingEnum.TRANSACTIONAL, true],
            [EmailNotificationSettingEnum.WEALTHYBITES, true],
            [EmailNotificationSettingEnum.PROMOTIONAL, true]
          ])
        }
      });
    });
  });

  describe("when user is signing up via mobile platform and a participant exists already for the given appsflyer id but has no email", () => {
    const WLTHD_ID = "wlthd-id";
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();
    let referrer: ParticipantDocument;
    let isNew: boolean;

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      await buildParticipant({
        email: undefined,
        appsflyerId: APPSFLYER_ID,
        participantRole: "BASIC",
        appInstallInfo: { createdAt: new Date(), platform: "android" }
      });

      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        gaClientId: GA_CLIENT_ID,
        wlthd: WLTHD_ID
      };

      // create referrer
      referrer = await buildParticipant({
        participantRole: "BASIC",
        wlthdId: WLTHD_ID
      });

      const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      isNew = userResponse.isNew;
    });
    afterAll(async () => await clearDb());

    it("should return a flag that the user is new", () => {
      expect(isNew).toBe(true);
    });

    it("should change the last login platform of the user to ios", async () => {
      const newUser = (await User.findOne({})) as UserDocument;
      expect(newUser).toEqual(
        expect.objectContaining({
          lastLoginPlatform: "ios"
        })
      );
    });

    it("should emit a 'signUp' user event", async () => {
      const newUser = (await User.findOne({ email: EMAIL.toLowerCase() })) as UserDocument;

      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        1,
        events.participant.emailSubmitted.eventId,
        expect.anything()
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        2,
        events.user.signUp.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
        }),
        expect.objectContaining({
          referredStatus: "True"
        })
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        3,
        events.user.whAccountStatusUpdate.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
        }),
        expect.objectContaining({
          accountStatus: MixpanelAccountStatusEnum.Pending
        })
      );
    });

    it("should update the participant doc with the attribution params and the email", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
      expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
      expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
      expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user is signing up via mobile platform and no participant exists", () => {
    const WLTHD_ID = "wlthd-id";
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GOOGLE_CAMPAIGN = "Search-Investing";
    const PAGE_USER_LANDED = "Page-User-Landed";
    const GA_CLIENT_ID = faker.string.uuid();
    const GCLID = faker.string.uuid();
    const EMAIL = faker.internet.email();
    let referrer: ParticipantDocument;
    let isNew: boolean;

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        pageUserLanded: PAGE_USER_LANDED,
        appsflyerId: APPSFLYER_ID,
        googleAdsMetadata: {
          campaign: GOOGLE_CAMPAIGN,
          gclid: GCLID
        },
        trackingSource: "google",
        gaClientId: GA_CLIENT_ID,
        wlthd: WLTHD_ID
      };

      // create referrer
      referrer = await buildParticipant({ participantRole: "BASIC", wlthdId: WLTHD_ID });

      const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      isNew = userResponse.isNew;
    });
    afterAll(async () => await clearDb());

    it("should return a flag that the user is new", () => {
      expect(isNew).toBe(true);
    });

    it("should change the last login platform of the user to ios", async () => {
      const newUser = (await User.findOne({})) as UserDocument;
      expect(newUser).toEqual(
        expect.objectContaining({
          lastLoginPlatform: "ios"
        })
      );
    });

    it("should store the app install info for platform & installation time", async () => {
      const newUser = (await User.findOne({}).populate("participant")) as UserDocument;
      expect(newUser?.participant?.appInstallInfo?.platform).toEqual("ios");
      expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
    });

    it("should emit an 'emailSubmitted' and a 'signUp' user event", async () => {
      const newUser = (await User.findOne({})) as UserDocument;

      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        1,
        events.participant.emailSubmitted.eventId,
        expect.objectContaining({ email: EMAIL.toLowerCase() })
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        2,
        events.user.signUp.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({
            referrer: expect.objectContaining({ email: referrer.email?.toLowerCase() })
          })
        }),
        expect.objectContaining({
          referredStatus: "True"
        })
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        3,
        events.user.whAccountStatusUpdate.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
        }),
        expect.objectContaining({
          accountStatus: MixpanelAccountStatusEnum.Pending
        })
      );
    });

    it("should create a new participant with attribution params & google ads metadata stored", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
      expect(participant).toMatchObject(
        expect.objectContaining({
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          pageUserLanded: PAGE_USER_LANDED,
          trackingSource: "google",
          metadata: expect.objectContaining({
            googleAds: expect.objectContaining({
              campaign: GOOGLE_CAMPAIGN,
              gclid: GCLID
            })
          })
        })
      );
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user is signing up via mobile platform with TikTok as source", () => {
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };

      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        // No Google Ads metadata provided to avoid auto-setting google source
        trackingSource: "tiktok"
      };

      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should create a new participant with trackingSource set to 'tiktok'", async () => {
      const participant = await Participant.findOne({ email: EMAIL });
      expect(participant).toBeTruthy();
      expect(participant?.trackingSource).toEqual("tiktok");
    });
  });

  describe("when user is signing up via mobile platform, no participant exists and user has gift", () => {
    const WLTHD_ID = "wlthd-id";
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const PAGE_USER_LANDED = "Page-User-Landed";
    const GOOGLE_CAMPAIGN = "Search-Investing";
    const GA_CLIENT_ID = faker.string.uuid();
    const GCLID = faker.string.uuid();
    const EMAIL = faker.internet.email().toLowerCase();

    let referrer: ParticipantDocument;
    let gift: GiftDocument;
    let isNew: boolean;

    beforeAll(async () => {
      jest.resetAllMocks();

      const anotherUser = await buildUser();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        pageUserLanded: PAGE_USER_LANDED,
        googleAdsMetadata: {
          campaign: GOOGLE_CAMPAIGN,
          gclid: GCLID
        },
        trackingSource: "google",
        gaClientId: GA_CLIENT_ID,
        wlthd: WLTHD_ID
      };

      // create referrer
      referrer = await buildParticipant({ participantRole: "BASIC", wlthdId: WLTHD_ID });

      gift = await buildGift({
        gifter: anotherUser._id,
        targetUserEmail: EMAIL
      });

      const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      isNew = userResponse.isNew;
    });
    afterAll(async () => await clearDb());

    it("should return a flag that the user is new", () => {
      expect(isNew).toBe(true);
    });

    it("should change the last login platform of the user to ios", async () => {
      const newUser = (await User.findOne({ email: EMAIL })) as UserDocument;
      expect(newUser).toEqual(
        expect.objectContaining({
          lastLoginPlatform: "ios"
        })
      );
    });

    it("should store the app install info for platform & installation time", async () => {
      const newUser = (await User.findOne({ email: EMAIL }).populate("participant")) as UserDocument;
      expect(newUser?.participant?.appInstallInfo?.platform).toEqual("ios");
      expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
    });

    it("should emit an 'emailSubmitted' and a 'signUp' user event", async () => {
      const newUser = (await User.findOne({ email: EMAIL })) as UserDocument;

      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        1,
        events.participant.emailSubmitted.eventId,
        expect.objectContaining({ email: EMAIL.toLowerCase() })
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        2,
        events.user.signUp.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({
            referrer: expect.objectContaining({ email: referrer.email?.toLowerCase() })
          })
        }),
        {
          referredStatus: "True",
          gift: expect.objectContaining({ id: gift.id })
        }
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        3,
        events.user.whAccountStatusUpdate.eventId,
        expect.objectContaining({
          id: newUser.id,
          participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
        }),
        expect.objectContaining({
          accountStatus: MixpanelAccountStatusEnum.Pending
        })
      );
    });

    it("should create a new participant with attribution params & google ads metadata stored", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
      expect(participant).toMatchObject(
        expect.objectContaining({
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          pageUserLanded: PAGE_USER_LANDED,
          trackingSource: "google",
          metadata: expect.objectContaining({
            googleAds: expect.objectContaining({
              campaign: GOOGLE_CAMPAIGN,
              gclid: GCLID
            })
          })
        })
      );
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("it should set the user's viewedReferralCodeScreen to true", async () => {
      const newUser = (await User.findOne({ email: EMAIL })) as UserDocument;

      expect(newUser?.viewedReferralCodeScreen).toEqual(true);
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user is logging in via mobile platform for the first time", () => {
    let user: UserDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      user = await buildUser();
      await buildParticipant({ email: user.email, appInstallInfo: undefined, appsflyerId: undefined });

      const userData = {
        auth0: { id: `email|${user.email}` },
        email: user.email,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referrerData = {
        appInstallInfo: {
          platform: "ios" as "ios" | "android",
          createdAt: new Date()
        },
        appsflyerId: "appsflyer-id"
      };

      await UserService.createOrUpdateUser(user.email, userData, referrerData);
    });
    afterAll(async () => await clearDb());

    it("should change the last login platform of the user to ios", async () => {
      const updatedUser = await User.findOne({});
      expect(updatedUser).toEqual(
        expect.objectContaining({
          lastLoginPlatform: "ios"
        })
      );
    });

    it("should store the app install info for platform & installation time", async () => {
      const newUser = (await User.findOne({}).populate("participant")) as UserDocument;
      expect(newUser?.participant?.appInstallInfo?.platform).toEqual("ios");
      expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
    });

    it("should emit a 'login' user event", async () => {
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.logIn.eventId,
        expect.objectContaining({ id: user.id, lastLoginPlatform: "ios" }),
        expect.objectContaining({ justDownloadedApp: true })
      );
    });
  });

  describe("when user is logging in via a different mobile platform than before", () => {
    const INSTALL_TIME = new Date();
    let user: UserDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      user = await buildUser({
        lastLoginPlatform: "android"
      });
      await buildParticipant({
        email: user.email,
        appInstallInfo: {
          platform: "android",
          createdAt: INSTALL_TIME
        }
      });
      const userData = {
        auth0: { id: `email|${user.email}` },
        email: user.email,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };

      await UserService.createOrUpdateUser(user.email, userData);
    });
    afterAll(async () => await clearDb());

    it("should change the last login platform of the user to ios", async () => {
      const updatedUser = await User.findOne({});
      expect(updatedUser).toEqual(
        expect.objectContaining({
          lastLoginPlatform: "ios"
        })
      );
    });

    it("should not update the app install info for platform & installation time", async () => {
      const newUser = (await User.findOne({}).populate("participant")) as UserDocument;
      expect(newUser?.participant?.appInstallInfo?.platform).toEqual("android");
      expect(newUser?.participant?.appInstallInfo?.createdAt).toEqual(INSTALL_TIME);
    });

    it("should emit a 'login' user event", async () => {
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.logIn.eventId,
        expect.objectContaining({ id: user.id, lastLoginPlatform: "ios" }),
        expect.objectContaining({ justDownloadedApp: false })
      );
    });
  });

  describe("when a user is logging in and the corresponding participant does not exist", () => {
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();

    let user: UserDocument;

    beforeAll(async () => {
      jest.resetAllMocks();
      jest.spyOn(eventEmitter, "emit");
      user = await buildUser();
      const userData = {
        auth0: { id: `email|${user.email}` },
        email: user.email,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        gaClientId: GA_CLIENT_ID
      };
      await user.populate("participant");
      expect(user.participant?.email).toBeUndefined();

      await UserService.createOrUpdateUser(user.email, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should create the missing participant document", async () => {
      const participant = await Participant.findOne({ email: user.email });
      expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
      expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
      expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
    });

    it("should not emit a emailSubmitted event", async () => {
      expect(eventEmitter.emit).not.toHaveBeenCalledWith(
        events.participant.emailSubmitted.eventId,
        expect.objectContaining({ email: user.email })
      );
    });
  });

  describe("when a user is logging in and the corresponding participant does not have an appsflyer id", () => {
    const APPSFLYER_ID = faker.string.uuid();

    let user: UserDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      user = await buildUser();
      await buildParticipant({ email: user.email });

      const userData = {
        auth0: { id: `email|${user.email}` },
        email: user.email,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        appsflyerId: APPSFLYER_ID,
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        }
      };
      await user.populate("participant");
      expect(user.participant.email).toEqual(user.email);
      expect(user.participant.appsflyerId).toBeUndefined();
      expect(user.participant.appInstallInfo).toMatchObject({});

      await UserService.createOrUpdateUser(user.email, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should update the participant document with the appsflyerId and the app install info", async () => {
      const participant = await Participant.findOne({ email: user.email });
      expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
      expect(participant?.appInstallInfo).toMatchObject(expect.objectContaining({ platform: "ios" }));
    });
  });

  describe("when user signs up with the free reward campaign wlthd id", () => {
    const REFERRER_WLTHD_ID = "wlthd-id";
    const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page

    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();

    let wealthyhoodParticipant: ParticipantDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        gaClientId: GA_CLIENT_ID,
        wlthd: WH_FREE_ETF_WLTHD_ID
      };

      // create referrer
      await buildParticipant({ participantRole: "BASIC", wlthdId: REFERRER_WLTHD_ID });
      // wealthyhood participant
      wealthyhoodParticipant = await buildParticipant({
        participantRole: "BASIC",
        wlthdId: WH_FREE_ETF_WLTHD_ID
      });

      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should create a new participant referred by the wealthyhood participant", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
      expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
      expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
      expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WH_FREE_ETF_WLTHD_ID);

      const user = await User.findOne({ email: EMAIL });
      expect(user?.referredByEmail).toEqual(wealthyhoodParticipant.email);
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user signs up with both a wlthd id & finance ads id", () => {
    const REFERRER_WLTHD_ID = "wlthd-id";
    const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page
    const FINANCE_ADS_WLTHD_ID = "rz81ojjz";

    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const FINANCER_ADS_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();

    let userParticipant: ParticipantDocument;
    let referralCode: ReferralCodeDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      referralCode = await buildReferralCode();
      await referralCode.populate("owner");

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        financeAdsSessionId: FINANCER_ADS_ID,
        gaClientId: GA_CLIENT_ID,
        wlthd: referralCode.code
      };

      // create referrer
      userParticipant = await buildParticipant({
        email: (referralCode.owner as UserDocument).email,
        participantRole: "BASIC",
        wlthdId: REFERRER_WLTHD_ID
      });
      // wealthyhood participant
      await buildParticipant({
        participantRole: "BASIC",
        wlthdId: WH_FREE_ETF_WLTHD_ID
      });
      // finance ads participant
      await buildParticipant({
        participantRole: "BASIC",
        wlthdId: FINANCE_ADS_WLTHD_ID
      });

      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should create a new participant referred by the participant that corresponds to the wlthd id param", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
      expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
      expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
      expect((participant?.referrer as ParticipantDocument)?.email).toEqual(
        (referralCode.owner as UserDocument)?.email
      );

      const user = await User.findOne({ email: EMAIL });
      expect(user?.referredByEmail).toEqual(userParticipant.email);
    });

    it("should make inactive the old referral code and generate a new one for the referrer", async () => {
      const oldReferralCode = await ReferralCode.findOne({ _id: referralCode.id });
      expect(oldReferralCode?.active).toBe(false);

      const updatedUser = await User.findOne({ _id: (oldReferralCode as ReferralCodeDocument).owner }).populate(
        "oneTimeReferralCode"
      );
      expect((updatedUser?.oneTimeReferralCode as ReferralCodeDocument).active).toBe(true);
      expect((updatedUser?.oneTimeReferralCode as ReferralCodeDocument).code).not.toBe(referralCode.code);
    });

    it("should create a new referral code for the user", async () => {
      const createdUser = (await User.findOne({ email: EMAIL })) as UserDocument;
      const referralCodes = await ReferralCode.find({ owner: createdUser.id });

      expect(referralCodes.length).toBe(1);
      expect(referralCodes[0]).toMatchObject(
        expect.objectContaining({
          active: true,
          lifetime: LifetimeEnum.EXPIRING
        })
      );
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user signs up with a finance ads id & a wlthd id that corresponds to the free reward campaign", () => {
    const REFERRER_WLTHD_ID = "wlthd-id";
    const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page
    const FINANCE_ADS_WLTHD_ID = "rz81ojjz";

    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const FINANCER_ADS_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();

    let financeAdsParticipant: ParticipantDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      const referralData: CreateParticipantData = {
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        financeAdsSessionId: FINANCER_ADS_ID,
        gaClientId: GA_CLIENT_ID,
        // important => we need to
        wlthd: WH_FREE_ETF_WLTHD_ID
      };

      // user referrer
      await buildParticipant({ participantRole: "BASIC", wlthdId: REFERRER_WLTHD_ID });
      // wealthyhood participant
      await buildParticipant({
        participantRole: "BASIC",
        wlthdId: WH_FREE_ETF_WLTHD_ID
      });
      // finance ads participant
      financeAdsParticipant = await buildParticipant({
        participantRole: "BASIC",
        wlthdId: FINANCE_ADS_WLTHD_ID
      });

      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should create a new participant referred by the participant that corresponds to the finance ads participant", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
      expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
      expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
      expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(FINANCE_ADS_WLTHD_ID);

      const user = await User.findOne({ email: EMAIL });
      expect(user?.referredByEmail).toEqual(financeAdsParticipant.email);
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user signs up and a participant exists already for the given email and has google ads metadata", () => {
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();
    const GOOGLE_CAMPAIGN = "Search-Investing";
    const GCLID = faker.string.uuid();
    const TRACKING_SOURCE: TrackingSourceType = "google";

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      await buildParticipant({
        email: EMAIL,
        participantRole: "BASIC",
        appsflyerId: faker.string.uuid(),
        appInstallInfo: { createdAt: new Date(), platform: "android" },
        trackingSource: TRACKING_SOURCE,
        metadata: {
          googleAds: {
            gclid: GCLID,
            campaign: GOOGLE_CAMPAIGN
          }
        }
      });

      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        gaClientId: GA_CLIENT_ID
      };

      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should not update the google ads metadata", async () => {
      const updatedParticipant = await Participant.findOne({ email: EMAIL });
      expect(updatedParticipant).toMatchObject(
        expect.objectContaining({
          trackingSource: TRACKING_SOURCE,
          metadata: expect.objectContaining({
            googleAds: expect.objectContaining({
              gclid: GCLID,
              campaign: GOOGLE_CAMPAIGN
            })
          })
        })
      );
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user signs up and a participant exists already for the given email but has no google ads metadata", () => {
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GA_CLIENT_ID = faker.string.uuid();
    const EMAIL = faker.internet.email();
    const TRACKING_SOURCE: TrackingSourceType = "google";
    const GCLID = faker.string.uuid();
    const GOOGLE_CAMPAIGN = "Search-Investing";

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };
      await buildParticipant({
        email: EMAIL,
        participantRole: "BASIC",
        appsflyerId: faker.string.uuid(),
        appInstallInfo: { createdAt: new Date(), platform: "android" },
        metadata: {}
      });

      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        gaClientId: GA_CLIENT_ID,
        trackingSource: TRACKING_SOURCE,
        googleAdsMetadata: {
          gclid: GCLID,
          campaign: GOOGLE_CAMPAIGN
        }
      };

      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should store the google ads metadata", async () => {
      const updatedParticipant = await Participant.findOne({ email: EMAIL });
      expect(updatedParticipant).toMatchObject(
        expect.objectContaining({
          trackingSource: TRACKING_SOURCE,
          metadata: expect.objectContaining({
            googleAds: expect.objectContaining({
              gclid: GCLID,
              campaign: GOOGLE_CAMPAIGN
            })
          })
        })
      );
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });

  describe("when user is signing up via mobile platform with upper case 'wlthdId' that matches a referral code", () => {
    const WLTHD_ID = "wlthd-id";
    const ANONYMOUS_ID = faker.string.uuid();
    const APPSFLYER_ID = faker.string.uuid();
    const GOOGLE_CAMPAIGN = "Search-Investing";
    const GA_CLIENT_ID = faker.string.uuid();
    const GCLID = faker.string.uuid();
    const EMAIL = faker.internet.email();
    let referrer: ParticipantDocument;
    let referrerUser: UserDocument;
    let referralCode: ReferralCodeDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      const userData = {
        auth0: { id: `email|${EMAIL}` },
        email: EMAIL,
        emailVerified: true,
        lastLogin: new Date(),
        role: [UserTypeEnum.INVESTOR],
        lastLoginPlatform: "ios" as PlatformType
      };

      // create referrer, with lowercase 'wlthId'
      const referrerEmail = faker.internet.email();
      referrer = await buildParticipant({ participantRole: "BASIC", email: referrerEmail });
      referrerUser = await buildUser({ email: referrerEmail });
      referralCode = await buildReferralCode({ owner: referrerUser.id, lifetime: "expiring", code: WLTHD_ID });

      const referralData: CreateParticipantData = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: "ios"
        },
        anonymousId: ANONYMOUS_ID,
        appsflyerId: APPSFLYER_ID,
        googleAdsMetadata: {
          campaign: GOOGLE_CAMPAIGN,
          gclid: GCLID
        },
        trackingSource: "google",
        gaClientId: GA_CLIENT_ID,
        wlthd: WLTHD_ID
      };

      // call createOrUpdateUser with uppercase 'wlthId'
      await UserService.createOrUpdateUser(EMAIL, userData, referralData);
    });
    afterAll(async () => await clearDb());

    it("should create a new participant that is referred by the owner (participant) of the referralCode", async () => {
      const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
      expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(referrer.wlthdId);
    });

    it("should deactivate the referralCode", async () => {
      const updatedReferralCode = await ReferralCode.findById(referralCode.id);

      expect(updatedReferralCode?.active).toEqual(false);
    });

    it("should create an account", async () => {
      const user = await User.findOne({ email: EMAIL });
      const accounts = await Account.find({});
      expect(accounts.length).toEqual(1);
      expect(accounts[0]).toMatchObject({
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        name: "General Investment Account",
        owner: user!._id
      });
    });

    it("should create a real portfolio", async () => {
      const user = await User.findOne({ email: EMAIL });
      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      expect(portfolios.length).toEqual(1);
      expect(portfolios[0].toObject()).toMatchObject({
        owner: user!._id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: []
      });
    });
  });
});
