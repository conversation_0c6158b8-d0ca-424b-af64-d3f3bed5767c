import { faker } from "@faker-js/faker";
import { UserDocument } from "../../../models/User";
import UserService from "../../userService";
import {
  buildUser,
  buildReward,
  buildGift,
  buildWealthyhoodDividendTransaction
} from "../../../tests/utils/generateModels";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { Reward } from "../../../models/Reward";
import { Gift } from "../../../models/Gift";
import { ProviderEnum } from "../../../configs/providersConfig";
import { WealthyhoodDividendTransaction } from "../../../models/Transaction";

describe("UserService.markPromptsAsSeen", () => {
  let user: UserDocument;

  beforeAll(async () => {
    await connectDb("markPromptsAsSeen");
    user = await buildUser();
  });
  afterAll(async () => {
    await clearDb();
    await closeDb();
  });

  it("should update reward document", async () => {
    const reward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id
    });

    await UserService.markPromptsAsSeen({
      ids: [reward.id],
      modalType: "Reward"
    });

    const updatedReward = await Reward.findById(reward.id);

    expect(updatedReward?.hasViewedAppModal).toEqual(true);
  });

  it("should update multiple reward documents", async () => {
    const firstReward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id
    });
    const secondReward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id
    });

    await UserService.markPromptsAsSeen({
      ids: [firstReward.id, secondReward.id],
      modalType: "Reward"
    });

    const updatedFirstReward = await Reward.findById(firstReward.id);

    expect(updatedFirstReward?.hasViewedAppModal).toEqual(true);

    const updatedSecondReward = await Reward.findById(secondReward.id);

    expect(updatedSecondReward?.hasViewedAppModal).toEqual(true);
  });

  it("should update single reward settled document for RewardSettled modal type", async () => {
    const reward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id
    });

    await UserService.markPromptsAsSeen({
      ids: [reward.id],
      modalType: "RewardSettled"
    });

    const updatedReward = await Reward.findById(reward.id);

    expect(updatedReward?.hasViewedAppModal).toEqual(true);
  });

  it("should update multiple reward settled documents for RewardSettled modal type", async () => {
    const firstReward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id
    });
    const secondReward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id
    });

    await UserService.markPromptsAsSeen({
      ids: [firstReward.id, secondReward.id],
      modalType: "RewardSettled"
    });

    const updatedFirstReward = await Reward.findById(firstReward.id);

    expect(updatedFirstReward?.hasViewedAppModal).toEqual(true);

    const updatedSecondReward = await Reward.findById(secondReward.id);

    expect(updatedSecondReward?.hasViewedAppModal).toEqual(true);
  });

  it("should update gift document", async () => {
    const gift = await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: user.email
    });

    await UserService.markPromptsAsSeen({
      ids: [gift.id],
      modalType: "Gift"
    });

    const updatedGift = await Gift.findById(gift.id);

    expect(updatedGift?.hasViewedAppModal).toEqual(true);
  });

  it("should update multiple gift documents", async () => {
    const firstGift = await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: user.email
    });
    const secondGift = await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: user.email
    });

    await UserService.markPromptsAsSeen({
      ids: [firstGift.id, secondGift.id],
      modalType: "Gift"
    });

    const updatedFirstGift = await Gift.findById(firstGift.id);

    expect(updatedFirstGift?.hasViewedAppModal).toEqual(true);

    const updatedSecondGift = await Gift.findById(secondGift.id);

    expect(updatedSecondGift?.hasViewedAppModal).toEqual(true);
  });

  it("should update wealthyhood dividend transaction document", async () => {
    const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
      hasViewedAppModal: false,
      owner: user.id,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      }
    });

    await UserService.markPromptsAsSeen({
      ids: [wealthyhoodDividend.id],
      modalType: "WealthyhoodDividend"
    });

    const updatedwealthyhoodDividend = await WealthyhoodDividendTransaction.findById(wealthyhoodDividend.id);

    expect(updatedwealthyhoodDividend?.hasViewedAppModal).toEqual(true);
  });

  it("should update multiple wealthyhood dividend transaction documents", async () => {
    const firstWealthyhoodDividend = await buildWealthyhoodDividendTransaction({
      hasViewedAppModal: false,
      owner: user.id,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      }
    });
    const secondWealthyhoodDividend = await buildWealthyhoodDividendTransaction({
      hasViewedAppModal: false,
      owner: user.id,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      }
    });

    await UserService.markPromptsAsSeen({
      ids: [firstWealthyhoodDividend.id, secondWealthyhoodDividend.id],
      modalType: "WealthyhoodDividend"
    });

    const updatedFirstwealthyhoodDividend = await WealthyhoodDividendTransaction.findById(
      firstWealthyhoodDividend.id
    );

    expect(updatedFirstwealthyhoodDividend?.hasViewedAppModal).toEqual(true);

    const updatedSecondwealthyhoodDividend = await WealthyhoodDividendTransaction.findById(
      firstWealthyhoodDividend.id
    );

    expect(updatedSecondwealthyhoodDividend?.hasViewedAppModal).toEqual(true);
  });
});
