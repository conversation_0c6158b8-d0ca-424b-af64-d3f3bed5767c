import { faker } from "@faker-js/faker";
import { investmentUniverseConfig, bannersConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import { closeDb, connectDb, clearDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildContentEntry,
  buildGift,
  buildPortfolio,
  buildReward,
  buildSavingsTopup,
  buildSavingsProduct,
  buildSubscription,
  buildTopUpAutomation,
  buildUser,
  buildWealthyhoodDividendTransaction
} from "../../../tests/utils/generateModels";
import { AppRating } from "../../../models/AppRating";
import { PortfolioModeEnum } from "../../../models/Portfolio";
import { UserDocument, KycStatusEnum } from "../../../models/User";
import UserService from "../../userService";
import { ProviderEnum } from "../../../configs/providersConfig";
import DateUtil from "../../../utils/dateUtil";
import { buildContentfulContentEntriesResponse } from "../../../tests/utils/generateContentful";
import ContentfulRetrievalService from "../../../external-services/contentfulRetrievalService";
import { ContentEntryContentTypeEnum } from "../../../models/ContentEntry";

const { BannerEnum, CATEGORY_CONFIG, CategoryEnum } = bannersConfig;

describe("UserService.getPrompts", () => {
  // This the minimum number of banner prompts this method will return
  // This should be updated if the default minimum has changed
  const MINIMUM_BANNER_PROMPTS_VERIFIED_USERS = 4;
  const MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS = 5;
  let user: UserDocument;

  const EUR_ONE_DAY_YIELD = 3.5;
  const GBP_ONE_DAY_YIELD = 4.7;

  const ASSET_COMMON_IDS_CONFIG: {
    assetId: investmentUniverseConfig.AssetType;
    quantity: number;
    price: number;
    percentage: number;
  }[] = [
    { assetId: "equities_china", quantity: 2, price: 10, percentage: 30 },
    { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
    { assetId: "government_bonds_us", quantity: 2, price: 10, percentage: 40 }
  ];

  beforeAll(async () => await connectDb("getPrompts"));
  beforeEach(async () => {
    user = await buildUser({ kycStatus: KycStatusEnum.PASSED });

    await buildPortfolio({
      owner: user.id,
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await Promise.all([
      buildSavingsProduct(true, { commonId: "mmf_dist_eur" }, { oneDayYield: EUR_ONE_DAY_YIELD }),
      buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { oneDayYield: GBP_ONE_DAY_YIELD })
    ]);
  });
  afterAll(async () => await closeDb());
  afterEach(async () => {
    jest.clearAllMocks();
    await clearDb();
  });

  it("should return app rating prompt modal", async () => {
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });
    await buildTopUpAutomation({ owner: user._id, active: true });

    const prompts = await UserService.getPrompts(user);

    const appRating = await AppRating.findOne({ owner: user._id });

    expect(prompts).toMatchObject({
      modalPrompts: [
        {
          order: 0,
          modalType: "AppRatingPrompt",
          data: {
            appRatingId: appRating!.id
          }
        }
      ]
    });
  });

  it("should return all modal prompts by order", async () => {
    const reward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id,
      status: "Settled"
    });
    const gift = await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: user.email
    });
    const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
      hasViewedAppModal: false,
      owner: user.id,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled",
            submittedAt: new Date()
          }
        }
      }
    });
    await buildTopUpAutomation({ owner: user._id, active: true });

    const prompts = await UserService.getPrompts(user, "modal");

    const appRating = await AppRating.findOne({ owner: user._id });

    expect(prompts).toEqual({
      modalPrompts: [
        {
          order: 0,
          modalType: "AppRatingPrompt",
          data: {
            appRatingId: appRating!.id
          }
        },
        {
          order: 1,
          modalType: "RewardSettled",
          data: { rewards: expect.arrayContaining([expect.objectContaining({ _id: reward._id })]) }
        },
        {
          order: 2,
          modalType: "Gift",
          data: { gifts: expect.arrayContaining([expect.objectContaining({ _id: gift._id })]) }
        },
        {
          order: 3,
          modalType: "WealthyhoodDividend",
          data: {
            wealthyhoodDividends: expect.arrayContaining([
              expect.objectContaining({ _id: wealthyhoodDividend._id })
            ])
          }
        }
      ]
    });
  });

  it("should return reward settled modal for users with settled rewards", async () => {
    const settledReward = await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id,
      accepted: true,
      status: "Settled"
    });

    const prompts = await UserService.getPrompts(user, "modal");

    expect(prompts).toEqual({
      modalPrompts: [
        {
          order: 0,
          modalType: "RewardSettled",
          data: { rewards: expect.arrayContaining([expect.objectContaining({ _id: settledReward._id })]) }
        }
      ]
    });
  });

  it("should NOT return reward modal for users with pending rewards", async () => {
    await buildReward({
      hasViewedAppModal: false,
      targetUser: user.id,
      accepted: true,
      status: "Pending"
    });

    const prompts = await UserService.getPrompts(user, "modal");

    expect(prompts).toEqual({
      modalPrompts: []
    });
  });

  it("should return all available modal prompts by order (gift + dividend)", async () => {
    const gift = await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: user.email
    });
    const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
      hasViewedAppModal: false,
      owner: user.id,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      }
    });

    const prompts = await UserService.getPrompts(user, "modal");

    expect(prompts).toEqual({
      modalPrompts: [
        {
          order: 0,
          modalType: "Gift",
          data: { gifts: expect.arrayContaining([expect.objectContaining({ _id: gift._id })]) }
        },
        {
          order: 1,
          modalType: "WealthyhoodDividend",
          data: {
            wealthyhoodDividends: expect.arrayContaining([
              expect.objectContaining({ _id: wealthyhoodDividend._id })
            ])
          }
        }
      ]
    });
  });

  it("should NOT return RedeemGift modal if the user has not set a target allocation & has no holdings", async () => {
    const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanNotRedeemGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: []
    });

    await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: userWhoCanNotRedeemGift.email
    });

    const prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "modal");

    expect(prompts).toEqual({
      modalPrompts: []
    });
  });

  it("should return EarnFreeShares banner prompt", async () => {
    const userWhoCanRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanRedeemGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await buildSubscription({
      owner: userWhoCanRedeemGift.id,
      price: "free_monthly"
    });

    const prompts = await UserService.getPrompts(userWhoCanRedeemGift, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnFreeShares,
          order: 0,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should return RedeemGift banner prompt if available", async () => {
    const userWhoCanRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanRedeemGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await buildSubscription({
      owner: userWhoCanRedeemGift.id,
      price: "free_monthly"
    });
    const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanNotRedeemGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: userWhoCanNotRedeemGift.id,
      price: "free_monthly"
    });

    const gift = await buildGift({
      hasViewedAppModal: false,
      targetUserEmail: userWhoCanRedeemGift.email
    });

    let prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS);

    prompts = await UserService.getPrompts(userWhoCanRedeemGift, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.RedeemGift,
          order: 0,
          data: expect.objectContaining({
            gifts: expect.arrayContaining([expect.objectContaining({ _id: gift._id })]),
            title: expect.anything()
          })
        })
      ])
    });
  });

  it("should NOT return RedeemGift banner prompt if gift is used", async () => {
    const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanNotRedeemGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await buildSubscription({
      owner: userWhoCanNotRedeemGift.id,
      price: "free_monthly"
    });

    const gift = await buildGift({
      targetUserEmail: userWhoCanNotRedeemGift.email
    });

    // create asset transaction that uses the gift
    await buildAssetTransaction({
      owner: userWhoCanNotRedeemGift.id,
      pendingGift: gift.id
    });

    const prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS);
  });

  it("should NOT return RedeemGift banner prompt if the user has not set a target allocation & has no holdings", async () => {
    const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanNotRedeemGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: []
    });

    await buildSubscription({
      owner: userWhoCanNotRedeemGift.id,
      price: "free_monthly"
    });

    const gift = await buildGift({
      targetUserEmail: userWhoCanNotRedeemGift.email
    });

    // create asset transaction that uses the gift
    await buildAssetTransaction({
      owner: userWhoCanNotRedeemGift.id,
      pendingGift: gift.id
    });

    const prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "banner");

    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.RedeemGift,
          order: expect.anything()
        })
      ])
    });
  });

  it("should return SendGift banner prompt if available", async () => {
    const TODAY = new Date("2023-09-18T11:00:00Z");

    Date.now = jest.fn(() => TODAY.valueOf());

    const userWhoCannotSendGift = await buildUser({
      portfolioConversionStatus: "completed",
      canSendGiftUntil: DateUtil.getDateOfDaysAgo(TODAY, 1),
      kycStatus: KycStatusEnum.PASSED
    });
    await buildPortfolio({
      owner: userWhoCannotSendGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: userWhoCannotSendGift.id,
      price: "free_monthly"
    });

    let prompts = await UserService.getPrompts(userWhoCannotSendGift, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS);

    const userWhoCanSendGift = await buildUser({
      kycStatus: KycStatusEnum.PASSED,
      canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
    });
    await buildPortfolio({
      owner: userWhoCanSendGift.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: userWhoCanSendGift.id,
      price: "free_monthly"
    });

    prompts = await UserService.getPrompts(userWhoCanSendGift, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.SendGift,
          order: 0,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should return UnlockFreeShare banner prompt if available", async () => {
    const userWhoCanUnlockFreeShare = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildPortfolio({
      owner: userWhoCanUnlockFreeShare.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: userWhoCanUnlockFreeShare.id,
      price: "free_monthly"
    });

    const userWhoCanNotUnlockFreeShare = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    await buildSubscription({
      owner: userWhoCanNotUnlockFreeShare.id,
      price: "free_monthly"
    });
    await buildPortfolio({
      owner: userWhoCanNotUnlockFreeShare.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    jest.spyOn(UserService, "canUnlockFreeShare").mockImplementation(async (user: UserDocument) => {
      return user.id === userWhoCanUnlockFreeShare.id;
    });

    let prompts = await UserService.getPrompts(userWhoCanNotUnlockFreeShare, "banner");
    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS);

    prompts = await UserService.getPrompts(userWhoCanUnlockFreeShare, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.UnlockFreeShare,
          order: 0,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should always return LatestAnalysis banner if a content entry exists", async () => {
    const LATEST_ANALYST_INSIGHT_TITLE = "Bond managers";
    const BANNER_IMAGE_URL = "some-image-url";

    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });
    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const contentEntry = await buildContentEntry({ contentType: ContentEntryContentTypeEnum.ANALYSIS });
    const contentfulResponse = buildContentfulContentEntriesResponse([
      {
        title: LATEST_ANALYST_INSIGHT_TITLE,
        id: contentEntry?.providers?.contentful?.id,
        analystInsightType: contentEntry.contentType,
        bannerImageURL: BANNER_IMAGE_URL,
        createdAt: contentEntry.createdAt
      }
    ]);

    jest
      .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
      .mockImplementation(async (id: string): Promise<any> => {
        const matchingContentEntry = contentfulResponse.items.find(
          (contentfulEntry) => id === contentfulEntry.sys.id
        );
        return Promise.resolve(matchingContentEntry);
      });

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.LatestAnalysis,
          data: {
            title: LATEST_ANALYST_INSIGHT_TITLE,
            imageURL: BANNER_IMAGE_URL,
            analystInsightId: contentEntry.id
          },
          order: 2
        })
      ])
    });
  });

  it("should always return LatestQuickTake banner if a content entry exists", async () => {
    const LATEST_ANALYST_INSIGHT_TITLE = "Bond managers";
    const BANNER_IMAGE_URL = "some-image-url";

    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });
    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const contentEntry = await buildContentEntry({ contentType: ContentEntryContentTypeEnum.QUICK_TAKE });
    const contentfulResponse = buildContentfulContentEntriesResponse([
      {
        title: LATEST_ANALYST_INSIGHT_TITLE,
        id: contentEntry?.providers?.contentful?.id,
        analystInsightType: contentEntry.contentType,
        bannerImageURL: BANNER_IMAGE_URL,
        createdAt: contentEntry.createdAt
      }
    ]);

    jest
      .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
      .mockImplementation(async (id: string): Promise<any> => {
        const matchingContentEntry = contentfulResponse.items.find(
          (contentfulEntry) => id === contentfulEntry.sys.id
        );
        return Promise.resolve(matchingContentEntry);
      });

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.LatestQuickTake,
          data: {
            title: LATEST_ANALYST_INSIGHT_TITLE,
            imageURL: BANNER_IMAGE_URL,
            analystInsightId: contentEntry.id
          },
          order: 2
        })
      ])
    });
  });

  it("should always return LatestWeeklyReview banner if a content entry exists", async () => {
    const LATEST_ANALYST_INSIGHT_TITLE = "Bond managers";
    const BANNER_IMAGE_URL = "some-image-url";

    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });
    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const contentEntry = await buildContentEntry({ contentType: ContentEntryContentTypeEnum.WEEKLY_REVIEW });
    const contentfulResponse = buildContentfulContentEntriesResponse([
      {
        title: LATEST_ANALYST_INSIGHT_TITLE,
        id: contentEntry?.providers?.contentful?.id,
        analystInsightType: contentEntry.contentType,
        bannerImageURL: BANNER_IMAGE_URL,
        createdAt: contentEntry.createdAt
      }
    ]);

    jest
      .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
      .mockImplementation(async (id: string): Promise<any> => {
        const matchingContentEntry = contentfulResponse.items.find(
          (contentfulEntry) => id === contentfulEntry.sys.id
        );
        return Promise.resolve(matchingContentEntry);
      });

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.LatestWeeklyReview,
          data: {
            title: LATEST_ANALYST_INSIGHT_TITLE,
            imageURL: BANNER_IMAGE_URL,
            analystInsightId: contentEntry.id
          },
          order: 2
        })
      ])
    });
  });

  it("should return different banner from investment promotion category based on date", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });

    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const TODAY = new Date("2023-09-18T11:00:00Z");
    Date.now = jest.fn(() => TODAY.valueOf());

    let prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.WhySetUpMonthlyInvestment,
          order: 1,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });

    const sampleDuration = CATEGORY_CONFIG[CategoryEnum.INVESTMENT_PROMOTION].options?.sampleDuration as number;

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.WhatIsDCA,
          order: 1,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should not return repeating investment related banners from investment promotion category when user has an active repeating investment", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });

    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]])
    });

    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    await buildTopUpAutomation({
      owner: user.id,
      category: "TopUpAutomation",
      active: true
    });

    const TODAY = new Date("2023-09-18T11:00:00Z");
    Date.now = jest.fn(() => TODAY.valueOf());

    let prompts = await UserService.getPrompts(user, "banner");
    expect(prompts.bannerPrompts).toEqual(
      expect.arrayContaining([
        expect.not.objectContaining({
          bannerId: BannerEnum.WhySetUpMonthlyInvestment
        }),
        expect.not.objectContaining({
          bannerId: BannerEnum.WhatIsDCA
        }),
        expect.not.objectContaining({
          bannerId: BannerEnum.EarnFreeShares
        })
      ])
    );

    const sampleDuration = CATEGORY_CONFIG[CategoryEnum.INVESTMENT_PROMOTION].options?.sampleDuration as number;

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toMatchObject({
      bannerPrompts: expect.arrayContaining([
        expect.not.objectContaining({
          bannerId: BannerEnum.WhySetUpMonthlyInvestment
        }),
        expect.not.objectContaining({
          bannerId: BannerEnum.WhatIsDCA
        }),
        expect.not.objectContaining({
          bannerId: BannerEnum.EarnFreeShares
        })
      ])
    });

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toMatchObject({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnFreeShares,
          order: 0,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should not return repeating investment related banners from investment promotion category when user has not set up target portfolio", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });

    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]])
    });

    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const TODAY = new Date("2023-09-18T11:00:00Z");
    Date.now = jest.fn(() => TODAY.valueOf());

    const prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.WhySetUpMonthlyInvestment,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should return different banner from plan promotion category based on date", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });

    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const TODAY = new Date("2023-09-18T11:00:00Z");
    Date.now = jest.fn(() => TODAY.valueOf());

    let prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnBonusDividendPlus,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });

    const sampleDuration = CATEGORY_CONFIG[CategoryEnum.PLAN_PROMOTION].options?.sampleDuration as number;

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnCashbackPlus,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.PlanUpgradePlus,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should not return plan promotion banners if user has paid_mid plan", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });

    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });

    await buildSubscription({
      owner: user.id,
      price: "paid_mid_monthly"
    });

    const TODAY = new Date("2023-09-18T11:00:00Z");
    Date.now = jest.fn(() => TODAY.valueOf());

    let prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnBonusDividendPlus,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });

    const sampleDuration = CATEGORY_CONFIG[CategoryEnum.PLAN_PROMOTION].options?.sampleDuration as number;

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnCashbackPlus,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.PlanUpgradePlus,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should return different banners from learning guide promotion based on date", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED,
      portfolioConversionStatus: "completed"
    });
    await buildSubscription({
      owner: user.id
    });

    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]])
    });

    const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
    Date.now = jest.fn(() => TODAY.valueOf());

    let prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.HowToThinkOfRisk,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        }),
        expect.objectContaining({
          bannerId: BannerEnum.InvestingInThematics,
          order: 3,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });

    const sampleDuration = CATEGORY_CONFIG[CategoryEnum.LEARNING_GUIDE_PROMOTION].options
      ?.sampleDuration as number;

    Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
    prompts = await UserService.getPrompts(user, "banner");
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.NewToInvesting,
          order: 2,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        }),
        expect.objectContaining({
          bannerId: BannerEnum.GrowYourSavingsWithMoneyMarketFunds,
          order: 3,
          data: expect.objectContaining({
            title: expect.anything(),
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should not return savings promotion banner if the user has savings holdings", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });
    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 100, currency: "GBX" }]])
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
    Date.now = jest.fn(() => TODAY.valueOf());

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);
    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnInterestOnYourMoney,
          order: 1
        })
      ])
    });
  });

  it("should not return savings promotion banner if the user has no savings holdings but has a pending savings topup", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED
    });
    const portfolio = await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 0, currency: "GBX" }]])
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });
    await buildSavingsTopup({
      owner: user.id,
      portfolio: portfolio.id
    });

    const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
    Date.now = jest.fn(() => TODAY.valueOf());

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);
    expect(prompts).not.toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnInterestOnYourMoney,
          order: 1
        })
      ])
    });
  });

  it("should return GBP savings promotion banner if the user has no savings holdings and zero pending savings topup & has UK company entity", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED,
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
    });
    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_gbp", { amount: 0, currency: "GBX" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
    Date.now = jest.fn(() => TODAY.valueOf());

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnInterestOnYourMoney,
          order: 2,
          data: expect.objectContaining({
            title: "Earn up to 4.60% interest on your money!",
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });

  it("should return EUR savings promotion banner if the user has no savings holdings and zero pending savings topup & has European company entity", async () => {
    const user = await buildUser({
      kycStatus: KycStatusEnum.PASSED,
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    await buildPortfolio({
      owner: user.id,
      mode: PortfolioModeEnum.REAL,
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
      savings: new Map([["mmf_dist_eur", { amount: 0, currency: "EUC" }]]),
      initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
        assetCommonId: config.assetId,
        percentage: config.percentage
      }))
    });
    await buildSubscription({
      owner: user.id,
      price: "free_monthly"
    });

    const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
    Date.now = jest.fn(() => TODAY.valueOf());

    const prompts = await UserService.getPrompts(user, "banner");

    expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_WITH_TARGET_USERS + 1);
    expect(prompts).toEqual({
      bannerPrompts: expect.arrayContaining([
        expect.objectContaining({
          bannerId: BannerEnum.EarnInterestOnYourMoney,
          order: 2,
          data: expect.objectContaining({
            title: "Earn up to 3.50% interest on your money!",
            modalTitle: expect.anything(),
            modalContent: expect.anything(),
            modalButtonText: expect.anything()
          })
        })
      ])
    });
  });
});
