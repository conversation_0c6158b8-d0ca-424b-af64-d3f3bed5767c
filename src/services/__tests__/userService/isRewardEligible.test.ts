import Decimal from "decimal.js";
import { faker } from "@faker-js/faker";
import { entitiesConfig, rewardsConfig } from "@wealthyhood/shared-configs";
import { KycStatusEnum } from "../../../models/User";
import UserService from "../../userService";
import {
  buildUser,
  buildAssetTransaction,
  buildDepositCashTransaction,
  buildUserDataRequest,
  buildOrder
} from "../../../tests/utils/generateModels";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { DepositMethodEnum } from "../../../types/transactions";

const { MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY, MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY } = rewardsConfig;

describe("UserService.isRewardEligible", () => {
  beforeAll(async () => await connectDb("isRewardEligible"));
  afterAll(async () => await closeDb());
  afterEach(async () => await clearDb());

  describe("when the user is a UK user (WEALTHYHOOD_UK)", () => {
    it("should require investment check for UK users", async () => {
      // Ineligible: no investments
      let user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        kycStatus: KycStatusEnum.PASSED
      });
      let eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);

      // Eligible: investments >= threshold
      user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        kycStatus: KycStatusEnum.PASSED
      });
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolioTransactionCategory: "buy",
        consideration: {
          amount: Decimal.add(MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY, 1).mul(1).toNumber(),
          currency: "GBP"
        },
        status: "Settled"
      });
      // Create the order that the eligibility logic expects
      await buildOrder({
        transaction: assetTransaction.id,
        side: "Buy",
        status: "Settled",
        consideration: {
          originalAmount: Decimal.add(MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY, 1).mul(1).toNumber(),
          amount: Decimal.add(MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY, 1).mul(1).toNumber(),
          currency: "GBP"
        }
      });
      eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(true);
    });
  });

  describe("when the user is an EU user (WEALTHYHOOD_EUROPE)", () => {
    it("should require deposit check for EU users", async () => {
      // Ineligible: no eligible deposits
      let user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED
      });
      let eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);

      // Eligible: deposit >= threshold, BANK_TRANSFER, Devengo status 'confirmed'
      user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED
      });
      await buildDepositCashTransaction({
        owner: user.id,
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        status: "Pending",
        consideration: {
          amount: Decimal.add(MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY, 1).mul(100).toNumber(),
          currency: "EUR"
        },
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  status: "confirmed",
                  accountId: "test-account-id"
                }
              }
            }
          }
        }
      });
      eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(true);
    });
  });

  describe("when the user is a whitelisted user", () => {
    it("should skip investment and deposit checks for whitelisted users", async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        usedWhitelistCode: true // triggers hasUsedWhitelistCodeOrEmail virtual
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(true);
    });
  });

  describe("when the user has a disassociation request", () => {
    it("should return false", async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        kycStatus: KycStatusEnum.PASSED
      });
      await buildUserDataRequest({
        owner: user.id,
        requestType: "disassociation"
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);
    });
  });

  describe("when the user has failed KYC", () => {
    it("should return false", async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        kycStatus: KycStatusEnum.FAILED
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);
    });
  });

  describe("when the user is Greek and has been referred", () => {
    it("should skip all other checks and return true for Greek referred users", async () => {
      const referrerEmail = faker.internet.email();
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        residencyCountry: "GR",
        referredByEmail: referrerEmail
        // Note: No deposits, no investments, not whitelisted - but should still be eligible
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(true);
    });

    it("should still require KYC for Greek referred users", async () => {
      const referrerEmail = faker.internet.email();
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.FAILED, // Failed KYC should still block eligibility
        residencyCountry: "GR",
        referredByEmail: referrerEmail
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);
    });

    it("should not apply Greek exception to non-Greek users", async () => {
      const referrerEmail = faker.internet.email();
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        residencyCountry: "DE", // German user, not Greek
        referredByEmail: referrerEmail
        // No deposits - should be ineligible despite being referred
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);
    });

    it("should not apply Greek exception to non-referred Greek users", async () => {
      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        residencyCountry: "GR"
        // Greek user but not referred - should require deposits
      });
      const eligible = await UserService.isRewardEligible(user, { referral: user.id.toString() });
      expect(eligible).toBe(false);
    });
  });
});
