import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import SubscriptionService from "../subscriptionService";
import { ChargeTransaction, ChargeTransactionDocument } from "../../models/Transaction";
import {
  buildAssetTransaction,
  buildChargeTransaction,
  buildDailyPortfolioTicker,
  buildHoldingDTO,
  buildMandate,
  buildOrder,
  buildPortfolio,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import { PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { UserDocument } from "../../models/User";
import Decimal from "decimal.js";
import { entitiesConfig, fees, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { Subscription, SubscriptionDocument, SubscriptionDTOInterface } from "../../models/Subscription";
import { faker } from "@faker-js/faker";
import { MandateDocument } from "../../models/Mandate";
import DateUtil from "../../utils/dateUtil";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { StripeService } from "../../external-services/stripeService";
import { InternalServerError } from "../../models/ApiErrors";
import { StripePricesEnum } from "../../configs/stripeConfig";
import { CurrencyEnum } from "../../external-services/wealthkernelService";
import { OrderSubmissionIntentEnum } from "../../models/Order";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MINIMUM_CUSTODY_FEE } = fees;

const ZERO_GBP_FEES = {
  fx: {
    currency: CurrencyEnum.GBP,
    amount: 0
  },
  commission: {
    currency: CurrencyEnum.GBP,
    amount: 0
  },
  executionSpread: {
    currency: CurrencyEnum.GBP,
    amount: 0
  },
  realtimeExecution: {
    currency: CurrencyEnum.GBP,
    amount: 0
  }
};

describe("SubscriptionService", () => {
  beforeAll(async () => await connectDb("SubscriptionService"));
  afterAll(async () => await closeDb());

  describe("createCustodyFeeCharges", () => {
    describe("when there are no users with a free plan subscription", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should not create any charge transactions", async () => {
        const charges = await ChargeTransaction.find({});
        expect(charges.length).toBe(0);
      });
    });

    describe("when there is a user with a free plan subscription but they have never invested", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        const user = await buildUser({ portfolioConversionStatus: "notStarted" });

        // User has a portfolio without holdings and cash
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should not create any charge transactions", async () => {
        const charges = await ChargeTransaction.find({});
        expect(charges.length).toBe(0);
      });
    });

    describe("when there is a user with a free plan subscription but they are fully withdrawn for the whole month", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        const user = await buildUser({ portfolioConversionStatus: "completed" });

        // User has a portfolio without holdings and cash
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should not create any charge transactions", async () => {
        const charges = await ChargeTransaction.find({});
        expect(charges.length).toBe(0);
      });
    });

    describe("when there is a user with a free plan subscription but they have no holdings to be charged", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const weekdaysWithTicker = [
        new Date("2023-03-23T12:00:00Z"),
        new Date("2023-03-24T12:00:00Z"),
        new Date("2023-03-27T12:00:00Z"),
        new Date("2023-03-28T12:00:00Z"),
        new Date("2023-03-29T12:00:00Z"),
        new Date("2023-03-30T12:00:00Z"),
        new Date("2023-03-31T12:00:00Z")
      ];

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2023-03-31T12:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });

        // User has a portfolio without holdings and cash
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", faker.number.int({ min: 1, max: 10 }))], // User has a single holding, but it's in a pending sell order
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } } // User does not have cash
        });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Pending"
        });
        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            quantity: portfolio.holdings.find((holding) => holding.assetCommonId === "equities_uk")!.quantity
          })
        ];
        await transaction.save();

        // Build tickers for each week day of the month (value of portfolio has not changed)
        await Promise.all(
          weekdaysWithTicker.map((date) => {
            return buildDailyPortfolioTicker({
              portfolio: portfolio._id,
              date,
              pricePerCurrency: { GBP: 1000 }
            });
          })
        );

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should not create any charge transactions", async () => {
        const charges = await ChargeTransaction.find({});
        expect(charges.length).toBe(0);
      });
    });

    describe("when there is a user with a free plan subscription but they already have a pending custody charge transaction", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ portfolioConversionStatus: "completed" });

        // User has a portfolio without holdings and cash
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 10, { price: 100 })], // User has holdings of value £1000
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const subscription = await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await buildChargeTransaction({
          chargeMethod: "holdings",
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          subscription: subscription.id,
          chargeType: "custody"
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should not create any new charge transactions", async () => {
        const charges = await ChargeTransaction.find({});
        expect(charges.length).toBe(1);
      });
    });

    describe("when there is a user with a free plan subscription that was invested for all days of the month", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2023-03-31T12:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });

        // User has a portfolio without holdings and cash
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 10, { price: 100 })], // User has holdings of value £1000
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const weekdaysInChargeMonth = [
          new Date("2023-03-01T12:00:00Z"),
          new Date("2023-03-02T12:00:00Z"),
          new Date("2023-03-03T12:00:00Z"),
          new Date("2023-03-06T12:00:00Z"),
          new Date("2023-03-07T12:00:00Z"),
          new Date("2023-03-08T12:00:00Z"),
          new Date("2023-03-09T12:00:00Z"),
          new Date("2023-03-10T12:00:00Z"),
          new Date("2023-03-13T12:00:00Z"),
          new Date("2023-03-14T12:00:00Z"),
          new Date("2023-03-15T12:00:00Z"),
          new Date("2023-03-16T12:00:00Z"),
          new Date("2023-03-17T12:00:00Z"),
          new Date("2023-03-20T12:00:00Z"),
          new Date("2023-03-21T12:00:00Z"),
          new Date("2023-03-22T12:00:00Z"),
          new Date("2023-03-23T12:00:00Z"),
          new Date("2023-03-24T12:00:00Z"),
          new Date("2023-03-27T12:00:00Z"),
          new Date("2023-03-28T12:00:00Z"),
          new Date("2023-03-29T12:00:00Z"),
          new Date("2023-03-30T12:00:00Z"),
          new Date("2023-03-31T12:00:00Z")
        ];

        // Build tickers for each week day of the month (value of portfolio has not changed)
        await Promise.all(
          weekdaysInChargeMonth.map((date) => {
            return buildDailyPortfolioTicker({
              portfolio: portfolio._id,
              date,
              pricePerCurrency: { GBP: 1000 }
            });
          })
        );

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should create a charge transaction with sell orders", async () => {
        const charges: ChargeTransactionDocument[] = await ChargeTransaction.find({}).populate("orders");

        const charge = charges[0];
        expect(charge).toEqual(
          expect.objectContaining({
            category: "ChargeTransaction",
            chargeMethod: "holdings",
            chargeType: "custody",
            consideration: {
              currency: "GBP",
              amount: 15, // Average portfolio value multiplied by monthly custody charge in cents
              holdingsAmount: 15
            },
            orders: expect.arrayContaining([
              expect.objectContaining({
                isin: ASSET_CONFIG["equities_us"].isin,
                side: "Sell",
                quantity: 0.0015,
                transaction: charge._id,
                fees: expect.objectContaining(ZERO_GBP_FEES),
                isSubmittedToBroker: false,
                submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
              })
            ]),
            owner: user._id,
            portfolio: portfolio._id
          })
        );
      });
    });

    describe("when there is a user with a free plan subscription that was invested for some days of the month", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const weekdaysWithTicker = [
        new Date("2023-03-23T12:00:00Z"),
        new Date("2023-03-24T12:00:00Z"),
        new Date("2023-03-27T12:00:00Z"),
        new Date("2023-03-28T12:00:00Z"),
        new Date("2023-03-29T12:00:00Z"),
        new Date("2023-03-30T12:00:00Z"),
        new Date("2023-03-31T12:00:00Z")
      ];

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2023-03-31T12:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });

        // User has a portfolio without holdings and cash
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 10, { price: 100 })], // User has holdings of value £1000
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } } // User does not have cash
        });

        // Build tickers for each week day of the month (value of portfolio has not changed)
        await Promise.all(
          weekdaysWithTicker.map((date) => {
            return buildDailyPortfolioTicker({
              portfolio: portfolio._id,
              date,
              pricePerCurrency: { GBP: 1000 }
            });
          })
        );

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should create a charge transaction with sell orders", async () => {
        const charges: ChargeTransactionDocument[] = await ChargeTransaction.find({}).populate("orders");

        const charge = charges[0];
        expect(charge).toEqual(
          expect.objectContaining({
            category: "ChargeTransaction",
            chargeMethod: "holdings",
            chargeType: "custody",
            consideration: {
              currency: "GBP",
              amount: 5, // Average portfolio value multiplied by monthly custody charge in cents
              holdingsAmount: 5
            },
            orders: expect.arrayContaining([
              expect.objectContaining({
                isin: ASSET_CONFIG["equities_us"].isin,
                side: "Sell",
                quantity: 0.0005,
                transaction: charge._id,
                fees: expect.objectContaining(ZERO_GBP_FEES),
                isSubmittedToBroker: false,
                submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
              })
            ]),
            owner: user._id,
            portfolio: portfolio._id
          })
        );
      });
    });

    describe("when there is a user with a free plan subscription that was invested for all days of the month and custody charge is less than minimum", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2023-03-31T12:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });

        // User has a portfolio without holdings and cash
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1, { price: 100 })], // User has holdings of value £100
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } } // User does not have cash
        });

        const weekdaysInChargeMonth = [
          new Date("2023-03-01T12:00:00Z"),
          new Date("2023-03-02T12:00:00Z"),
          new Date("2023-03-03T12:00:00Z"),
          new Date("2023-03-06T12:00:00Z"),
          new Date("2023-03-07T12:00:00Z"),
          new Date("2023-03-08T12:00:00Z"),
          new Date("2023-03-09T12:00:00Z"),
          new Date("2023-03-10T12:00:00Z"),
          new Date("2023-03-13T12:00:00Z"),
          new Date("2023-03-14T12:00:00Z"),
          new Date("2023-03-15T12:00:00Z"),
          new Date("2023-03-16T12:00:00Z"),
          new Date("2023-03-17T12:00:00Z"),
          new Date("2023-03-20T12:00:00Z"),
          new Date("2023-03-21T12:00:00Z"),
          new Date("2023-03-22T12:00:00Z"),
          new Date("2023-03-23T12:00:00Z"),
          new Date("2023-03-24T12:00:00Z"),
          new Date("2023-03-27T12:00:00Z"),
          new Date("2023-03-28T12:00:00Z"),
          new Date("2023-03-29T12:00:00Z"),
          new Date("2023-03-30T12:00:00Z"),
          new Date("2023-03-31T12:00:00Z")
        ];

        // Build tickers for each week day of the month (value of portfolio has not changed)
        await Promise.all(
          weekdaysInChargeMonth.map((date) => {
            return buildDailyPortfolioTicker({ portfolio: portfolio._id, date, pricePerCurrency: { GBP: 100 } });
          })
        );

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should create a charge transaction with sell orders", async () => {
        const charges: ChargeTransactionDocument[] = await ChargeTransaction.find({}).populate("orders");

        const charge = charges[0];
        expect(charge).toEqual(
          expect.objectContaining({
            category: "ChargeTransaction",
            chargeMethod: "holdings",
            chargeType: "custody",
            consideration: {
              currency: "GBP",
              amount: Decimal.mul(MINIMUM_CUSTODY_FEE, 100).toNumber(), // Since the custody charge for this portfolio is less that minimum, we charge the minimum instead
              holdingsAmount: Decimal.mul(MINIMUM_CUSTODY_FEE, 100).toNumber()
            },
            orders: expect.arrayContaining([
              expect.objectContaining({
                isin: ASSET_CONFIG["equities_us"].isin,
                side: "Sell",
                quantity: 0.0002,
                transaction: charge._id,
                fees: expect.objectContaining(ZERO_GBP_FEES),
                isSubmittedToBroker: false,
                submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
              })
            ]),
            owner: user._id,
            portfolio: portfolio._id
          })
        );
      });
    });

    describe("when there is a European user with a free plan subscription that was invested for all days of the month", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2023-03-31T12:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({
          portfolioConversionStatus: "completed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });

        // User has a portfolio with holdings
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 10, { price: 100 })], // User has holdings of value £1000
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const weekdaysInChargeMonth = [
          new Date("2023-03-01T12:00:00Z"),
          new Date("2023-03-02T12:00:00Z"),
          new Date("2023-03-03T12:00:00Z"),
          new Date("2023-03-06T12:00:00Z"),
          new Date("2023-03-07T12:00:00Z"),
          new Date("2023-03-08T12:00:00Z"),
          new Date("2023-03-09T12:00:00Z"),
          new Date("2023-03-10T12:00:00Z"),
          new Date("2023-03-13T12:00:00Z"),
          new Date("2023-03-14T12:00:00Z"),
          new Date("2023-03-15T12:00:00Z"),
          new Date("2023-03-16T12:00:00Z"),
          new Date("2023-03-17T12:00:00Z"),
          new Date("2023-03-20T12:00:00Z"),
          new Date("2023-03-21T12:00:00Z"),
          new Date("2023-03-22T12:00:00Z"),
          new Date("2023-03-23T12:00:00Z"),
          new Date("2023-03-24T12:00:00Z"),
          new Date("2023-03-27T12:00:00Z"),
          new Date("2023-03-28T12:00:00Z"),
          new Date("2023-03-29T12:00:00Z"),
          new Date("2023-03-30T12:00:00Z"),
          new Date("2023-03-31T12:00:00Z")
        ];

        // Build tickers for each week day of the month (value of portfolio has not changed)
        await Promise.all(
          weekdaysInChargeMonth.map((date) => {
            return buildDailyPortfolioTicker({
              portfolio: portfolio._id,
              date,
              pricePerCurrency: { GBP: 0, EUR: 1000, USD: 0 }
            });
          })
        );

        await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          price: "free_monthly",
          active: true
        });

        await SubscriptionService.createCustodyFeeCharges("2023-03");
      });
      afterAll(async () => await clearDb());

      it("should not create any charge transactions for European users", async () => {
        const charges = await ChargeTransaction.find({});
        expect(charges.length).toBe(0);
      });
    });
  });

  describe("createSubscription", () => {
    describe("when user creates a FeeBasedSubscription", () => {
      let owner: UserDocument;
      let subscriptionData: SubscriptionDTOInterface;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });

        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(0);

        subscriptionData = {
          active: true,
          owner: owner.id,
          price: "free_monthly",
          category: "FeeBasedSubscription",
          nextChargeAt: new Date("2022-08-26T11:00:00Z")
        };
        await SubscriptionService.createSubscription(subscriptionData);
      });
      afterAll(async () => await clearDb());

      it("should create a subscription document", async () => {
        const subscription = await Subscription.findOne({});
        expect(subscription).toMatchObject(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: true
          })
        );
      });
    });

    describe("when user creates a DirectDebitSubscription", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });

        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(0);
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          async () =>
            await SubscriptionService.createSubscription({
              active: true,
              owner: owner.id,
              price: "paid_low_monthly",
              category: "DirectDebitSubscription",
              mandate: mandate.id,
              nextChargeAt: new Date("2022-08-26T11:00:00Z"),
              expiration: {
                date: new Date("2022-11-29T11:00:00Z"),
                downgradesTo: "free_monthly"
              }
            })
        ).rejects.toThrow(new InternalServerError("Invalid price/subscription category combination"));
      });
    });

    describe("when user creates a SinglePaymentSubscription", () => {
      let owner: UserDocument;
      let subscriptionData: SubscriptionDTOInterface;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });

        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(0);

        subscriptionData = {
          active: true,
          owner: owner.id,
          price: "paid_low_lifetime_blackfriday_2023",
          category: "SinglePaymentSubscription",
          nextChargeAt: new Date("2022-08-26T11:00:00Z")
        };
        await SubscriptionService.createSubscription(subscriptionData);
      });
      afterAll(async () => await clearDb());

      it("should create a free, fee-based subscription document", async () => {
        const subscription = await Subscription.findOne({});
        expect(subscription).toMatchObject(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: true
          })
        );
      });

      it("should not emit a plan updated event", async () => {
        const subscription = await Subscription.findOne({});
        expect(subscription).toMatchObject(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: true
          })
        );
      });
    });
  });

  describe("updateSubscription", () => {
    describe("when user upgrades from free to a direct debit subscription", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          async () =>
            await SubscriptionService.updateSubscription(subscription.id, {
              category: "DirectDebitSubscription",
              mandate: mandate.id,
              price: "paid_low_monthly"
            })
        ).rejects.toThrow(new InternalServerError("Invalid price/subscription category combination"));
      });
    });

    describe("when user downgrades from a direct debit subscription", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");
        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          mandate: mandate.id,
          active: true,
          price: "paid_mid_monthly",
          owner: owner.id,
          nextChargeAt: new Date("2022-07-06")
        });
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          async () =>
            await SubscriptionService.updateSubscription(subscription.id, {
              category: "FeeBasedSubscription",
              price: "free_monthly"
            })
        ).rejects.toThrow(
          new InternalServerError(
            "The user should not be updating from paid_mid_monthly/DirectDebitSubscription to free_monthly/FeeBasedSubscription"
          )
        );
      });
    });

    describe("when user upgrades from a direct debit subscription", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          mandate: mandate.id,
          nextChargeAt: DateUtil.getDateAfterNdays(TODAY, 15) // The user upgrades 15 days before the charge date
        });
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          async () =>
            await SubscriptionService.updateSubscription(subscription.id, {
              category: "DirectDebitSubscription",
              price: "paid_mid_monthly"
            })
        ).rejects.toThrow(new InternalServerError("Invalid price/subscription category combination"));
      });
    });

    describe("when user upgrades from free to card based subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          async () =>
            await SubscriptionService.updateSubscription(subscription.id, {
              category: "CardPaymentSubscription",
              price: "paid_low_monthly"
            })
        ).rejects.toThrow(
          new InternalServerError(
            "The user should not be updating from free_monthly/FeeBasedSubscription to paid_low_monthly/CardPaymentSubscription"
          )
        );
      });
    });

    describe("when user upgrades from free to paid_low (lifetime)", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "SinglePaymentSubscription",
          price: "paid_low_lifetime_blackfriday_2023"
        });
      });
      afterAll(async () => await clearDb());

      it("should not update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: true
          })
        );
      });

      it("should not create a new pending subscription charge", async () => {
        const chargeTransaction = await ChargeTransaction.findOne({ chargeType: "subscription" });
        expect(chargeTransaction).toBeNull();
      });

      it("should not emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when user upgrades from paid_low to paid_mid (lifetime)", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          mandate: mandate.id,
          nextChargeAt: DateUtil.getDateAfterNdays(TODAY, 15) // The user upgrades 15 days before the charge date
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "SinglePaymentSubscription",
          price: "paid_mid_lifetime_blackfriday_2023"
        });
      });
      afterAll(async () => await clearDb());

      it("should not update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_monthly",
            owner: owner._id,
            category: "DirectDebitSubscription",
            active: true,
            mandate: mandate._id
          })
        );
      });

      it("should not create a new pending subscription charge", async () => {
        const chargeTransaction = await ChargeTransaction.findOne({ chargeType: "subscription" });
        expect(chargeTransaction).toBeNull();
      });

      it("should not emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when user upgrades from a card based subscription to a higher card based subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ITEM_ID = faker.string.uuid();

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrieveSubscription").mockResolvedValue({
          id: STRIPE_SUBSCRIPTION_ID,
          items: {
            data: [
              {
                id: STRIPE_SUBSCRIPTION_ITEM_ID
              }
            ]
          }
        } as any);
        jest.spyOn(StripeService.Instance, "updateSubscription");

        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_SUBSCRIPTION_ID,
              status: "active"
            }
          }
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "CardPaymentSubscription",
          price: "paid_mid_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_mid_monthly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true
          })
        );
      });

      it("should call the Stripe API to update the subscription", async () => {
        expect(StripeService.Instance.updateSubscription).toHaveBeenCalledWith(STRIPE_SUBSCRIPTION_ID, {
          cancel_at_period_end: false,
          proration_behavior: "always_invoice",
          items: [
            {
              id: STRIPE_SUBSCRIPTION_ITEM_ID,
              price: StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP
            }
          ]
        });
      });

      it("should not create a new pending subscription charge", async () => {
        const chargeTransactions = await ChargeTransaction.find({ chargeType: "subscription" });
        expect(chargeTransactions.length).toEqual(0);
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_low",
            fromRecurrence: "monthly",
            to: "paid_mid",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user upgrades from a card based subscription to a higher card based subscription during expiration period", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ITEM_ID = faker.string.uuid();

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrieveSubscription").mockResolvedValue({
          id: STRIPE_SUBSCRIPTION_ID,
          items: {
            data: [
              {
                id: STRIPE_SUBSCRIPTION_ITEM_ID
              }
            ]
          }
        } as any);
        jest.spyOn(StripeService.Instance, "updateSubscription");

        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_SUBSCRIPTION_ID,
              status: "active"
            }
          },
          expiration: {
            date: DateUtil.getDateAfterNdays(TODAY, 10),
            downgradesTo: "free_monthly"
          }
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "CardPaymentSubscription",
          price: "paid_mid_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_mid_monthly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true
          })
        );
      });

      it("should call the Stripe API to update the subscription", async () => {
        expect(StripeService.Instance.updateSubscription).toHaveBeenCalledWith(STRIPE_SUBSCRIPTION_ID, {
          cancel_at_period_end: false,
          proration_behavior: "always_invoice",
          items: [
            {
              id: STRIPE_SUBSCRIPTION_ITEM_ID,
              price: StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP
            }
          ]
        });
      });

      it("should not create a new pending subscription charge", async () => {
        const chargeTransactions = await ChargeTransaction.find({ chargeType: "subscription" });
        expect(chargeTransactions.length).toEqual(0);
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_low",
            fromRecurrence: "monthly",
            to: "paid_mid",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user downgrades from a card based subscription to free", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const EXPIRATION_DATE = 1708961000;

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(StripeService.Instance, "updateSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE
        } as any);

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "FeeBasedSubscription",
          price: "free_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription with an expiration object", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_monthly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true,
            expiration: expect.objectContaining({
              date: new Date("2024-02-26T15:23:20.000Z"),
              downgradesTo: "free_monthly"
            })
          })
        );
      });

      it("should emit a 'onPlanUpdated' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeInit.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_low",
            fromRecurrence: "monthly",
            to: "free",
            toRecurrence: "monthly"
          }
        );
      });

      it("should call Stripe to cancel the subscription on the period end", () => {
        expect(StripeService.Instance.updateSubscription).toHaveBeenCalledWith(subscription.providers!.stripe.id, {
          cancel_at_period_end: true
        });
      });
    });

    describe("when user downgrades from a card based subscription to a lower card based subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ITEM_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrieveSubscription").mockResolvedValue({
          id: STRIPE_SUBSCRIPTION_ID,
          items: {
            data: [
              {
                id: STRIPE_SUBSCRIPTION_ITEM_ID
              }
            ]
          }
        } as any);
        jest.spyOn(StripeService.Instance, "updateSubscription");
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_mid_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_SUBSCRIPTION_ID
            }
          }
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "CardPaymentSubscription",
          price: "paid_low_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription without an expiration object", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_monthly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true,
            expiration: undefined
          })
        );
      });

      it("should call the Stripe API to update the subscription", async () => {
        expect(StripeService.Instance.updateSubscription).toHaveBeenCalledWith(STRIPE_SUBSCRIPTION_ID, {
          items: [
            {
              id: STRIPE_SUBSCRIPTION_ITEM_ID,
              price: StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_GBP
            }
          ]
        });
      });

      it("should emit a 'onPlanUpdated' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeInit.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_mid",
            fromRecurrence: "monthly",
            to: "paid_low",
            toRecurrence: "monthly"
          }
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeCompletion.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_mid",
            fromRecurrence: "monthly",
            to: "paid_low",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user downgrades from a card based subscription to a lower card based subscription during expiration", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const EXPIRATION_DATE = 1708961000;
      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ITEM_ID = faker.string.uuid();
      const TODAY = new Date("2023-10-30T11:00:00Z");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(StripeService.Instance, "retrieveSubscription").mockResolvedValue({
          id: STRIPE_SUBSCRIPTION_ID,
          items: {
            data: [
              {
                id: STRIPE_SUBSCRIPTION_ITEM_ID
              }
            ]
          }
        } as any);
        jest.spyOn(StripeService.Instance, "updateSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE
        } as any);
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_mid_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_SUBSCRIPTION_ID
            }
          },
          expiration: {
            date: DateUtil.getDateAfterNdays(TODAY, 10),
            downgradesTo: "free_monthly"
          }
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "CardPaymentSubscription",
          price: "paid_low_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription without an expiration object", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_monthly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true,
            expiration: undefined
          })
        );
      });

      it("should call the Stripe API to update the subscription", async () => {
        expect(StripeService.Instance.updateSubscription).toHaveBeenCalledWith(STRIPE_SUBSCRIPTION_ID, {
          items: [
            {
              id: STRIPE_SUBSCRIPTION_ITEM_ID,
              price: StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_GBP
            }
          ]
        });
      });

      it("should emit a 'onPlanUpdated' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeInit.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_mid",
            fromRecurrence: "monthly",
            to: "paid_low",
            toRecurrence: "monthly"
          }
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeCompletion.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_mid",
            fromRecurrence: "monthly",
            to: "paid_low",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user downgrades from a lifetime plan to free", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");
        jest.resetAllMocks();
        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "SinglePaymentSubscription",
          active: true,
          price: "paid_low_lifetime_blackfriday_2023",
          owner: owner.id,
          nextChargeAt: new Date("2022-07-06")
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "FeeBasedSubscription",
          price: "free_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription without an expiration object", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: true
          })
        );

        expect(updatedSubscription!.expiration).toBeUndefined();
      });

      it("should emit a 'onPlanDowngradeInit' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeInit.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_low",
            fromRecurrence: "lifetime",
            to: "free",
            toRecurrence: "monthly"
          }
        );
      });

      it("should emit a 'onPlanDowngradeCompletion' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeCompletion.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_low",
            fromRecurrence: "lifetime",
            to: "free",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user downgrades from a lifetime plan to a card based subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");
        jest.resetAllMocks();
        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "SinglePaymentSubscription",
          active: true,
          price: "paid_mid_lifetime_blackfriday_2023",
          owner: owner.id
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "CardPaymentSubscription",
          price: "paid_low_monthly"
        });
      });
      afterAll(async () => await clearDb());

      it("should successfully update the subscription to a lifetime paid_low plan", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_lifetime_blackfriday_2023",
            owner: owner._id,
            category: "SinglePaymentSubscription",
            active: true
          })
        );

        expect(updatedSubscription!.expiration).toBeUndefined();
      });

      it("should emit a 'onPlanDowngradeInit' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeInit.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_mid",
            fromRecurrence: "lifetime",
            to: "paid_low",
            toRecurrence: "lifetime"
          }
        );
      });

      it("should emit a 'onPlanDowngradeCompletion' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planDowngradeCompletion.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_mid",
            fromRecurrence: "lifetime",
            to: "paid_low",
            toRecurrence: "lifetime"
          }
        );
      });
    });

    describe("when user downgrades from a lifetime plan to a lower lifetime plan", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "SinglePaymentSubscription",
          active: true,
          price: "paid_mid_lifetime_blackfriday_2023",
          owner: owner.id
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "SinglePaymentSubscription",
          price: "paid_low_lifetime_blackfriday_2023"
        });
      });
      afterAll(async () => await clearDb());

      it("should not update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_mid_lifetime_blackfriday_2023",
            owner: owner._id,
            category: "SinglePaymentSubscription",
            active: true
          })
        );
      });

      it("should not emit a 'onPlanUpdated' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when user switches from a card based subscription to the equal lifetime plan", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          nextChargeAt: DateUtil.getDateAfterNdays(TODAY, 15) // The user upgrades 15 days before the charge date
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "SinglePaymentSubscription",
          price: "paid_low_lifetime_blackfriday_2023"
        });
      });
      afterAll(async () => await clearDb());

      it("should not update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_monthly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true
          })
        );
      });

      it("should not create a new pending subscription charge", async () => {
        const chargeTransaction = await ChargeTransaction.findOne({ chargeType: "subscription" });
        expect(chargeTransaction).toBeNull();
      });

      it("should not emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when user switches from a card based subscription (monthly) to the equal yearly plan", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      const TODAY = new Date("2022-07-29T11:00:00Z");
      const YEAR_FROM_NOW_SECONDS = 1690628400;
      const YEAR_FROM_NOW = new Date("2023-07-29T11:00:00Z");
      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ITEM_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(StripeService.Instance, "retrieveSubscription").mockResolvedValue({
          id: STRIPE_SUBSCRIPTION_ID,
          items: {
            data: [
              {
                id: STRIPE_SUBSCRIPTION_ITEM_ID
              }
            ]
          }
        } as any);
        jest.spyOn(StripeService.Instance, "updateSubscription").mockResolvedValue({
          current_period_end: YEAR_FROM_NOW_SECONDS
        } as any);
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser({ kycPassed: true });
        await buildPortfolio({ owner: owner.id });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          nextChargeAt: DateUtil.getDateAfterNdays(TODAY, 15), // The user upgrades 15 days before the charge date
          providers: {
            stripe: {
              id: STRIPE_SUBSCRIPTION_ID
            }
          }
        });

        await SubscriptionService.updateSubscription(subscription.id, {
          category: "CardPaymentSubscription",
          price: "paid_low_yearly"
        });
      });
      afterAll(async () => await clearDb());

      it("should update the subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            price: "paid_low_yearly",
            owner: owner._id,
            category: "CardPaymentSubscription",
            active: true,
            nextChargeAt: YEAR_FROM_NOW
          })
        );
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "paid_low",
            fromRecurrence: "monthly",
            to: "paid_low",
            toRecurrence: "yearly"
          }
        );
      });
    });
  });

  describe("renewSubscription", () => {
    describe("when there is a card subscription which is going to be expired in the future", () => {
      let user: UserDocument;
      let subscription: SubscriptionDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        jest
          .spyOn(StripeService.Instance, "updateSubscription")
          .mockResolvedValue({ id: faker.string.uuid() } as any);

        user = await buildUser({});
        await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "CardPaymentSubscription",
          active: true,
          expiration: {
            date: new Date("2022-09-01T11:00:00Z"), // Expiration date is in the future
            downgradesTo: "free_monthly"
          },
          providers: {
            stripe: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });

        await SubscriptionService.renewSubscription(subscription.id);
      });
      afterAll(async () => await clearDb());

      it("should remove the expiration block from the subscription", async () => {
        const updatedSubscription = await Subscription.findById(subscription._id);
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            owner: user._id,
            category: "CardPaymentSubscription",
            price: "paid_low_monthly",
            expiration: undefined
          })
        );
      });

      it("should call the Stripe API to abort cancelling the subscription", async () => {
        expect(StripeService.Instance.updateSubscription).toHaveBeenCalledWith(subscription.providers!.stripe.id, {
          cancel_at_period_end: false
        });
      });
    });
  });
});
