import { countriesConfig } from "@wealthyhood/shared-configs";
import {
  ReviewAnswerType,
  ReviewStatusType,
  SumsubService,
  SumsubLevelEnum
} from "../external-services/sumsubService";

export const WORKFLOW_TIMEOUT_MINUTES = 60;

type WorkflowType = {
  sdkToken: string;
};

type ApplicantType = {
  id: string;
  reviewId: string;
  status: ReviewStatusType;
  decision?: ReviewAnswerType;
  passportDetails: PassportDetails;
};

export type PassportDetails = {
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date;
  nationality?: countriesConfig.CountryCodesType;
  idDocs: IdDocType[];
};

type IdDocType = {
  idDocType: string; // Can be 'PASSPORT', 'ID_CARD', etc.
  mrzLine1?: string;
  country?: string; // alpha-3 country code (e.g. GRC)
};

export interface KycServiceInterface {
  createKycWorkflow(userId: string): Promise<WorkflowType>;

  runAMLCheck(applicantId: string): Promise<void>;

  retrieveApplicant(userId: string): Promise<ApplicantType>;
}

export class SumsubBasedKycService implements KycServiceInterface {
  _sumsubClient: SumsubService;

  constructor() {
    this._sumsubClient = SumsubService.Instance;
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public async createKycWorkflow(userId: string): Promise<WorkflowType> {
    const workflow = await this._sumsubClient.generateAccessToken(userId);

    return {
      sdkToken: workflow.token
    };
  }

  public async runAMLCheck(applicantId: string): Promise<void> {
    await this._sumsubClient.runAMLCheck(applicantId);
  }

  public async retrieveApplicant(userId: string): Promise<ApplicantType> {
    const applicant = await this._sumsubClient.retrieveApplicant(userId);

    return {
      id: applicant.id,
      reviewId: applicant.review?.reviewId,
      status: applicant.review?.reviewStatus,
      decision: applicant.review?.reviewResult?.reviewAnswer,
      passportDetails: {
        firstName: applicant.info?.firstName,
        lastName: applicant.info?.lastName,
        dateOfBirth: applicant.info?.dob ? new Date(applicant.info.dob) : undefined,
        nationality: applicant.info?.nationality
          ? countriesConfig.THREE_TO_TWO_COUNTRY_CODE_MAPPING[
              applicant.info.nationality as countriesConfig.Alpha3CountryCodesType
            ]
          : undefined,
        idDocs: applicant.info?.idDocs
      }
    };
  }
}

export class DefaultSumsubBasedKycService extends SumsubBasedKycService {
  private static _instance: DefaultSumsubBasedKycService;

  public static get Instance(): DefaultSumsubBasedKycService {
    return this._instance || (this._instance = new this());
  }
}

export class ExtendedVerificationSumsubBasedKycService extends SumsubBasedKycService {
  private static _instance: ExtendedVerificationSumsubBasedKycService;

  public static get Instance(): ExtendedVerificationSumsubBasedKycService {
    return this._instance || (this._instance = new this());
  }

  public async createKycWorkflow(userId: string): Promise<WorkflowType> {
    const workflow = await this._sumsubClient.generateAccessToken(userId, SumsubLevelEnum.BASIC_KYC_EU);

    return {
      sdkToken: workflow.token
    };
  }
}
