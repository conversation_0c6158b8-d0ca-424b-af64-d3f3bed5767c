import {
  DailySummarySnapshot,
  DailySummarySnapshotDocument,
  IndividualSentimentScoreComponentEnum,
  TotalSentimentScoreComponentEnum
} from "../models/DailySummarySnapshot";
import { DailySummarySnapshotFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import { QueryOptions } from "mongoose";
import { PortfolioDocument, PortfolioPopulationFieldsEnum } from "../models/Portfolio";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import Decimal from "decimal.js";
import { PartialRecord } from "utils";
import { RedisClientService } from "../loaders/redis";
import PortfolioUtil from "../utils/portfolioUtil";
import { UserDocument } from "../models/User";
import SavingsProductService from "./savingsProductService";
import { TransactionService } from "./transactionService";
import RewardService from "./rewardService";
import { TenorEnum } from "../configs/durationConfig";
import PortfolioService from "./portfolioService";
import DateUtil from "../utils/dateUtil";
import { InvestmentProductsDictType } from "investmentProducts";

/**
 * ENUMS
 */
const BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS: Record<IndividualSentimentScoreComponentEnum, number> = {
  [IndividualSentimentScoreComponentEnum.NEWS]: 0.3,
  [IndividualSentimentScoreComponentEnum.ANALYST]: 0.2,
  [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.5
};

export type AssetSentimentScoresType = PartialRecord<IndividualSentimentScoreComponentEnum, number>;

export type SentimentScoreType = PartialRecord<IndividualSentimentScoreComponentEnum, number> &
  Record<TotalSentimentScoreComponentEnum, number>;

export default class DailySummarySnapshotService {
  /**
   * ===============
   * PUBLIC METHODS
   * ===============
   */

  /**
   * @description Retrieves all the snapshots for a given owner and date range, optionally sorted by timestamp.
   * @param filter
   * @param sort
   */
  public static async getDailySummarySnapshots(
    filter: DailySummarySnapshotFilter = {},
    sort?: string
  ): Promise<DailySummarySnapshotDocument[]> {
    const dbFilter = DailySummarySnapshotService._createDailySummarySnapshotDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    return DailySummarySnapshot.find(dbFilter, null, options);
  }

  /**
   * @description Checks if a user has any daily summary snapshots
   * @param user The user document to check
   * @returns A boolean indicating whether the user has any snapshots
   */
  public static async hasUserSnapshots(user: UserDocument): Promise<boolean> {
    return !!(await DailySummarySnapshot.exists({
      "metadata.owner": user.id
    }));
  }

  /**
   * This method is used from two places:
   * 1. In a cron task that calculates and stores portfolio sentiment scores each evening.
   * 2. In today's daily summary view, where we calculate the real-time sentiment score for the portfolio.
   *
   * The method has been optimised to only do one mGet to Redis to retrieve all asset sentiment scores and then use
   * the fetched values to calculate the portfolio sentiment score.
   *
   * @param portfolio
   */
  public static async calculatePortfolioSentimentScore(portfolio: PortfolioDocument): Promise<SentimentScoreType> {
    if (portfolio.holdings?.length > 0) {
      await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
      const user = portfolio.owner as UserDocument;

      const assetSentimentScores =
        await DailySummarySnapshotService._calculatePortfolioAssetSentimentScores(portfolio);

      if (!assetSentimentScores || assetSentimentScores.length === 0) {
        return null;
      }

      const individualPortfolioSentimentScores: Record<IndividualSentimentScoreComponentEnum, number> = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: null,
        [IndividualSentimentScoreComponentEnum.NEWS]: null,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: null
      };

      // For each individual sentiment score for the portfolio, we calculate it based on the relevant asset sentiment scores and their percentage allocation in the portfolio.
      Object.keys(individualPortfolioSentimentScores).forEach((key) => {
        const relevantScores = assetSentimentScores.filter(
          (assetScore) => !!assetScore?.score?.[key as IndividualSentimentScoreComponentEnum]
        );

        if (relevantScores.length > 0) {
          const totalAllocation = relevantScores.reduce(
            (sum, { allocation }) => sum.add(allocation),
            new Decimal(0)
          );

          individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum] = relevantScores
            .reduce((sum, { allocation, score }) => {
              // Normalize the allocation relative to total allocation of assets with scores
              const normalizedAllocation = Decimal.div(allocation, totalAllocation);
              return Decimal.mul(normalizedAllocation, score[key as IndividualSentimentScoreComponentEnum]).add(
                sum
              );
            }, new Decimal(0))
            .toNumber();
        }
      });

      const totalScore = DailySummarySnapshotService._calculatePortfolioTotalSentimentScore(
        individualPortfolioSentimentScores,
        user.isPriceMomentumSentimentEnabled
      );

      return Object.fromEntries(
        Object.entries({
          [TotalSentimentScoreComponentEnum.TOTAL]: totalScore,
          [IndividualSentimentScoreComponentEnum.ANALYST]:
            individualPortfolioSentimentScores[IndividualSentimentScoreComponentEnum.ANALYST],
          [IndividualSentimentScoreComponentEnum.NEWS]:
            individualPortfolioSentimentScores[IndividualSentimentScoreComponentEnum.NEWS],
          [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: user.isPriceMomentumSentimentEnabled
            ? individualPortfolioSentimentScores[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
            : null
        }).filter(([, value]) => value !== null)
      ) as SentimentScoreType;
    }
  }

  public static async createDailySummarySnapshotForUsers(
    users: UserDocument[],
    investmentProductsDict: InvestmentProductsDictType,
    date: Date = new Date(Date.now())
  ): Promise<void> {
    const { start, end } = DateUtil.getStartAndEndOfDay(date);

    const dbOperations = await Promise.all(
      users.map(async (user) => {
        const existingEntry = await DailySummarySnapshot.findOne({
          "metadata.owner": user.id,
          date: { $gt: start, $lt: end }
        });
        if (existingEntry) {
          return null;
        }

        // We populate holdings within the portfolio with the pre-fetched investment products. This way,
        // we only fetch investment products with their tickers once instead for every portfolio.
        const portfolio = PortfolioUtil.populatePortfolioWithInvestmentProducts(
          user.portfolios[0] as PortfolioDocument,
          investmentProductsDict
        );

        const [userSavings, sentimentScore, dividendsForReturns, rewardsForReturns] = await Promise.all([
          SavingsProductService.getUserSavings(user.id, date),
          DailySummarySnapshotService.calculatePortfolioSentimentScore(portfolio),
          TransactionService.getDividendTransactionsForReturnsUpBy(user.id),
          RewardService.getSettledRewards(user.id)
        ]);

        const transactions = { dividendTransactions: dividendsForReturns, rewards: rewardsForReturns };

        // We only calculate MWRR and up-by values if user has holdings.
        let mwrr, upBy: Record<TenorEnum.TODAY, number>;
        if (portfolio.holdings?.length) {
          [mwrr, upBy] = await Promise.all([
            PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY, date),
            PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY, date)
          ]);
        }

        // We store all values in whole currency.
        const cashValue = portfolio.cash[user.currency].available;
        const savingsValue = userSavings?.[0].savingsAmount ?? 0;
        const holdingsValue = portfolio.currentTicker?.getPrice(user.currency) ?? 0;
        const totalValue = new Decimal(cashValue).add(savingsValue).add(holdingsValue).toNumber();
        const assetAllocation = PortfolioUtil.mapHoldingsToAllocationFormat(
          user.currency,
          portfolio.holdings
        ).assets;

        return {
          insertOne: {
            document: {
              metadata: { owner: user.id },
              date,
              ...(sentimentScore && { sentimentScore }), // To avoid sentiment score being persisted when it is null.
              portfolio: {
                cash: { value: { currency: user.currency, amount: cashValue } },
                savings: {
                  value: {
                    currency: user.currency,
                    amount: savingsValue
                  },
                  unrealisedInterest: {
                    amount: userSavings?.[0].unrealisedInterest,
                    currency: user.currency
                  }
                },
                total: { value: { currency: user.currency, amount: totalValue } },
                holdings: {
                  value: { currency: user.currency, amount: holdingsValue },
                  assets: portfolio.holdings.map((holding) => {
                    return {
                      assetId: holding.assetCommonId,
                      latestPrice: {
                        currency: user.currency,
                        amount: holding.asset.currentTicker.getPrice(user.currency)
                      },
                      quantity: holding.quantity,
                      holdingWeightPercentage: Decimal.div(assetAllocation[holding.assetCommonId], 100).toNumber(),
                      dailyReturnPercentage: DateUtil.datesAreEqual(holding.asset.currentTicker.timestamp, date)
                        ? holding.asset.currentTicker.dailyReturnPercentage
                        : 0
                    };
                  }),
                  dailyReturnPercentage: mwrr?.[TenorEnum.TODAY],
                  dailyUpBy: upBy?.[TenorEnum.TODAY]
                }
              }
            }
          }
        };
      })
    );

    await DailySummarySnapshot.bulkWrite(dbOperations.filter((operation) => !!operation));
  }

  /**
   * ===============
   * PRIVATE METHODS
   * ===============
   */

  private static _createDailySummarySnapshotDbFilter(filter: DailySummarySnapshotFilter) {
    const dbFilter = {
      "metadata.owner": filter.owner,
      date: null as any
    };

    if (filter.date) {
      dbFilter["date"] = {
        $gte: filter.date.startDate,
        $lt: filter.date.endDate
      };
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }

  /**
   * Calculates the sentiment score for a given asset given its news, analyst views and price momentum.
   * @param asset
   * @param sentimentScores pre-fetched sentiment scores
   * @param isPriceMomentumSentimentEnabled
   * @private
   */
  private static _calculateAssetSentimentScore(
    asset: InvestmentProductDocument,
    sentimentScores: AssetSentimentScoresType,
    isPriceMomentumSentimentEnabled: boolean
  ): SentimentScoreType {
    if (asset.isETF) {
      if (!isPriceMomentumSentimentEnabled) {
        return null; // ETFs have no sentiment score when price momentum is disabled
      }

      return {
        [TotalSentimentScoreComponentEnum.TOTAL]:
          sentimentScores[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM],
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
          sentimentScores[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
      };
    }

    const scoresForCalculation = Object.fromEntries(
      Object.entries(sentimentScores).filter(
        ([key]) => key !== IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM || isPriceMomentumSentimentEnabled
      )
    ) as AssetSentimentScoresType;

    const totalScore = DailySummarySnapshotService._getWeightedAverageTotalSentimentScore(scoresForCalculation);

    return {
      [TotalSentimentScoreComponentEnum.TOTAL]: totalScore,
      ...scoresForCalculation
    };
  }

  /**
   * This method calculates the total score based on the individual sentiment scores.
   *
   * Although the base weight for each sentiment score is fixed (30%/20%/50%), if any of the three component scores is
   * missing, this method adjusts the rest of the scores to still sum to 1.
   *
   * @param inputs
   * @private
   */
  private static _getWeightedAverageTotalSentimentScore(
    inputs: PartialRecord<IndividualSentimentScoreComponentEnum, number>
  ): number {
    const availableInputs = Object.entries(inputs).filter(([, value]) => !!value) as [
      keyof IndividualSentimentScoreComponentEnum,
      number
    ][];

    if (availableInputs.length === 0) {
      throw new Error("Cannot calculate sentiment score when no individual sentiment scores are calculated.");
    }

    const totalWeight = availableInputs.reduce(
      (sum, [key]) =>
        Decimal.add(sum, BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS[key as IndividualSentimentScoreComponentEnum]),
      new Decimal(0)
    );

    return availableInputs
      .reduce((sum, [key, value]) => {
        const adjustedWeight = Decimal.div(
          BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS[key as IndividualSentimentScoreComponentEnum],
          totalWeight
        );
        return Decimal.mul(value, adjustedWeight).add(sum);
      }, new Decimal(0))
      .toNumber();
  }

  /**
   * Calculates, for each asset in the portfolio, its sentiment score and the percentage allocation of it in the portfolio.
   * @param portfolio
   * @private
   */
  private static async _calculatePortfolioAssetSentimentScores(portfolio: PortfolioDocument): Promise<
    {
      score: PartialRecord<IndividualSentimentScoreComponentEnum, number> &
        Record<TotalSentimentScoreComponentEnum, number>;
      allocation: number;
    }[]
  > {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    const assetAllocation = PortfolioUtil.mapHoldingsToAllocationFormat(
      portfolio.currency,
      portfolio.holdings
    ).assets;

    const redisKeys = portfolio.holdings.flatMap((holding) => {
      const commonId = holding.asset.commonId;
      if (holding.asset.isETF) {
        return [`sentiment_scores:${commonId}:price_momentum`];
      }
      return [
        `sentiment_scores:${commonId}:news`,
        `sentiment_scores:${commonId}:analyst`,
        `sentiment_scores:${commonId}:price_momentum`
      ];
    });

    const allSentimentScores = await RedisClientService.Instance.mGet<number>(redisKeys);

    return portfolio.holdings
      .map((holding) => {
        const commonId = holding.asset.commonId;

        let assetSentimentScore: SentimentScoreType;
        if (holding.asset.isETF) {
          const priceMomentumIndex = redisKeys.indexOf(`sentiment_scores:${commonId}:price_momentum`);

          assetSentimentScore = DailySummarySnapshotService._calculateAssetSentimentScore(
            holding.asset,
            {
              [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: allSentimentScores[priceMomentumIndex]
            },
            user.isPriceMomentumSentimentEnabled
          );
        } else {
          const newsIndex = redisKeys.indexOf(`sentiment_scores:${commonId}:news`);
          const analystIndex = redisKeys.indexOf(`sentiment_scores:${commonId}:analyst`);
          const priceMomentumIndex = redisKeys.indexOf(`sentiment_scores:${commonId}:price_momentum`);

          assetSentimentScore = DailySummarySnapshotService._calculateAssetSentimentScore(
            holding.asset,
            {
              [IndividualSentimentScoreComponentEnum.NEWS]: allSentimentScores[newsIndex],
              [IndividualSentimentScoreComponentEnum.ANALYST]: allSentimentScores[analystIndex],
              [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: allSentimentScores[priceMomentumIndex]
            },
            user.isPriceMomentumSentimentEnabled
          );
        }

        return {
          allocation: Decimal.div(assetAllocation[holding.assetCommonId], 100).toNumber(),
          score: assetSentimentScore
        };
      })
      .filter(({ score }) => !!score);
  }

  private static _calculatePortfolioTotalSentimentScore(
    individualPortfolioSentimentScores: Record<IndividualSentimentScoreComponentEnum, number>,
    isPriceMomentumSentimentEnabled: boolean
  ) {
    const totalWeight = Object.entries(BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS)
      .filter(
        ([key]) =>
          !!individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum] &&
          (key !== IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM || isPriceMomentumSentimentEnabled)
      )
      .reduce((sum, [, weight]) => Decimal.add(sum, weight), new Decimal(0));

    return Object.entries(BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS)
      .filter(
        ([key]) =>
          !!individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum] &&
          (key !== IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM || isPriceMomentumSentimentEnabled)
      )
      .reduce((sum, [key, weight]) => {
        const adjustedWeight = Decimal.div(weight, totalWeight);
        return Decimal.mul(
          individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum],
          adjustedWeight
        ).add(sum);
      }, new Decimal(0))
      .toNumber();
  }
}
