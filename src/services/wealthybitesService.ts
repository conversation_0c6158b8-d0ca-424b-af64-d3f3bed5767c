import { captureException } from "@sentry/node";
import { Message } from "postmark";
import { render } from "@react-email/components";
import React from "react";
import UserService from "./userService";
import logger from "../external-services/loggerService";
import { RedisClientService } from "../loaders/redis";
import SundownDigestService from "./sundownDigestService";
import MarketRoundupCreator from "../lib/marketRoundupCreator";
import DateUtil from "../utils/dateUtil";
import TopNewsSelector from "../lib/topNewsSelector";
import WealthyhubService, { AnalystInsightType } from "./wealthyhubService";
import PortfolioService from "./portfolioService";
import PortfolioUtil from "../utils/portfolioUtil";
import { TenorEnum } from "../configs/durationConfig";
import { PortfolioDocument } from "../models/Portfolio";
import ContentEntryRepository from "../repositories/contentEntryRepository";
import { FMPService } from "../external-services/fmpService";
import Decimal from "decimal.js";
import { indexesConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import * as CacheUtil from "../utils/cacheUtil";
import SavingsProductService from "./savingsProductService";
import {
  WealthybitesEmailBody,
  LearnInsight,
  InvestmentSnapshotData,
  TopPortfolioMoverItem,
  LearningGuideData
} from "types/wealthybites";
import DailyTickerRepository from "../repositories/dailyTickerRepository";
import MailerService from "../external-services/mailerService";
import { WealthybitesEmail } from "../emails/templates/wealthybites/index";
import { User, UserDocument } from "../models/User";
import { EmailNotificationSettingEnum, NotificationSettingsDocument } from "../models/NotificationSettings";
import ConfigUtil from "../utils/configUtil";

const { INDEX_CONFIG_GLOBAL } = indexesConfig;
const { PUBLIC_ASSET_CONFIG } = publicInvestmentUniverseConfig;

const EMAIL_BATCH_SIZE = 100;
const SNAPSHOT_PRECALCULATION_BATCH_SIZE = 100;
const INVESTMENT_SNAPSHOT_CACHE_EXPIRATION_SECONDS = 60 * 60 * 24; // 24 hours
const WEALTHYBITES_EMAIL_SUBJECT = "Weekly Roundup: Top news to begin your week";
const WEALTHYBITES_EMAIL_FROM = "<NAME_EMAIL>";

export default class WealthybitesService {
  /**
   * ===============
   * PUBLIC METHODS
   * ===============
   */

  /**
   * @description Processes all users in batches and builds their Wealthybites email bodies.
   * Common data is fetched before processing users.
   */
  public static async processAllWealthybitesEmails(): Promise<void> {
    let totalEmailsSent = 0;

    try {
      // Fetch common data once before processing users
      const [marketRoundup, topMarketIndexes, topNews, analystInsights, learningGuide] = await Promise.all([
        WealthybitesService._assembleMarketRoundup(),
        WealthybitesService._assemblePopularIndexesReturns(),
        WealthybitesService._assembleTopNews(),
        WealthybitesService._assembleAnalystInsights(),
        WealthybitesService._selectLearningGuide()
      ]);

      await UserService.getUsersStreamed({}, [
        {
          path: "portfolios",
          populate: { path: "currentTicker" }
        },
        {
          path: "notificationSettings"
        }
      ]).eachAsync(
        async (users: UserDocument[]) => {
          const activeEligibleUsers = users.filter(WealthybitesService._isUserEligibleForWealthybitesEmail);

          const htmlEmailBatch: Message[] = await Promise.all(
            activeEligibleUsers
              .map(async (user: UserDocument): Promise<Message> => {
                try {
                  const portfolioStatus = await UserService.getPortfolioConversionStatus(user);
                  const isInvested = portfolioStatus === "completed";
                  const userLocale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
                  const userCurrency = user.currency!;

                  const emailDataPayload: WealthybitesEmailBody = {
                    firstName: user.firstName,
                    userLocale,
                    userCurrency,
                    userEmail: user.email,
                    marketRoundup,
                    topMarketIndexes,
                    topNews,
                    analystInsights,
                    learningGuide
                  };

                  if (isInvested) {
                    const [investmentSnapshot, topPortfolioMovers] = await Promise.all([
                      WealthybitesService._getInvestmentSnapshot(user._id),
                      WealthybitesService._getTopPortfolioMovers(user._id)
                    ]);
                    emailDataPayload.investmentSnapshot = investmentSnapshot;
                    emailDataPayload.topPortfolioMovers = topPortfolioMovers;
                  }

                  const [emailHtml, emailText] = await Promise.all([
                    render(React.createElement(WealthybitesEmail, emailDataPayload)),
                    render(React.createElement(WealthybitesEmail, emailDataPayload), {
                      plainText: true
                    })
                  ]);

                  return {
                    From: WEALTHYBITES_EMAIL_FROM,
                    To: user.email,
                    Subject: WEALTHYBITES_EMAIL_SUBJECT,
                    HtmlBody: emailHtml,
                    TextBody: emailText,
                    MessageStream: "outbound"
                  } as Message;
                } catch (error) {
                  captureException(error);
                  logger.error("Failed to build Wealthybites email for user in batch", {
                    module: "WealthybitesService",
                    method: "processAllWealthybitesEmails",
                    userEmail: user?.email,
                    data: { userId: user?._id, error }
                  });
                  return null;
                }
              })
              // we get nulls in case of error
              .filter((email) => email !== null)
          );

          try {
            await MailerService.batchEmails(htmlEmailBatch);
            totalEmailsSent += htmlEmailBatch.length;
            logger.info(`Successfully sent batch of ${htmlEmailBatch.length} Wealthybites emails.`, {
              module: "WealthybitesService",
              method: "processAllWealthybitesEmails",
              data: { count: htmlEmailBatch.length }
            });
          } catch (batchError) {
            logger.error("Failed to send batch of Wealthybites emails", {
              module: "WealthybitesService",
              method: "processAllWealthybitesEmails",
              data: { count: htmlEmailBatch.length, error: batchError }
            });
          }
        },
        { batchSize: EMAIL_BATCH_SIZE }
      );

      logger.info(`🎉 Wealthybites email processing completed. Total emails sent: ${totalEmailsSent}`, {
        module: "WealthybitesService",
        method: "processAllWealthybitesEmails",
        data: { totalEmailsSent }
      });
    } catch (error) {
      captureException(error);
      logger.error("Failed to process and send Wealthybites emails", {
        module: "WealthybitesService",
        method: "processAllWealthybitesEmails",
        data: { error }
      });
      throw error;
    }
  }

  public static async precalculateSnapshotsForAllUsers(): Promise<void> {
    await UserService.getUsersStreamed({ portfolioConversionStatus: "completed" }, [
      {
        path: "portfolios",
        populate: { path: "currentTicker" }
      },
      {
        path: "notificationSettings"
      }
    ]).eachAsync(
      async (users: UserDocument[]) => {
        const activeEligibleUsers = users.filter(WealthybitesService._isUserEligibleForWealthybitesEmail);

        for (const user of activeEligibleUsers) {
          try {
            const portfolio = user.portfolios[0];

            const [investmentSnapshot, topPortfolioMovers] = await Promise.all([
              WealthybitesService._calculateInvestmentSnapshot(user.id, portfolio),
              WealthybitesService._calculateTopPortfolioMovers(portfolio)
            ]);

            if (investmentSnapshot) {
              await RedisClientService.Instance.set(`wealthybites:snapshot:${user.id}`, investmentSnapshot, {
                ex: INVESTMENT_SNAPSHOT_CACHE_EXPIRATION_SECONDS
              });
            }
            if (topPortfolioMovers) {
              await RedisClientService.Instance.set(`wealthybites:movers:${user.id}`, topPortfolioMovers, {
                ex: INVESTMENT_SNAPSHOT_CACHE_EXPIRATION_SECONDS
              });
            }
          } catch (error) {
            captureException(error);
            logger.error(`Failed to precalculate snapshots for user: ${user.id}`, {
              module: "WealthybitesService",
              method: "precalculateSnapshotsForAllUsers",
              data: { userId: user.id, error }
            });
          }
        }
      },
      { batchSize: SNAPSHOT_PRECALCULATION_BATCH_SIZE }
    );
  }

  /**
   * ===============
   * PRIVATE METHODS
   * ===============
   */

  /**
   * @description Checks if a user is eligible to receive the Wealthybites email.
   * A user is eligible if they are not deleted and have enabled Wealthybites email notifications.
   * @param user - The UserDocument to check.
   * @returns True if the user is eligible, false otherwise.
   */
  private static _isUserEligibleForWealthybitesEmail(user: UserDocument): boolean {
    if (user.isDeleted) {
      return false;
    }

    const notificationSettings = user.notificationSettings as NotificationSettingsDocument;
    const hasEnabledWealthybites = notificationSettings.email.settings.get(
      EmailNotificationSettingEnum.WEALTHYBITES
    );

    return hasEnabledWealthybites === true;
  }

  private static async _calculateInvestmentSnapshot(
    userId: string,
    portfolio: PortfolioDocument
  ): Promise<InvestmentSnapshotData> {
    const [portfolioWithReturns, userSavings, latestSavingsPortfolioTicker] = await Promise.all([
      PortfolioService.getPortfolioWithReturnsByTenor(portfolio),
      SavingsProductService.getUserSavings(userId),
      DailyTickerRepository.getLatestSavingsPortfolioTicker(portfolio.id)
    ]);

    // Extract required data
    const investmentsValue = portfolio.currentTicker?.getPrice(portfolio.currency) ?? 0;
    const weeklyReturns = portfolioWithReturns.returnsValues?.[TenorEnum.ONE_WEEK] ?? 0;
    const upByValue = portfolioWithReturns.upByValues?.[TenorEnum.ONE_WEEK] ?? 0;
    const cashValue = portfolio.cash[portfolio.currency]?.available ?? 0;
    const savingsValue = userSavings?.[0]?.savingsAmount ?? 0;
    const savingsDailyInterest = new Decimal(latestSavingsPortfolioTicker?.dailyAccrual ?? 0)
      .div(100)
      .toDecimalPlaces(2)
      .toNumber();
    const totalValue = new Decimal(investmentsValue).add(cashValue).add(savingsValue).toNumber();

    const investmentSnapshot: InvestmentSnapshotData = {
      investmentsValue,
      weeklyReturns,
      upByValue,
      cashValue,
      savingsValue,
      savingsDailyInterest,
      totalValue
    };

    return investmentSnapshot;
  }

  private static async _calculateTopPortfolioMovers(portfolio: PortfolioDocument): Promise<{
    winners: TopPortfolioMoverItem[];
    losers: TopPortfolioMoverItem[];
  }> {
    const holdings = portfolio.holdings;
    if (holdings.length === 0) {
      return { winners: [], losers: [] };
    }

    const assetIds = holdings.map((h) => h.assetCommonId);
    const weeklyReturnKeys = assetIds.map((id) => `asset:weeklyReturn:${id}`);
    const weeklyReturnsRaw = await RedisClientService.Instance.mGet<number>(weeklyReturnKeys);

    const portfolioMovers: TopPortfolioMoverItem[] = assetIds.map((assetId, i) => {
      const weeklyReturns = weeklyReturnsRaw[i] ?? 0;
      return { assetId, weeklyReturns };
    });

    // Sort by weekly returns descending
    const sorted = portfolioMovers
      .filter((m) => m.weeklyReturns !== 0)
      .sort((a, b) => b.weeklyReturns - a.weeklyReturns);
    return {
      winners: sorted.slice(0, 3).filter((winner) => winner.weeklyReturns > 0),
      losers: sorted.slice(-3).filter((loser) => loser.weeklyReturns < 0)
    };
  }

  /**
   * @description Retrieves the cached or recalculated investment snapshot for a user.
   * @param userId string
   * @returns Promise<InvestmentSnapshot | undefined>
   */
  private static async _getInvestmentSnapshot(userId: string): Promise<InvestmentSnapshotData> {
    return CacheUtil.getCachedDataWithFallback(
      `wealthybites:snapshot:${userId}`,
      async () => {
        const user = await User.findById(userId).populate([
          {
            path: "portfolios",
            populate: { path: "currentTicker" }
          }
        ]);
        return WealthybitesService._calculateInvestmentSnapshot(user.id, user.portfolios[0] as PortfolioDocument);
      },
      () => INVESTMENT_SNAPSHOT_CACHE_EXPIRATION_SECONDS
    );
  }

  /**
   * @description Retrieves the cached or recalculated top portfolio movers for a user.
   * @param userId string
   * @returns Promise<{ winners: TopPortfolioMoverItem[]; losers: TopPortfolioMoverItem[] }>
   */
  private static async _getTopPortfolioMovers(userId: string): Promise<{
    winners: TopPortfolioMoverItem[];
    losers: TopPortfolioMoverItem[];
  }> {
    return CacheUtil.getCachedDataWithFallback(
      `wealthybites:movers:${userId}`,
      async () => {
        const user = await UserService.getUser(userId, { portfolios: true });

        return WealthybitesService._calculateTopPortfolioMovers(user.portfolios[0] as PortfolioDocument);
      },
      () => INVESTMENT_SNAPSHOT_CACHE_EXPIRATION_SECONDS
    );
  }

  /**
   * @description Fetches and aggregates the weekly market roundup for the Wealthybites newsletter
   * using the summary of all market overviews for the last week.
   * @returns Promise<string>
   */
  private static async _assembleMarketRoundup(): Promise<string> {
    // Fetch all SundownDigest docs for the week
    const sundownDigests = await SundownDigestService.getSundownDigests({
      date: { startDate: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7), endDate: new Date(Date.now()) }
    });
    if (sundownDigests.length === 0) return "";

    // Extract all available formattedContent.overview fields
    const marketOverviews = sundownDigests.map((digest) => digest.formattedContent?.overview);

    return MarketRoundupCreator.aggregate(marketOverviews as string[]);
  }

  /**
   * @description Fetches and calculates returns for popular indexes for the week.
   */
  private static async _assemblePopularIndexesReturns(): Promise<{ name: string; returns: number }[]> {
    const indexIds = [
      "sp500",
      "dow_jones",
      "nasdaq",
      "europe_stoxx_600",
      "dax",
      "nikkei225",
      "hang_seng",
      "sensex"
    ] as Array<keyof typeof INDEX_CONFIG_GLOBAL>;
    const from = DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfDaysAgo(new Date(), 7));
    const to = DateUtil.getYearAndMonthAndDay(new Date());
    const data = await Promise.all(
      indexIds.map(async (id) => {
        const fmpSymbol = INDEX_CONFIG_GLOBAL[id].symbol;
        const historicalIndexData = await FMPService.Instance.getHistoricalIndexPrices(fmpSymbol, { from, to });
        const points = historicalIndexData.slice(-5);

        if (points.length < 2) return null;

        const first = points[0].close;
        const last = points[points.length - 1].close;
        const returns = PortfolioUtil.getReturns({
          startValue: new Decimal(first),
          endValue: new Decimal(last)
        })
          .toDecimalPlaces(4) // 4 digit precision for the decimal to be formatted as percentage
          .toNumber();
        const name = INDEX_CONFIG_GLOBAL[id].simpleName;
        return {
          name,
          returns
        };
      })
    );
    return data.filter((popularIndex) => popularIndex !== null);
  }

  /**
   * @description Aggregates and selects the top 5 news items with assetId from all SundownDigests for the week.
   * @returns Promise<TopNewsSection[]>
   */
  private static async _assembleTopNews(): Promise<
    {
      imageUrl: string;
      companyName: string;
      title: string;
      content: string;
    }[]
  > {
    // Fetch all SundownDigest docs for the week
    const sundownDigests = await SundownDigestService.getSundownDigests({
      date: { startDate: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7), endDate: new Date(Date.now()) }
    });
    if (sundownDigests.length === 0) return [];

    // Aggregate all news sections with assetId
    const allSections = sundownDigests
      .flatMap((digest) => digest.formattedContent?.sections || [])
      .filter((section) => !!section.assetId && !!section.companyName && !!section.title && !!section.content)
      .map((section) => ({
        companyName: section.companyName!,
        ticker: section.companyTicker,
        assetId: section.assetId!,
        title: section.title!,
        content: section.content!
      }));

    // Select the top 5 news items
    const selected = await TopNewsSelector.select(allSections);

    // Map to new structure with imageUrl, companyName, title, summary
    return selected.map((section) => ({
      imageUrl: `https://mobile-config-edge-server.wealthyhood.workers.dev/logos/${section.assetId}.png`,
      companyName: `${section.companyName} (${PUBLIC_ASSET_CONFIG[section.assetId as publicInvestmentUniverseConfig.PublicAssetType].formalTicker})`,
      title: section.title,
      content: section.content
    }));
  }

  /**
   * @description Fetches and aggregates analyst insights and quick-takes for the week.
   * @returns Promise<{ insights: LearnInsight[], quickTakes: LearnInsight[] }>
   */
  private static async _assembleAnalystInsights(): Promise<{
    insights: LearnInsight[];
    quickTakes: LearnInsight[];
  }> {
    // Fetch all analyst insights (paginated, but we want all from the last week)
    // WealthyhubService.getAnalystInsights returns paginated results, so we fetch page 1 with a large enough page size
    const { data } = await WealthyhubService.getAnalystInsights({ page: 1 }, { isPaidPlan: true });

    // Filter the ones within the last week
    const recent = data
      .filter((item) => new Date(item.createdAt) >= DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7))
      .sort(
        (insightA, insightB) => new Date(insightA.createdAt).getTime() - new Date(insightB.createdAt).getTime()
      );

    const mapToLearnInsight = (item: AnalystInsightType): LearnInsight => ({
      emoji: WealthybitesService._getRandomEmojiForInsight(),
      title: item.title,
      url: WealthybitesService._getInsightUrl(item.id)
    });

    const insights = recent.filter((item) => item.analystInsightType === "ANALYSIS").map(mapToLearnInsight);
    const quickTakes = recent.filter((item) => item.analystInsightType === "QUICK_TAKE").map(mapToLearnInsight);
    return { insights, quickTakes };
  }

  /**
   * @description Selects a random learning guide and returns its title, link (slug), and chapters.
   * @returns Promise<LearningGuideData>
   */
  private static async _selectLearningGuide(): Promise<LearningGuideData> {
    const guides = await WealthyhubService.getLearningGuides();
    if (!guides.length) throw new Error("No learning guides found");
    const selectedGuide = guides[Math.floor(Math.random() * guides.length)];

    const [dbGuide, guideWithChapters] = await Promise.all([
      ContentEntryRepository.getByTitle(selectedGuide.title),
      WealthyhubService.getLearningGuideBySlug(selectedGuide.key, { isPaidPlan: false })
    ]);

    let url: string;
    if (dbGuide?.id) {
      url = `https://wealthyhood.onelink.me/TwZO/wealthyhubguides?documentId=${dbGuide.id}`;
    } else {
      url = "https://wealthyhood.onelink.me/TwZO/wealthyhubguides";
    }

    return {
      title: selectedGuide.title,
      url,
      chapters: guideWithChapters.chapters ? guideWithChapters.chapters.map((c: any) => c.title) : []
    };
  }

  private static _getInsightUrl(documentId: string): string {
    return `https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=${documentId}`;
  }

  private static _getRandomEmojiForInsight(): string {
    const emojis = [
      "🤓",
      "🥸",
      "🧐",
      "🤯",
      "🤔",
      "🫡",
      "🤫",
      "😯",
      "🤑",
      "🤝",
      "🙌🏽",
      "🧠",
      "♟️",
      "🏢",
      "🏦",
      "🏛️",
      "👉",
      "👀",
      "👔",
      "🎓",
      "🎩",
      "😎",
      "😯",
      "💼",
      "☕",
      "🎯",
      "💡",
      "💸",
      "💵",
      "💶",
      "💎",
      "⚖️",
      "💣",
      "🪄",
      "✉️",
      "📊",
      "📁",
      "📚",
      "🔎",
      "🔍",
      "🔝",
      "📣",
      "🔊",
      "📢",
      "💭"
    ];
    const idx = Math.floor(Math.random() * emojis.length);
    return emojis[idx];
  }
}
