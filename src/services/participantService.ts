import logger from "../external-services/loggerService";
import { InternalServerError } from "../models/ApiErrors";
import { ParticipantsFilter } from "filters";
import events from "../event-handlers/events";
import eventEmitter from "../loaders/eventEmitter";
import {
  GoogleAdsMetadataType,
  Participant,
  ParticipantDocument,
  ParticipantDTOInterface,
  ParticipantMetadataType,
  ParticipantRoleType,
  PlatformType,
  TrackingSourceType
} from "../models/Participant";
import { ReferralCode } from "../models/ReferralCode";
import { UserDocument } from "../models/User";
import ReferralCodeService from "./referralCodeService";
import DbUtil from "../utils/dbUtil";

const FINANCE_ADS_WLTHD_ID = "rz81ojjz";
const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page

export default class ParticipantService {
  /**
   * PUBLIC METHODS
   */
  public static async createParticipant(
    participantInfo: {
      appInstallInfo?: {
        createdAt: Date;
        platform: PlatformType;
      };
      email: string;
      anonymousId?: string;
      appsflyerId?: string;
      attributionErrorMsg?: string;
      googleAdsMetadata?: GoogleAdsMetadataType;
      gaClientId?: string;
      pageUserLanded?: string;
      influencerId?: string;
      referrerEmail?: string;
      referrerWlthdId?: string;
      referrerGrsfId?: string;
      participantRole?: ParticipantRoleType;
      trackingSource?: TrackingSourceType;
      submissionTechClickId?: string;
    },
    options?: { emitEmailSubmittedEvent?: boolean }
  ): Promise<ParticipantDocument> {
    logger.info(`Creating participant for email ${participantInfo.email}`, {
      module: "ParticipantService",
      method: "createParticipant",
      data: participantInfo
    });

    const {
      appInstallInfo,
      email,
      anonymousId,
      appsflyerId,
      attributionErrorMsg,
      googleAdsMetadata,
      gaClientId,
      pageUserLanded,
      influencerId,
      participantRole,
      referrerEmail,
      referrerWlthdId,
      referrerGrsfId,
      trackingSource,
      submissionTechClickId
    } = participantInfo;
    let existingParticipant = await Participant.findOne({ email });
    if (existingParticipant) {
      logger.warn(`Participant exists already for email ${email} - aborting`, {
        module: "ParticipantService",
        method: "createParticipant"
      });
      return existingParticipant;
    }

    if (!appsflyerId && !email) {
      throw new InternalServerError("Participant cannot be created without at least one of email or appsflyer id");
    }

    if (appsflyerId) {
      existingParticipant = await Participant.findOne({
        appsflyerId,
        email: { $exists: true, $ne: null }
      });

      if (existingParticipant) {
        logger.error(
          `Participant exists already for appsflyerId ${appsflyerId} and has email set already - aborting`,
          {
            module: "ParticipantService",
            method: "createParticipant",
            userEmail: email
          }
        );
        // If a user exists already for this appsflyer id and has email set we create another participant with different
        // email and same appsflyer id. This may not be ideal, but means that different users are using the same
        // device to create accounts (thus the same appsflyer id).
        if (email) {
          return Participant.findOneAndUpdate(
            { appsflyerId, email },
            // The body here is redundant because find will be empty for this email & appsflyer id and will therefore
            // create a new doc with those two fields.
            { appsflyerId, email },
            {
              runValidators: true,
              setDefaultsOnInsert: true,
              upsert: true,
              new: true
            }
          );
        } else {
          return existingParticipant;
        }
      }
    }

    const metadata = ParticipantService._getMetadataObject(
      {},
      {
        googleAdsMetadata,
        influencerId,
        submissionTechClickId
      }
    );

    // filter out any undefined values to allow defaults to be set
    const participantData = Object.fromEntries(
      Object.entries({
        appInstallInfo,
        email,
        anonymousId,
        appsflyerId,
        attributionErrorMsg,
        gaClientId,
        pageUserLanded,
        participantRole: participantRole || "BASIC",
        metadata,
        trackingSource
      } as ParticipantDTOInterface).filter((entry) => entry[1])
    ) as ParticipantDTOInterface;

    // At this point we know that no participant exists for the given email
    // so we can safely query by appsflyer id if it's defined.
    const participantQueryObj = appsflyerId ? { appsflyerId } : { email };

    await Participant.findOneAndUpdate(participantQueryObj, participantData, {
      runValidators: true,
      setDefaultsOnInsert: true,
      upsert: true,
      new: true
    });

    // referrer has to be set after we create the participant doc
    const newParticipant = await ParticipantService._setReferrer(participantQueryObj, {
      influencerId,
      referrerEmail,
      referrerWlthdId,
      referrerGrsfId
    });

    if (options?.emitEmailSubmittedEvent) {
      eventEmitter.emit(events.participant.emailSubmitted.eventId, newParticipant);
    }

    return newParticipant;
  }

  public static async getParticipants(filters: ParticipantsFilter = {}): Promise<ParticipantDocument[]> {
    const dbFilters = ParticipantService._createDbFilters(filters);
    return Participant.find(dbFilters).populate("referrer");
  }

  public static async getParticipantByAppsflyerId(
    appsflyerId: string,
    populate: {
      owner?: boolean;
      referrer?: boolean;
    }
  ): Promise<ParticipantDocument> {
    if (!appsflyerId) {
      throw new InternalServerError("Appsflyer id is empty");
    }
    const populateString = DbUtil.getPopulationString(populate);
    return Participant.findOne({ appsflyerId }).populate(populateString);
  }

  public static async getParticipantByEmail(email: string): Promise<ParticipantDocument> {
    if (!email) {
      throw new InternalServerError("Email is empty");
    }
    return Participant.findOne({ email }).populate("referrer");
  }

  public static async updateParticipant(
    participant: ParticipantDocument,
    {
      appsflyerId,
      attributionErrorMsg,
      email,
      appInstallInfo,
      googleAdsMetadata,
      gaClientId,
      trackingSource,
      referrer,
      submissionTechClickId
    }: {
      appsflyerId?: string;
      email?: string;
      attributionErrorMsg?: string;
      appInstallInfo?: {
        createdAt: Date;
        platform: PlatformType;
      };
      googleAdsMetadata?: GoogleAdsMetadataType;
      gaClientId?: string;
      trackingSource?: TrackingSourceType;
      referrer?: {
        influencerId?: string;
        referrerEmail?: string;
        referrerWlthdId?: string;
        referrerGrsfId?: string;
      };
      submissionTechClickId?: string;
    }
  ): Promise<ParticipantDocument> {
    const participantData: ParticipantDTOInterface = { appsflyerId, email, appInstallInfo, attributionErrorMsg };

    if (!participant.gaClientId && gaClientId) {
      participantData.gaClientId = gaClientId;
    }
    if (!participant.trackingSource && trackingSource) {
      participantData.trackingSource = trackingSource;
    }
    participantData.metadata = ParticipantService._getMetadataObject(participant.metadata, {
      googleAdsMetadata,
      influencerId: referrer?.influencerId,
      submissionTechClickId: submissionTechClickId
    });
    if (participantData.metadata?.googleAds?.gclid) {
      participantData.trackingSource = "google";
    }

    const updatedParticipant = await Participant.findByIdAndUpdate(participant.id, participantData, {
      new: true,
      upsert: false
    }).populate("referrer");

    if (!updatedParticipant.referrer && referrer) {
      const queryObj = participant.email ? { email: participant.email } : { appsflyerId: participant.appsflyerId };
      // NOTE: this method should be called with referrer data only if the
      // user hasn't signed up yet.
      return await ParticipantService._setReferrer(queryObj, referrer);
    } else {
      return updatedParticipant;
    }
  }

  public static async deleteParticipant(participantId: string): Promise<ParticipantDocument> {
    return Participant.findByIdAndDelete(participantId);
  }

  /**
   * PRIVATE METHODS
   */
  private static _getMetadataObject(
    initialMetadataObj: ParticipantMetadataType,
    {
      googleAdsMetadata,
      influencerId,
      submissionTechClickId
    }: { googleAdsMetadata?: GoogleAdsMetadataType; influencerId?: string; submissionTechClickId?: string }
  ): ParticipantMetadataType {
    const metadata: ParticipantMetadataType = initialMetadataObj;
    if (!metadata?.financeAds?.influencerId && influencerId) {
      metadata.financeAds = { influencerId };
    }
    if (!metadata?.googleAds && googleAdsMetadata) {
      metadata.googleAds = googleAdsMetadata;
    }
    if (!metadata?.submissionTech && submissionTechClickId) {
      metadata.submissionTech = { clickId: submissionTechClickId };
    }

    return metadata;
  }

  private static _createDbFilters(filters: ParticipantsFilter): any {
    const dbFilters: {
      email?: string;
      grsfId?: string;
      participantRole?: ParticipantRoleType;
    } = {
      email: filters.email,
      grsfId: filters.grsfId,
      participantRole: filters.participantRole
    };

    return Object.fromEntries(Object.entries(dbFilters).filter(([, value]) => value != null));
  }

  private static async _setReferrer(
    queryObj: { email: string } | { appsflyerId: string },
    {
      influencerId,
      referrerEmail,
      referrerWlthdId,
      referrerGrsfId
    }: {
      influencerId?: string;
      referrerEmail?: string;
      referrerWlthdId?: string;
      referrerGrsfId?: string;
    }
  ): Promise<ParticipantDocument> {
    if (!(queryObj as { email: string }).email && !(queryObj as { appsflyerId: string }).appsflyerId) {
      throw new InternalServerError("Participant email & appsflyer id cannot be empty at the same time");
    }

    // Prioritise referrals over Finance Ads affiliates
    let referrer: ParticipantDocument;
    if (referrerEmail) {
      referrer = await Participant.findOne({ email: referrerEmail });
    } else if (referrerWlthdId && (influencerId ? referrerWlthdId !== WH_FREE_ETF_WLTHD_ID : true)) {
      // if the wlthd id corresponds to our Wealthyhood owned if for the free
      // reward campaign we skip this check to give priority to the referral
      // by the finance ads.
      const referralCode = await ReferralCode.findOne({
        code: referrerWlthdId.toLowerCase().trim() // 'code' field will always be lowercase
      }).populate([
        {
          path: "owner",
          populate: [{ path: "participant" }]
        }
      ]);
      if (referralCode) {
        // For some legacy campaigns we may use a referral code that does not exist in referral code collection
        // so we need to check if the code exists and then check if it's active.
        if (referralCode.active) {
          referrer = (referralCode.owner as UserDocument).participant;
          if (referralCode.isExpiring) {
            await ReferralCodeService.replaceExpiringCode(referralCode);
          }
        }
      } else {
        /**
         * 'wlthdId' is a property that is generated with lower case alphabet
         *  so we have to ensuse that the given referrerWlthdId is lowercase
         */
        referrer = await Participant.findOne({ wlthdId: referrerWlthdId.toLowerCase().trim() });
      }
    } else if (referrerGrsfId) {
      referrer = await Participant.findOne({ grsfId: referrerGrsfId });
    } else if (influencerId) {
      referrer = await Participant.findOne({ wlthdId: FINANCE_ADS_WLTHD_ID });
    }

    return Participant.findOneAndUpdate(
      queryObj,
      { referrer: referrer?.id },
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: false,
        new: true
      }
    ).populate("referrer");
  }
}
