import Decimal from "decimal.js";
import { captureException } from "@sentry/node";
import {
  TransactionDocument,
  DepositCashTransactionDocument,
  WithdrawalCashTransactionDocument,
  DividendTransactionDocument,
  SavingsDividendTransactionDocument,
  TransactionPopulationFieldsEnum
} from "../models/Transaction";
import { DepositMethodEnum } from "../types/transactions";
import { OrderDocument, OrderPopulationFieldsEnum } from "../models/Order";
import { RewardDocument, RewardPopulationFieldsEnum } from "../models/Reward";
import { GiftDocument } from "../models/Gift";
import { AccountingRecordIndex } from "../models/AccountingRecordIndex";
import { InvoiceReferenceNumber } from "../models/InvoiceReferenceNumber";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../external-services/accountingLedgerStorageService";
import {
  LedgerAccounts,
  AccountingClientSegment,
  AccountingEventType,
  AccountingEntry
} from "../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../configs/accountingConfig";
import { UserDocument } from "../models/User";
import DbUtil from "../utils/dbUtil";
import logger from "../external-services/loggerService";
import { UserRepository } from "../repositories/userRepository";

type AccountingEntriesResult = {
  movements: AccountingEntry[];
  revenues?: AccountingEntry[];
  expenses?: AccountingEntry[];
};

type AccountingEntryGeneratorType = {
  entries: (...args: any[]) => AccountingEntriesResult | Promise<AccountingEntriesResult>;
  description: (
    userId: string,
    document: TransactionDocument | OrderDocument | RewardDocument | GiftDocument
  ) => string;
  articleDate: (document: TransactionDocument | OrderDocument | RewardDocument | GiftDocument) => string;
};

export class AccountingService {
  public static async generateAccountingEntriesOnTransactionUpdate(
    newTransaction: TransactionDocument,
    oldTransaction?: TransactionDocument | null
  ): Promise<void> {
    // Only process supported transaction categories
    const supportedUpdateCategories = ["DepositCashTransaction", "WithdrawalCashTransaction"];
    if (!supportedUpdateCategories.includes(newTransaction.category)) {
      return;
    }

    if (newTransaction.consideration.currency === "EUR") {
      logger.info(`Starting accounting entries generation for transaction ${newTransaction._id} update`, {
        module: "AccountingService",
        method: "generateAccountingEntriesOnTransactionUpdate",
        data: {
          newTransaction: newTransaction.toObject(),
          oldTransaction: oldTransaction?.toObject?.()
        }
      });
    }

    if (!newTransaction.populated("owner")) {
      await newTransaction.populate(TransactionPopulationFieldsEnum.OWNER);
    }
    const user = newTransaction.owner as UserDocument;

    await AccountingService._withAccountingContext(newTransaction, user, async ({ userId, clientSegment }) => {
      const generator = AccountingService._getEntryGenerator(newTransaction.category);
      const entries = await generator.entries(newTransaction, oldTransaction, clientSegment);
      const description = generator.description(userId, newTransaction);
      const articleDate = generator.articleDate(newTransaction);
      return { entries, description, articleDate };
    });
  }

  public static async generateAccountingEntriesOnTransactionInsert(
    transaction: TransactionDocument
  ): Promise<void> {
    // Only process supported transaction categories
    const supportedInsertCategories = ["DividendTransaction", "SavingsDividendTransaction"];
    if (!supportedInsertCategories.includes(transaction.category)) {
      return;
    }

    if (transaction.consideration.currency === "EUR") {
      logger.info(`Starting accounting entries generation for transaction ${transaction._id} insert`, {
        module: "AccountingService",
        method: "generateAccountingEntriesOnTransactionInsert",
        data: {
          transaction: transaction.toObject()
        }
      });
    }

    await transaction.populate(TransactionPopulationFieldsEnum.OWNER);
    const user = transaction.owner as UserDocument;

    await AccountingService._withAccountingContext(transaction, user, async ({ userId, clientSegment }) => {
      const generator = AccountingService._getEntryGenerator(transaction.category);
      const entries = await generator.entries(transaction, clientSegment);
      const description = generator.description(userId, transaction);
      const articleDate = entries.movements.length > 0 ? generator.articleDate(transaction) : "";
      return { entries, description, articleDate };
    });
  }

  public static async generateAccountingEntriesOnOrderUpdate(
    newOrder: OrderDocument,
    oldOrder?: OrderDocument | null
  ): Promise<void> {
    if (!newOrder.populated("transaction")) {
      await newOrder.populate(OrderPopulationFieldsEnum.TRANSACTION);
    }

    const transaction = newOrder.transaction as TransactionDocument;
    // Only process orders linked to AssetTransactions, RebalanceTransactions, SavingsTopupTransactions, and SavingsWithdrawalTransactions
    if (
      ![
        "AssetTransaction",
        "RebalanceTransaction",
        "SavingsTopupTransaction",
        "SavingsWithdrawalTransaction"
      ].includes(transaction.category)
    ) {
      return;
    }

    if (newOrder.consideration.currency === "EUR") {
      logger.info(`Starting accounting entries generation for order ${newOrder._id} update`, {
        module: "AccountingService",
        method: "generateAccountingEntriesOnOrderUpdate",
        data: {
          newOrder: newOrder.toObject(),
          oldOrder: oldOrder?.toObject?.()
        }
      });
    }

    const orderTransaction = newOrder.transaction as TransactionDocument;
    await orderTransaction.populate(TransactionPopulationFieldsEnum.OWNER);
    const user = orderTransaction.owner as UserDocument;

    await AccountingService._withAccountingContext(newOrder, user, async ({ userId, clientSegment }) => {
      const generator = AccountingService._getEntryGenerator("Order");
      const entries = await generator.entries(newOrder, oldOrder, clientSegment);
      const description = generator.description(userId, newOrder);
      const articleDate = entries.movements.length > 0 ? generator.articleDate(newOrder) : "";
      return { entries, description, articleDate };
    });
  }

  public static async generateAccountingEntriesOnRewardUpdate(
    newReward: RewardDocument,
    oldReward?: RewardDocument | null
  ): Promise<void> {
    if (newReward.consideration.currency === "EUR") {
      logger.info(`Starting accounting entries generation for reward ${newReward._id} update`, {
        module: "AccountingService",
        method: "generateAccountingEntriesOnRewardUpdate",
        data: {
          newReward: newReward.toObject(),
          oldReward: oldReward?.toObject?.()
        }
      });
    }

    if (!newReward.populated("targetUser")) {
      await newReward.populate(RewardPopulationFieldsEnum.TARGET_USER);
    }

    const user = newReward.targetUser as UserDocument;

    await AccountingService._withAccountingContext(newReward, user, async ({ userId, clientSegment }) => {
      const generator = AccountingService._getEntryGenerator("Reward");
      const entries = await generator.entries(newReward, oldReward, clientSegment);
      const description = generator.description(userId, newReward);
      const articleDate = generator.articleDate(newReward);
      return { entries, description, articleDate };
    });
  }

  public static async generateAccountingEntriesOnGiftUpdate(
    newGift: GiftDocument,
    oldGift?: GiftDocument | null
  ): Promise<void> {
    if (newGift.consideration.currency === "EUR") {
      logger.info(`Starting accounting entries generation for gift ${newGift._id} update`, {
        module: "AccountingService",
        method: "generateAccountingEntriesOnGiftUpdate",
        data: {
          newGift: newGift.toObject(),
          oldGift: oldGift?.toObject?.()
        }
      });
    }

    // Get the target user by email since gifts don't have a direct user reference
    const user = await UserRepository.getUserByEmail(newGift.targetUserEmail);

    await AccountingService._withAccountingContext(newGift, user, async ({ userId }) => {
      const generator = AccountingService._getEntryGenerator("Gift");
      const entries = await generator.entries(newGift, oldGift);
      const description = generator.description(userId, newGift);
      const articleDate = generator.articleDate(newGift);
      return { entries, description, articleDate };
    });
  }

  public static getAccountingActivityDescription(
    userId: string,
    transactionId: string,
    eventType: AccountingEventType,
    isin?: string
  ): string {
    return `${userId}|${transactionId}${isin ? `|${isin}` : ""}|${eventType}`;
  }

  public static shouldGenerateDepositStage1Entries(
    deposit: DepositCashTransactionDocument,
    oldDeposit?: DepositCashTransactionDocument
  ): boolean {
    // Check if acquisition stage just completed
    const hasConfirmedIncoming =
      deposit.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status === "confirmed";
    const hadConfirmedIncoming =
      oldDeposit?.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status ===
      "confirmed";
    return hasConfirmedIncoming && !hadConfirmedIncoming;
  }

  public static shouldGenerateDepositStage2Entries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // Only relevant for instant flow
    // Check if collection stage just completed
    const hasConfirmedOutgoing =
      deposit.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status === "confirmed";
    const hadConfirmedOutgoing =
      oldDeposit?.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ===
      "confirmed";
    return hasConfirmedOutgoing && !hadConfirmedOutgoing;
  }

  public static shouldGenerateDepositStage3Entries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // Check if WealthKernel settled the deposit
    const isSettled = deposit.providers?.wealthkernel?.status === "Settled";
    const wasSettled = oldDeposit?.providers?.wealthkernel?.status === "Settled";
    return isSettled && !wasSettled;
  }

  /**
   * Generate stage 1 entries for a deposit (Devengo acquisition)
   */
  public static generateDepositStage1Entries(
    deposit: DepositCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const amount = deposit.consideration.amount;
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    return [
      { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "debit" },
      { account: clientLedgerAccount, amount, type: "credit" }
    ];
  }

  /**
   * Generate stage 2 entries for a deposit (Devengo collection - instant flow only)
   */
  public static generateDepositStage2Entries(deposit: DepositCashTransactionDocument): AccountingEntry[] {
    const amount = deposit.consideration.amount;

    return [
      { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, amount, type: "debit" },
      { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "credit" }
    ];
  }

  /**
   * Generate stage 3 entries for a deposit (WealthKernel settlement)
   */
  public static generateDepositStage3Entries(
    deposit: DepositCashTransactionDocument,
    isInstantFlow: boolean
  ): AccountingEntry[] {
    const amount = deposit.consideration.amount;

    if (isInstantFlow) {
      // Instant flow: from intermediary 2 to omnibus
      return [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, amount, type: "credit" }
      ];
    } else {
      // Standard flow: from intermediary 1 to omnibus
      return [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "credit" }
      ];
    }
  }

  public static shouldGenerateWithdrawalStage1Entries(
    withdrawal: WithdrawalCashTransactionDocument,
    oldWithdrawal?: WithdrawalCashTransactionDocument
  ): boolean {
    // Check if WealthKernel just settled the withdrawal
    const isSettled = withdrawal.providers?.wealthkernel?.status === "Settled";
    const wasSettled = oldWithdrawal?.providers?.wealthkernel?.status === "Settled";
    return isSettled && !wasSettled;
  }

  public static shouldGenerateWithdrawalStage2Entries(
    withdrawal: WithdrawalCashTransactionDocument,
    oldWithdrawal?: WithdrawalCashTransactionDocument
  ): boolean {
    // Check if Devengo collection just confirmed
    const isConfirmed =
      withdrawal.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status === "confirmed";
    const wasConfirmed =
      oldWithdrawal?.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ===
      "confirmed";
    return isConfirmed && !wasConfirmed;
  }

  /**
   * Generate stage 1 entries for a withdrawal (WealthKernel settlement: Omnibus → Intermediary)
   */
  public static generateWithdrawalStage1Entries(withdrawal: WithdrawalCashTransactionDocument): AccountingEntry[] {
    const amount = withdrawal.consideration.amount;

    return [
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "credit" },
      { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, amount, type: "debit" }
    ];
  }

  /**
   * Generate stage 2 entries for a withdrawal (Devengo collection: Intermediary → Client)
   */
  public static generateWithdrawalStage2Entries(
    withdrawal: WithdrawalCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const amount = withdrawal.consideration.amount;
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    return [
      { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, amount, type: "credit" },
      { account: clientLedgerAccount, amount, type: "debit" }
    ];
  }

  /**
   * Generate reward deposit settlement entries (bonus expense)
   *
   * Use bonusAmount (amount + fees) for the deposit settlement
   * Debit: Clients Accounts (Omnibus) - Money flows into omnibus for the user
   * Credit: Wealthyhood Bonus Expense - Company expense for giving bonus
   *
   */
  public static generateRewardDepositEntries(reward: any): AccountingEntry[] {
    const bonusAmount = reward.consideration.bonusAmount;
    return [
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: bonusAmount, type: "debit" },
      { account: LedgerAccounts.BONUS_EXPENSE, amount: bonusAmount, type: "credit" }
    ];
  }

  /**
   * Generate reward order settlement entries (asset movements)
   */
  public static generateRewardOrderMovementEntries(
    reward: any,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const brokerFeeEuros = reward.order?.providers?.wealthkernel?.accountingBrokerFxFee || 0;
    const brokerFee = Decimal.mul(brokerFeeEuros, 100).toNumber();
    const orderAmount = reward.consideration.orderAmount;
    const netOrderAmount = Decimal.sub(orderAmount, brokerFee).toNumber();
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Main Asset Movement (Buy order) - Similar to regular orders
    // Debit client account (reduces their bonus cash), credit omnibus (company collects cash)

    return [
      { account: clientLedgerAccount, amount: netOrderAmount, type: "debit" },
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netOrderAmount, type: "credit" },
      { account: LedgerAccounts.ASSETS_ACTIVE, amount: netOrderAmount, type: "debit" },
      { account: LedgerAccounts.ASSETS_PASSIVE, amount: netOrderAmount, type: "credit" }
    ];
  }

  /**
   * Generate reward commission revenue entries
   */
  public static generateRewardCommissionEntries(
    reward: any,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];
    const fxFeeEuros = reward.fees?.fx?.amount || 0;
    const whCommission = Decimal.mul(fxFeeEuros, 100).toNumber();
    // Calculate broker fee - fees are initially in euros, convert to cents
    const brokerFee = Decimal.mul(
      reward.order?.providers?.wealthkernel?.accountingBrokerFxFee ?? 0,
      100
    ).toNumber();

    // Total Commission Revenue (WH commission + broker commission)
    // Always generate revenue entries to ensure reference number is created, even if commission is 0
    const totalCommission = Decimal.add(whCommission, brokerFee).toNumber();

    return [
      { account: clientLedgerAccount, amount: totalCommission, type: "debit" },
      { account: LedgerAccounts.COMMISSION_FEES_WH, amount: totalCommission, type: "credit" }
    ];
  }

  /**
   * Generate reward broker fee expense entries
   */
  public static generateRewardBrokerFeeEntries(reward: any): AccountingEntry[] {
    const brokerFeeEuros = reward.order?.providers?.wealthkernel?.accountingBrokerFxFee || 0;
    const brokerFee = Decimal.mul(brokerFeeEuros, 100).toNumber();

    if (brokerFee <= 0) {
      return [];
    }

    return [
      { account: LedgerAccounts.BROKER_FEE_EXPENSE, amount: brokerFee, type: "debit" },
      { account: LedgerAccounts.PAYABLES_TO_BROKER, amount: brokerFee, type: "credit" },
      { account: LedgerAccounts.PAYABLES_TO_BROKER, amount: brokerFee, type: "debit" },
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: brokerFee, type: "credit" }
    ];
  }

  private static async _withAccountingContext(
    dbDoc: TransactionDocument | RewardDocument | OrderDocument | GiftDocument,
    user: UserDocument,
    processFn: (context: { userId: string; clientSegment: AccountingClientSegment }) => Promise<{
      entries: AccountingEntriesResult;
      description: string;
      articleDate: string;
    }>
  ): Promise<void> {
    // Check if user belongs to European entity
    if (!user.isEuropeanEntity) {
      // Skip accounting for non-European entities
      return;
    }

    const userId = user._id.toString();
    const clientSegment = user.accountingClientSegment;
    const sourceDocumentType = AccountingService._getSourceDocumentType(dbDoc);

    try {
      const result = await processFn({ userId, clientSegment });
      const { entries, description, articleDate } = result;
      const { movements, revenues, expenses } = entries;

      if (movements.length === 0 && (revenues?.length || 0) === 0 && (expenses?.length || 0) === 0) {
        return;
      }

      const allLedgerEntries: AccountingLedgerEntry[] = [];

      // Process movements - only AccountingRecordIndex (no invoice reference)
      if (movements.length > 0) {
        const movementRecord = await new AccountingRecordIndex({
          linkedDocumentId: dbDoc._id,
          sourceDocumentType
        }).save();

        logger.info("Created accounting record index for movements", {
          module: "AccountingService",
          method: "_withAccountingContext",
          data: {
            documentId: dbDoc._id,
            aaIndex: movementRecord.aaIndex,
            description,
            articleDate,
            entryType: "movements"
          }
        });

        allLedgerEntries.push(
          ...AccountingService._transformEntriesToLedgerDataFormat(movements, {
            aa: movementRecord.aaIndex,
            description,
            articleDate,
            referenceNumber: undefined,
            documentId: dbDoc.id,
            ownerId: userId
          })
        );
      }

      // Process revenues - AccountingRecordIndex + InvoiceReferenceNumber (commission income)
      if (revenues?.length > 0) {
        const [revenueRecord, invoiceReference] = await Promise.all([
          new AccountingRecordIndex({
            linkedDocumentId: dbDoc._id,
            sourceDocumentType
          }).save(),
          new InvoiceReferenceNumber({
            linkedDocumentId: dbDoc._id,
            sourceDocumentType
          }).save()
        ]);

        logger.info("Created accounting record index and invoice reference for revenues", {
          module: "AccountingService",
          method: "_withAccountingContext",
          data: {
            documentId: dbDoc._id,
            aaIndex: revenueRecord.aaIndex,
            invoiceId: invoiceReference.invoiceId,
            description,
            articleDate,
            entryType: "revenues"
          }
        });

        allLedgerEntries.push(
          ...AccountingService._transformEntriesToLedgerDataFormat(revenues, {
            aa: revenueRecord.aaIndex,
            description,
            articleDate,
            referenceNumber: invoiceReference.invoiceId.toString(),
            documentId: dbDoc.id,
            ownerId: userId
          })
        );
      }

      // Process expenses - only AccountingRecordIndex (no invoice reference)
      if (expenses?.length > 0) {
        const expenseRecord = await new AccountingRecordIndex({
          linkedDocumentId: dbDoc._id,
          sourceDocumentType
        }).save();

        logger.info("Created accounting record index for expenses", {
          module: "AccountingService",
          method: "_withAccountingContext",
          data: {
            documentId: dbDoc._id,
            aaIndex: expenseRecord.aaIndex,
            description,
            articleDate,
            entryType: "expenses"
          }
        });

        allLedgerEntries.push(
          ...AccountingService._transformEntriesToLedgerDataFormat(expenses, {
            aa: expenseRecord.aaIndex,
            description,
            articleDate,
            referenceNumber: undefined,
            documentId: dbDoc.id,
            ownerId: userId
          })
        );
      }

      if (allLedgerEntries.length === 0) {
        return;
      }

      logger.info("Storing validated ledger entries", {
        module: "AccountingService",
        method: "_withAccountingContext",
        data: {
          documentId: dbDoc._id,
          ledgerEntries: allLedgerEntries.map((entry) => ({
            aa: entry.aa,
            account_code: entry.account_code,
            side: entry.side,
            amount: entry.amount,
            reference_number: entry.reference_number
          }))
        }
      });
      await AccountingLedgerStorageService.addValidatedLedgerEntries(allLedgerEntries);
    } catch (err) {
      captureException(err);
      logger.error(`Error while storing ledger entries for ${dbDoc._id}.`, {
        module: "AccountingService",
        method: "_withAccountingContext",
        data: {
          transactionId: dbDoc._id,
          err
        }
      });
    }
  }

  private static _getEntryGenerator(key: string): AccountingEntryGeneratorType | undefined {
    const ACCOUNTING_ENTRY_GENERATORS = {
      DepositCashTransaction: {
        entries: async (
          newDeposit: DepositCashTransactionDocument,
          oldDeposit: DepositCashTransactionDocument | null,
          clientSegment: AccountingClientSegment
        ): Promise<AccountingEntriesResult> => {
          await DbUtil.populateIfNotAlreadyPopulated(newDeposit, TransactionPopulationFieldsEnum.CREDIT_TICKET);
          const movements = AccountingService._generateDepositEntries(
            newDeposit,
            oldDeposit as DepositCashTransactionDocument,
            clientSegment
          );
          return { movements };
        },
        description: (userId: string, deposit: DepositCashTransactionDocument): string =>
          AccountingService.getAccountingActivityDescription(
            userId,
            deposit.id,
            AccountingEventType.BANK_TRANSACTION_DEPOSIT
          ),
        articleDate: (): string => new Date(Date.now()).toISOString().slice(0, 10)
      },
      WithdrawalCashTransaction: {
        entries: (
          newWithdrawal: WithdrawalCashTransactionDocument,
          oldWithdrawal: WithdrawalCashTransactionDocument | null,
          clientSegment: AccountingClientSegment
        ): AccountingEntriesResult => {
          const movements = AccountingService._generateWithdrawalEntries(
            newWithdrawal,
            oldWithdrawal as WithdrawalCashTransactionDocument,
            clientSegment
          );
          return { movements };
        },
        description: (userId: string, withdrawal: WithdrawalCashTransactionDocument): string =>
          AccountingService.getAccountingActivityDescription(
            userId,
            withdrawal.id,
            AccountingEventType.BANK_TRANSACTION_WITHDRAWAL
          ),
        articleDate: (): string => new Date(Date.now()).toISOString().slice(0, 10)
      },
      DividendTransaction: {
        entries: (
          dividend: DividendTransactionDocument,
          clientSegment: AccountingClientSegment
        ): AccountingEntriesResult => {
          const wkStatus = dividend.providers.wealthkernel.status;
          if (dividend.status === "Settled" || wkStatus === "Matched") {
            const movements = AccountingService._generateStockDividendReceiptEntries(dividend, clientSegment);
            return { movements };
          }
          return { movements: [] };
        },
        description: (userId: string, dividend: DividendTransactionDocument): string =>
          AccountingService.getAccountingActivityDescription(
            userId,
            dividend.id,
            AccountingEventType.ASSET_DIVIDEND
          ),
        articleDate: (dividend: DividendTransactionDocument): string => {
          return new Date(dividend.settledAt).toISOString().slice(0, 10);
        }
      },
      SavingsDividendTransaction: {
        entries: (
          savingsDividend: SavingsDividendTransactionDocument,
          clientSegment: AccountingClientSegment
        ): AccountingEntriesResult => {
          const movements = AccountingService._generateMMFDividendEntries(savingsDividend, clientSegment);
          return { movements };
        },
        description: (userId: string, savingsDividend: SavingsDividendTransactionDocument): string =>
          AccountingService.getAccountingActivityDescription(
            userId,
            savingsDividend.id,
            AccountingEventType.ASSET_DIVIDEND
          ),
        articleDate: (savingsDividend: SavingsDividendTransactionDocument): string => {
          return new Date(savingsDividend.createdAt).toISOString().slice(0, 10);
        }
      },
      Order: {
        entries: (
          newOrder: OrderDocument,
          oldOrder: OrderDocument | null,
          clientSegment: AccountingClientSegment
        ): AccountingEntriesResult => {
          // Only process when order transitions from not matched to matched
          const wasPreviouslyProcessed = oldOrder?.isMatched;
          if (wasPreviouslyProcessed || !newOrder.isMatched) {
            return { movements: [] };
          }
          return AccountingService._generateAssetOrderEntries(newOrder, clientSegment);
        },
        description: (userId: string, newOrder: OrderDocument): string =>
          AccountingService.getAccountingActivityDescription(
            userId,
            newOrder.id,
            newOrder.side === "Buy" ? AccountingEventType.ASSET_BUY : AccountingEventType.ASSET_SELL,
            newOrder.isin
          ),
        articleDate: (newOrder: OrderDocument): string => {
          return new Date(newOrder.filledAt).toISOString().slice(0, 10);
        }
      },
      Reward: {
        entries: (
          newReward: RewardDocument,
          oldReward: RewardDocument,
          clientSegment: AccountingClientSegment
        ): AccountingEntriesResult => {
          return AccountingService._generateRewardEntries(newReward, oldReward, clientSegment);
        },
        description: (userId: string, reward: RewardDocument): string =>
          AccountingService.getAccountingActivityDescription(userId, reward.id, AccountingEventType.BONUS),
        articleDate: (): string => new Date(Date.now()).toISOString().slice(0, 10)
      },
      Gift: {
        entries: (newGift: GiftDocument, oldGift: GiftDocument | null): AccountingEntriesResult => {
          return AccountingService._generateGiftEntries(newGift, oldGift);
        },
        description: (userId: string, gift: GiftDocument): string =>
          AccountingService.getAccountingActivityDescription(userId, gift.id, AccountingEventType.BONUS),
        articleDate: (newGift: GiftDocument): string =>
          new Date(newGift.deposit?.providers?.wealthkernel?.submittedAt).toISOString().slice(0, 10)
      }
    } as Record<string, AccountingEntryGeneratorType>;

    return ACCOUNTING_ENTRY_GENERATORS[key];
  }

  private static _transformEntriesToLedgerDataFormat(
    entries: AccountingEntry[],
    context: {
      aa: number;
      description: string;
      articleDate: string;
      referenceNumber?: string;
      documentId: string;
      ownerId: string;
    }
  ): AccountingLedgerEntry[] {
    const ledgerEntries: AccountingLedgerEntry[] = [];
    for (const entry of entries) {
      ledgerEntries.push({
        aa: context.aa,
        account_code: entry.account,
        side: entry.type,
        amount: Decimal.div(entry.amount, 100).toNumber(), // Convert cents to euros with decimal
        reference_number: context.referenceNumber,
        article_date: context.articleDate,
        description: context.description,
        document_id: context.documentId,
        owner_id: context.ownerId
      });
    }
    return ledgerEntries;
  }

  private static _generateDepositEntries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];

    // Handle bank transfer & direct debit deposit methods
    switch (deposit.depositMethod) {
      case DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER:
        entries.push(
          ...AccountingService._generateDirectDebitEntries(
            deposit,
            oldDeposit as DepositCashTransactionDocument,
            clientSegment
          )
        );
        break;

      case DepositMethodEnum.BANK_TRANSFER: {
        // Check if instant flow (CreditTicket is Credited)
        const inInstantFlow = deposit.inInstantMoneyFlow;

        if (inInstantFlow) {
          // Instant flow: All 3 stages (includes intermediary 2)
          entries.push(...AccountingService._generateDepositInstantFlowEntries(deposit, oldDeposit));
        } else {
          // Standard flow: Only stages 1 & 3 (skip intermediary 2)
          entries.push(...AccountingService._generateDepositStandardFlowEntries(deposit, oldDeposit));
        }
        break;
      }
    }

    return entries;
  }

  private static _generateDepositInstantFlowEntries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];
    const clientSegment = (deposit.owner as UserDocument).accountingClientSegment;

    // Stage 1: Money received in intermediary 1
    if (AccountingService.shouldGenerateDepositStage1Entries(deposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage1Entries(deposit, clientSegment));
    }

    // Stage 2: Money moved to intermediary 2 (ONLY in instant flow)
    if (AccountingService.shouldGenerateDepositStage2Entries(deposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage2Entries(deposit));
    }

    // Stage 3: Money settled to omnibus from intermediary 2
    if (AccountingService.shouldGenerateDepositStage3Entries(deposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage3Entries(deposit, true)); // true = instant flow
    }

    return entries;
  }

  private static _generateDepositStandardFlowEntries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];
    const clientSegment = (deposit.owner as UserDocument).accountingClientSegment;

    // Stage 1: Money received in intermediary 1
    if (AccountingService.shouldGenerateDepositStage1Entries(deposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage1Entries(deposit, clientSegment));
    }

    // Stage 3: Money settled DIRECTLY from intermediary 1 to omnibus (skipping intermediary 2)
    if (AccountingService.shouldGenerateDepositStage3Entries(deposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage3Entries(deposit, false)); // false = standard flow
    }

    return entries;
  }

  private static _generateDirectDebitEntries(
    newDeposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];

    // Stage 1: Direct debit collection (User's bank account → Intermediary)
    if (this._shouldGenerateDirectDebitStage1Entries(newDeposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage1Entries(newDeposit, clientSegment));
    }

    // Stage 2: Settlement (Intermediary → Omnibus)
    if (this._shouldGenerateDirectDebitStage2Entries(newDeposit, oldDeposit)) {
      entries.push(...AccountingService.generateDepositStage3Entries(newDeposit, false)); // false = standard flow (intermediary 1 → omnibus)
    }

    return entries;
  }

  private static _shouldGenerateDirectDebitStage1Entries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // check for Devengo collection status
    const isDevengoCollected =
      deposit?.transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.status === "confirmed";
    const wasDevengoCollected =
      oldDeposit?.transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.status ===
      "confirmed";

    return isDevengoCollected && !wasDevengoCollected;
  }

  private static _shouldGenerateDirectDebitStage2Entries(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // Check if WealthKernel settled the deposit
    const isSettled = deposit.providers?.wealthkernel?.status === "Settled";
    const wasSettled = oldDeposit?.providers?.wealthkernel?.status === "Settled";
    return isSettled && !wasSettled;
  }

  private static _generateWithdrawalEntries(
    newWithdrawal: WithdrawalCashTransactionDocument,
    oldWithdrawal: WithdrawalCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];

    // Stage 1: Omnibus to Intermediary (WealthKernel settlement)
    if (AccountingService.shouldGenerateWithdrawalStage1Entries(newWithdrawal, oldWithdrawal)) {
      entries.push(...AccountingService.generateWithdrawalStage1Entries(newWithdrawal));
    }

    // Stage 2: Intermediary to Client (Devengo collection)
    if (AccountingService.shouldGenerateWithdrawalStage2Entries(newWithdrawal, oldWithdrawal)) {
      entries.push(...AccountingService.generateWithdrawalStage2Entries(newWithdrawal, clientSegment));
    }

    return entries;
  }

  private static _generateStockDividendReceiptEntries(
    transaction: DividendTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];
    const amount = transaction.consideration.amount ?? 0;
    if (amount <= 0) return entries; // No amount, no entries

    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    entries.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" });
    entries.push({ account: clientLedgerAccount, amount, type: "credit" });

    return entries;
  }

  private static _generateMMFDividendEntries(
    transaction: SavingsDividendTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const entries: AccountingEntry[] = [];
    const grossAmount = transaction.originalDividendAmount ?? 0;
    // convert all to cents
    const commissionAmount = Decimal.mul(transaction.fees?.commission?.amount ?? 0, 100).toNumber();

    if (grossAmount <= 0) {
      return []; // No dividend, no accounting
    }

    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Entry 1: Gross Dividend Receipt
    entries.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: grossAmount, type: "debit" });
    entries.push({ account: clientLedgerAccount, amount: grossAmount, type: "credit" });

    // Entry 2: Commission Fee on Dividend
    if (commissionAmount > 0) {
      entries.push({ account: clientLedgerAccount, amount: commissionAmount, type: "debit" });
      entries.push({ account: LedgerAccounts.MMF_DIVIDEND_FEES_WH, amount: commissionAmount, type: "credit" });
    }

    // Note: The reinvestment (buy) part is explicitly deferred to a later step
    return entries;
  }

  private static _generateAssetOrderEntries(
    order: OrderDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntriesResult {
    const movements: AccountingEntry[] = [];
    const revenues: AccountingEntry[] = [];
    const expenses: AccountingEntry[] = [];
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Amounts is in cents
    const settlementAmount = order.consideration.amount ?? 0;
    const whCommission = Decimal.add(order.fees.fx.amount, order.fees.realtimeExecution.amount)
      .mul(100)
      .toNumber();
    // Fees are initially in euros, so we need to convert to cents
    const brokerFee = Decimal.mul(order.providers.wealthkernel.accountingBrokerFxFee ?? 0, 100).toNumber();

    const netAmount = Decimal.sub(settlementAmount, brokerFee).toNumber();

    // Main Asset Movement
    if (order.side === "Buy") {
      movements.push({ account: clientLedgerAccount, amount: netAmount, type: "debit" });
      movements.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "credit" });
      movements.push({ account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "debit" });
      movements.push({ account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "credit" });

      const remainderEntries = AccountingService._generateOrderRemainderEntries(order, clientSegment);
      movements.push(...remainderEntries);
    } else if (order.side === "Sell") {
      movements.push({ account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "debit" });
      movements.push({ account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "credit" });
      movements.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "debit" });
      movements.push({ account: clientLedgerAccount, amount: netAmount, type: "credit" });
    }

    // Total Commission Revenue (WH commission + broker commission)
    // Always generate revenue entries to ensure reference number is created, even if commission is 0
    const totalCommission = Decimal.add(whCommission, brokerFee).toNumber();
    revenues.push({ account: clientLedgerAccount, amount: totalCommission, type: "debit" });
    revenues.push({ account: LedgerAccounts.COMMISSION_FEES_WH, amount: totalCommission, type: "credit" });

    // Broker Fee - Expense
    if (brokerFee > 0) {
      expenses.push({ account: LedgerAccounts.BROKER_FEE_EXPENSE, amount: brokerFee, type: "debit" });
      expenses.push({ account: LedgerAccounts.PAYABLES_TO_BROKER, amount: brokerFee, type: "credit" });
      expenses.push({ account: LedgerAccounts.PAYABLES_TO_BROKER, amount: brokerFee, type: "debit" });
      expenses.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: brokerFee, type: "credit" });
    }

    return { movements, revenues, expenses };
  }

  /**
   * Generate accounting entries for order remainders
   * Remainders represent unallocated cash from asset buy orders that couldn't be invested due to fractional shares
   * This creates cash movement entries returning the remainder to the client's account (credit client, debit omnibus)
   */
  private static _generateOrderRemainderEntries(
    order: OrderDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntry[] {
    const remainderAmountCents = order.remainder; // in cents

    if (remainderAmountCents <= 0) {
      return [];
    }

    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Remainder entries: opposite of normal buy flow (credit client account, debit omnibus)
    // This represents unallocated cash staying in the client's account
    return [
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: remainderAmountCents, type: "debit" },
      { account: clientLedgerAccount, amount: remainderAmountCents, type: "credit" }
    ];
  }

  private static _generateRewardEntries(
    reward: RewardDocument,
    oldReward: RewardDocument,
    clientSegment: AccountingClientSegment
  ): AccountingEntriesResult {
    const movements: AccountingEntry[] = [];
    const revenues: AccountingEntry[] = [];
    const expenses: AccountingEntry[] = [];

    // Check if reward deposit just settled (transitioned from not settled to settled)
    const isDepositSettled = reward.depositStatus === "Settled";
    const wasDepositSettled = oldReward?.depositStatus === "Settled";

    if (isDepositSettled && !wasDepositSettled) {
      // Step 1: Transfer from Wealthyhood bonus account to user's account
      movements.push(...AccountingService.generateRewardDepositEntries(reward));
    }

    // Check if reward order just settled (transitioned from not matched to matched)
    const isOrderSettled = reward.status === "Settled";
    const wasOrderSettled = oldReward?.status === "Settled";

    if (isOrderSettled && !wasOrderSettled) {
      // Step 2: Reward order execution

      // Main Asset Movement (Buy order)
      movements.push(...AccountingService.generateRewardOrderMovementEntries(reward, clientSegment));

      // Total Commission Revenue (WH commission + broker commission)
      // Always generate revenue entries to ensure reference number is created, even if commission is 0
      revenues.push(...AccountingService.generateRewardCommissionEntries(reward, clientSegment));

      // Broker Fee Expense (only if broker fee > 0)
      expenses.push(...AccountingService.generateRewardBrokerFeeEntries(reward));
    }

    return { movements, revenues, expenses };
  }

  private static _generateGiftEntries(gift: GiftDocument, oldGift: GiftDocument | null): AccountingEntriesResult {
    const movements: AccountingEntry[] = [];
    const settledAmount = gift.consideration.amount;

    // Check if gift deposit just settled (transitioned from not settled to settled)
    const isDepositSettled = gift.deposit?.providers?.wealthkernel?.status === "Settled";
    const wasDepositSettled = oldGift?.deposit?.providers?.wealthkernel?.status === "Settled";

    if (isDepositSettled && !wasDepositSettled) {
      // Gift deposit settlement: Transfer from Wealthyhood bonus account to user's account
      // This is similar to reward bonus transfer but for gifts
      // Debit: Clients Accounts (Omnibus) - Money flows into omnibus for the user
      // Credit: Wealthyhood Bonus Expense - Company expense for giving gift
      movements.push(
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: settledAmount, type: "debit" },
        { account: LedgerAccounts.BONUS_EXPENSE, amount: settledAmount, type: "credit" }
      );
    }

    // Note: Gift investment execution is handled separately through the normal order processing flow
    // when the asset transaction status changes from "PendingGift" to "Pending" and orders are executed

    return { movements };
  }

  private static _getSourceDocumentType(
    dbDoc: TransactionDocument | RewardDocument | OrderDocument | GiftDocument
  ): "Transaction" | "Reward" | "Order" | "Gift" {
    // Use mongoose model name to reliably identify document type
    const modelName = (dbDoc.constructor as any).modelName;

    switch (modelName) {
      case "Reward":
        return "Reward";
      case "Order":
        return "Order";
      case "Gift":
        return "Gift";
      case "Transaction":
      default:
        return "Transaction";
    }
  }
}
