import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../external-services/loggerService";
import { SaltedgeEventPayloadType, SaltedgeService } from "../external-services/saltedgeService";
import { TransactionService } from "../services/transactionService";
import { BadRequestError } from "../models/ApiErrors";
import DbUtil from "../utils/dbUtil";
import { TransactionPopulationFieldsEnum } from "../models/Transaction";
import { UserDocument } from "../models/User";
import BankAccountService from "../services/bankAccountService";
import BanksUtil from "../utils/banksUtil";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";

/**
 * This controller handles webhooks received from Saltedge.
 *
 * We're listening for a single webhook event related to payments:
 *
 * More information on webhooks we're listening to can be found here:
 * https://docs.saltedge.com/partners_payment_initiation/v1/#callbacks_partners_pisp_v1
 *
 */
class SaltedgeWebhookController {
  public static async processWebhook(req: CustomRequest, res: Response): Promise<Response> {
    SaltedgeService.validateWebhookSignature(req);

    const body = req.body as SaltedgeEventPayloadType;

    logger.info(`Received event for payment ${body.data.payment_id}`, {
      module: "SaltedgeWebhookController",
      method: "processWebhook",
      data: {
        body
      }
    });

    const payment = await SaltedgeService.Instance.getPayment(body.data.payment_id);

    const deposit = await TransactionService.getDepositBySaltedgeCustomId(
      payment.data.payment_attributes.end_to_end_id
    );
    if (!deposit) {
      throw new BadRequestError(
        `Received Saltedge webhook but custom ID ${payment.data.payment_attributes.end_to_end_id} does not match any deposit!`
      );
    }

    const updatedDeposit = await TransactionService.updateDepositSaltedgeData(
      payment.data.id,
      payment.data.payment_attributes.end_to_end_id,
      payment.data.status
    );

    if (updatedDeposit.isSaltedgeFlowCompleted) {
      await DbUtil.populateIfNotAlreadyPopulated(updatedDeposit, TransactionPopulationFieldsEnum.OWNER);
      const user = updatedDeposit.owner as UserDocument;

      // For some banks, IBAN is not returned when the payment is completed. For those, we'll create the relevant
      // bank account when we receive the payment in Devengo (where the IBAN is known).
      if (payment.data?.payment_attributes?.debtor_iban) {
        const bankAccount = await BankAccountService.createBankAccount(
          {
            iban: payment.data.payment_attributes.debtor_iban,
            name: user.fullName,
            active: true,
            owner: user.id,
            activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BANK_ACCOUNTS]),
            bankId: BanksUtil.getBankFromSaltedgeInstitutionId(payment.data.provider_code),
            currency: "EUR"
          },
          user
        );

        await TransactionService.addBankAccountToDeposit(updatedDeposit.id, bankAccount.id);
      }
    }

    logger.info(`Successfully handled event for payment ${body.data.payment_id}`, {
      module: "SaltedgeWebhookController",
      method: "processWebhook"
    });

    return res.sendStatus(200);
  }
}

export default SaltedgeWebhookController;
