import { CustomRequest } from "custom";
import { Response } from "express";
import { TransactionRepository } from "../repositories/transactionRepository";
import { WealthyhoodBankAccountStatusArray, WealthyhoodBankAccountStatusType } from "../models/BankAccount";
import BankAccountRepository from "../repositories/bankAccountRepository";
import BankAccountService from "../services/bankAccountService";
import { TransactionService } from "../services/transactionService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";

export class AdminBankAccountController {
  public static readonly createAllWkBankAccounts = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await BankAccountService.createAllWkBankAccounts();
    return res.sendStatus(204);
  };

  public static readonly syncAllWkBankAccounts = async (req: CustomRequest, res: Response): Promise<Response> => {
    await BankAccountService.syncAllWkBankAccounts();
    return res.sendStatus(204);
  };

  public static readonly getSuspendedBankAccounts = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const suspendedBankAccounts = await BankAccountService.getSuspendedBankAccounts();
    return res.status(200).json({ data: suspendedBankAccounts });
  };

  public static readonly getPendingBankAccounts = async (req: CustomRequest, res: Response): Promise<Response> => {
    const pendingBankAccounts = await BankAccountService.getPendingBankAccounts();
    return res.status(200).json({ data: pendingBankAccounts });
  };

  public static readonly updateBankAccountWealthyhoodStatus = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const bankAccountId = ParamsValidationUtil.isObjectIdParamValid("bankAccountId", req.params.id);
    const status = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "status",
      req.body.status,
      WealthyhoodBankAccountStatusArray
    ) as WealthyhoodBankAccountStatusType;

    const [updatedBankAccount, pendingDepositsForBankAccount] = await Promise.all([
      BankAccountRepository.updateWealthyhoodStatus(bankAccountId, status),
      TransactionRepository.getPendingBankTransferDepositsForBankAccount(bankAccountId)
    ]);

    for (const deposit of pendingDepositsForBankAccount) {
      await TransactionService.syncDepositWkStatusSafely(deposit);
    }

    return res.status(200).json({ data: updatedBankAccount });
  };
}
