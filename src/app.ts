import { hcCaptureXhr } from "./external-services/libhoney";
import * as Sentry from "@sentry/node";
import * as SentryProfiling from "@sentry/profiling-node";
import express, { NextFunction, Request, Response } from "express";
import { envIsDemo, envIsDev, envIsProd } from "./utils/environmentUtil";

const app = express();

Sentry.init({
  enabled: envIsProd(),
  dsn: process.env.SENTRY_DNS,
  environment: process.env.NODE_ENV,
  integrations: [
    SentryProfiling.nodeProfilingIntegration(),
    Sentry.extraErrorDataIntegration(),
    Sentry.httpIntegration({
      ignoreOutgoingRequests: (url) => {
        if (url.includes("datadog")) {
          return true;
        }
        return false;
      }
    }),
    Sentry.expressIntegration(),
    Sentry.mongoIntegration(),
    Sentry.mongooseIntegration()
  ],
  profilesSampler: (samplingContext) => {
    if (envIsProd()) {
      if (samplingContext?.request?.url?.includes("/healthz")) {
        return false;
      } else {
        return 0.2;
      }
    } else {
      return false;
    }
  },
  tracesSampler: (samplingContext) => {
    if (envIsProd()) {
      if (samplingContext?.request?.url?.includes("/healthz")) {
        return false;
      } else {
        return 0.2;
      }
    } else {
      return false;
    }
  }
});

import bodyParser from "body-parser";
import apiDocsRoutes from "./routes/apiDocsRoutes";
import logger from "./external-services/loggerService";
import cors from "cors";
import path from "path";
import ErrorMiddleware from "./middlewares/errorMiddleware";
import adminM2mRoutes from "./routes/adminM2mRoutes";
import c2mRoutes from "./routes/c2mRoutes";
import m2mRoutes from "./routes/m2mRoutes";
import publicMainRoutes from "./routes/publicMainRoutes";
import testRoutes from "./routes/testRoutes";

app.use(cors());

/**
 * Trust proxy
 */

// Needed because we're behind a load balancer or reverse proxy
if (process.env.NODE_ENV !== "development") {
  app.enable("trust proxy"); // TODO: we should specify a subnet?

  app.use((req: Request, res: Response, next: NextFunction) => {
    if (req.secure) {
      return next();
    }
    // TODO: sanitize
    logger.warn(`req is not secure, redirecting to: https://${req.headers.host}${req.url}`);
    res.redirect("https://" + req.headers.host + req.url);
  });
}

app.use((req: Request, res: Response, next: NextFunction) => {
  const isWealthkernelWebhook = /wealthkernel\/(?:eu\/|uk\/)?webhooks/.test(req.url);

  // For some webhook endpoints, body parsing middleware does not allow signature verification to work correctly
  if (req.url.includes("stripe/webhooks") || req.url.includes("sumsub/webhooks") || isWealthkernelWebhook) {
    return next();
  }
  return bodyParser.json()(req, res, next);
});

app.use(bodyParser.urlencoded({ extended: false }));

// HoneyComb handler
app.use(
  hcCaptureXhr({
    writeKey: process.env.HONEYCOMB_API_KEY,
    dataset: "app-api-xhr",
    disabled: process.env.NODE_ENV === "development"
  })
);

// Redirect the root endpoint to '/static/index.html'
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "../static", "index.html"));
});
app.use("/", publicMainRoutes);
app.use("/api/c2m/", c2mRoutes);
app.use("/api/m2m/", m2mRoutes);
app.use("/api/admin/m2m/", adminM2mRoutes);
app.use("/test/", testRoutes);

if (envIsDev() || envIsDemo()) {
  app.use("/static", express.static(path.join(__dirname, "../static")));
  app.use("/api/", apiDocsRoutes);
}

// Add this after all routes,
// but before any and other error-handling middlewares are defined
Sentry.setupExpressErrorHandler(app);

// IMPORTANT GLOBAL ERROR HANDLER MUST BE THE FINAL ROUTE
app.use(ErrorMiddleware.handleNotFound);
app.use(ErrorMiddleware.handleGlobalErrors);

export default app;
