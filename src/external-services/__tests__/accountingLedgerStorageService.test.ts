import "jest";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../tests/utils/db";
import AccountingLedgerStorageService from "../accountingLedgerStorageService";

describe("AccountingLedgerStorageService", () => {
  beforeAll(async () => {
    await connectDb("accountingLedgerStorageService");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  describe("getLastProcessedId", () => {
    it("should return null when no checkpoint exists for report type", async () => {
      const lastProcessedId = await AccountingLedgerStorageService.getLastProcessedId("nonexistent_report");
      expect(lastProcessedId).toBeNull();
    });

    it("should return the most recent last processed ID for a report type", async () => {
      const reportType = "test_report";

      // Add multiple checkpoints for the same report type
      await AccountingLedgerStorageService.updateLastProcessedId(reportType, 100);
      await AccountingLedgerStorageService.updateLastProcessedId(reportType, 200);
      await AccountingLedgerStorageService.updateLastProcessedId(reportType, 150); // Most recent timestamp

      const lastProcessedId = await AccountingLedgerStorageService.getLastProcessedId(reportType);
      expect(lastProcessedId).toBe(150); // Should return the most recently processed ID
    });

    it("should return different last processed IDs for different report types", async () => {
      const reportType1 = "report_type_1";
      const reportType2 = "report_type_2";

      await AccountingLedgerStorageService.updateLastProcessedId(reportType1, 100);
      await AccountingLedgerStorageService.updateLastProcessedId(reportType2, 200);

      const lastProcessedId1 = await AccountingLedgerStorageService.getLastProcessedId(reportType1);
      const lastProcessedId2 = await AccountingLedgerStorageService.getLastProcessedId(reportType2);

      expect(lastProcessedId1).toBe(100);
      expect(lastProcessedId2).toBe(200);
    });
  });

  describe("updateLastProcessedId", () => {
    it("should successfully store a new checkpoint", async () => {
      const reportType = "test_report";
      const lastProcessedId = 42;

      const result = await AccountingLedgerStorageService.updateLastProcessedId(reportType, lastProcessedId);
      expect(result.success).toBe(true);

      const retrievedId = await AccountingLedgerStorageService.getLastProcessedId(reportType);
      expect(retrievedId).toBe(lastProcessedId);
    });

    it("should store multiple checkpoints with timestamps", async () => {
      const reportType = "test_report";

      const result1 = await AccountingLedgerStorageService.updateLastProcessedId(reportType, 100);
      expect(result1.success).toBe(true);

      // Wait a bit to ensure different timestamps
      await new Promise((resolve) => setTimeout(resolve, 10));

      const result2 = await AccountingLedgerStorageService.updateLastProcessedId(reportType, 200);
      expect(result2.success).toBe(true);

      const checkpoints = await AccountingLedgerStorageService.getAllReportingCheckpoints();
      expect(checkpoints.length).toBeGreaterThanOrEqual(2);

      const reportCheckpoints = checkpoints.filter((cp) => cp.reportType === reportType);
      expect(reportCheckpoints.length).toBe(2);
    });
  });

  describe("queryLedgerEntriesFromId", () => {
    beforeEach(async () => {
      // Add some test ledger entries
      await AccountingLedgerStorageService.addLedgerEntries([
        {
          aa: 1,
          account_code: "30-00-00-0000",
          side: "debit",
          amount: 100.0,
          article_date: "2024-01-15",
          description: "test entry 1",
          document_id: "doc1",
          owner_id: "user1"
        },
        {
          aa: 2,
          account_code: "30-00-00-0000",
          side: "credit",
          amount: 200.0,
          article_date: "2024-01-16",
          description: "test entry 2",
          document_id: "doc2",
          owner_id: "user2"
        },
        {
          aa: 3,
          account_code: "30-00-00-0000",
          side: "debit",
          amount: 300.0,
          article_date: "2024-01-17",
          description: "test entry 3",
          document_id: "doc3",
          owner_id: "user3"
        }
      ]);
    });

    it("should return all entries when no fromId is specified", async () => {
      const entries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({});
      expect(entries.length).toBe(3);
    });

    it("should return entries starting from specified ID", async () => {
      // Get all entries first to know their IDs
      const allEntries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({});
      expect(allEntries.length).toBe(3);

      const firstEntryId = allEntries[0].id!;

      // Query entries starting from the first entry's ID
      const entriesFromId = await AccountingLedgerStorageService.queryLedgerEntriesFromId({
        fromId: firstEntryId
      });

      // Should return the 2 entries after the first one
      expect(entriesFromId.length).toBe(2);
      expect(entriesFromId.every((entry) => entry.id! > firstEntryId)).toBe(true);
    });

    it("should respect date filters when querying from ID", async () => {
      const entries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({
        article_date_from: "2024-01-16",
        article_date_to: "2024-01-16"
      });

      expect(entries.length).toBe(1);
      expect(entries[0].article_date).toBe("2024-01-16");
    });

    it("should respect limit parameter", async () => {
      const entries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({
        limit: 2
      });

      expect(entries.length).toBe(2);
    });

    it("should combine fromId and date filters correctly", async () => {
      const allEntries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({});
      const firstEntryId = allEntries[0].id!;

      const entries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({
        fromId: firstEntryId,
        article_date_from: "2024-01-17"
      });

      expect(entries.length).toBe(1);
      expect(entries[0].article_date).toBe("2024-01-17");
      expect(entries[0].id!).toBeGreaterThan(firstEntryId);
    });
  });

  describe("getAllReportingCheckpoints", () => {
    it("should return empty array when no checkpoints exist", async () => {
      const checkpoints = await AccountingLedgerStorageService.getAllReportingCheckpoints();
      expect(checkpoints).toEqual([]);
    });

    it("should return all checkpoints ordered by processed_at desc", async () => {
      const reportType1 = "report_1";
      const reportType2 = "report_2";

      await AccountingLedgerStorageService.updateLastProcessedId(reportType1, 100);
      await AccountingLedgerStorageService.updateLastProcessedId(reportType2, 200);
      await AccountingLedgerStorageService.updateLastProcessedId(reportType1, 150);

      const checkpoints = await AccountingLedgerStorageService.getAllReportingCheckpoints();
      expect(checkpoints.length).toBe(3);

      // Should be ordered by processed_at desc (most recent first)
      expect(checkpoints[0].reportType).toBe(reportType1);
      expect(checkpoints[0].lastProcessedId).toBe(150);
    });
  });
});
