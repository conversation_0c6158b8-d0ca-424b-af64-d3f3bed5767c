import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import { RewardDocument } from "../models/Reward";
import { TransactionalNotificationEventEnum } from "./notificationEvents";
import NotificationService from "../services/notificationService";

class ReferralEventHandler {
  constructor() {
    eventEmitter.on(events.referral.referralRewardCreation.eventId, this._handleReferralRewardCreation.bind(this));
    eventEmitter.on(events.referral.referrerRewardCreation.eventId, this._handleReferrerRewardCreation.bind(this));
    eventEmitter.on(events.referral.rewardSettled.eventId, this._handleRewardSettled.bind(this));
  }

  private async _handleReferralRewardCreation(user: UserDocument): Promise<void> {
    // App notification removed - now only sent when reward is settled
  }

  private async _handleReferrerRewardCreation(user: UserDocument): Promise<void> {
    // App notification removed - now only sent when reward is settled
  }

  private async _handleRewardSettled(targetUser: UserDocument, data?: { reward: RewardDocument }): Promise<void> {
    // Send app notification to target user about settled reward
    await NotificationService.createAppNotification(
      targetUser.id,
      { notificationId: TransactionalNotificationEventEnum.REFERRAL_SUCCESS_3 },
      { sendImmediately: true }
    );

    // Use Promise.allSettled to send both the app notification and email notification concurrently.
    // This ensures both notifications are attempted, and we can later inspect results if needed.
    await Promise.allSettled([
      NotificationService.createAppNotification(
        targetUser.id,
        { notificationId: TransactionalNotificationEventEnum.REFERRAL_SUCCESS_2A },
        { sendImmediately: true }
      ),
      NotificationService.createEmailNotification(
        targetUser.id,
        {
          notificationId: "rewardSuccess",
          properties: new Map(Object.entries({}))
        },
        { sendImmediately: true }
      )
    ]);
  }
}

export default ReferralEventHandler;
