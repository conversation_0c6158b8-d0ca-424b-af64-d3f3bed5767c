import Decimal from "decimal.js";
import eventEmitter from "../loaders/eventEmitter";
import CurrencyUtil from "../utils/currencyUtil";
import events from "./events";
import analytics, { TrackCreditTicketReportedPropertiesType } from "../external-services/segmentAnalyticsService";
import { nanoid } from "nanoid";

class CreditTicketEventHandler {
  constructor() {
    eventEmitter.on(
      events.creditTickets.creditedAmountReported.eventId,
      this._handleCreditTicketReported.bind(this)
    );
  }

  private _handleCreditTicketReported(properties: TrackCreditTicketReportedPropertiesType): void {
    const messages = [];

    // 1. Report total credited amount
    messages.push(
      `We have currently ${CurrencyUtil.formatCurrency(properties.totalOpenCreditAmount, "EUR", "en")} in open credit tickets.`
    );

    // 2. In-flight payments
    messages.push(
      `In-flight Devengo amount: ${CurrencyUtil.formatCurrency(properties.inFlightPaymentsAmount, "EUR", "en")}.`
    );

    // 3. Compare total credited amount with in-flight payments
    if (!new Decimal(properties.totalOpenCreditAmount).equals(new Decimal(properties.inFlightPaymentsAmount))) {
      messages.push(
        `⚠️ Mismatch between total credited amount (${CurrencyUtil.formatCurrency(
          properties.totalOpenCreditAmount,
          "EUR",
          "en"
        )}) and in-flight payments amount (${CurrencyUtil.formatCurrency(
          properties.inFlightPaymentsAmount,
          "EUR",
          "en"
        )}).`
      );
    } else {
      messages.push("✅️ Total credited amount matches in-flight payments amount.");
    }

    // 4. Wealthkernel funding cash balance
    messages.push(
      `Wealthkernel funding cash balance: ${CurrencyUtil.formatCurrency(properties.wkFundingCashBalance, "EUR", "en")}`
    );

    // 5. Total of in-flight payments and WK funding cash balance
    const totalInFlightAndWK = new Decimal(properties.inFlightPaymentsAmount)
      .add(properties.wkFundingCashBalance)
      .toNumber();
    messages.push(
      `Total in-flight payments + WK funding cash balance: ${CurrencyUtil.formatCurrency(totalInFlightAndWK, "EUR", "en")}`
    );

    // 6. Report old open tickets
    if (properties.oldOpenTickets > 0) {
      messages.push(
        `⚠️ There are ${properties.oldOpenTickets} credit tickets that have been open for more than 2 business days.`
      );
    } else {
      messages.push("✅️ There are no credit tickets that have been open for more than 2 business days.");
    }

    // 7. Report deposit vs credited amount match
    if (!properties.depositVsCreditedMatch) {
      messages.push(
        "⚠️ Mismatch between deposits and credited amounts of the day:\n" +
          `- Total EU deposits: ${CurrencyUtil.formatCurrency(properties.totalRecentDeposits, "EUR", "en")}\n` +
          `- Total credited amount: ${CurrencyUtil.formatCurrency(properties.totalRecentCredited, "EUR", "en")}`
      );
    } else {
      messages.push("✅️ Recent deposits and credited amounts match!");
    }

    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.creditTickets.creditedAmountReported.name,
        properties: {
          message: messages.join("\n")
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }
}

export default CreditTicketEventHandler;
