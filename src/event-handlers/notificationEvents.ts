// This is a list of events (transactional, learning & promotional) that are sent from Mailchimp or our API
// and that are currently consumed by OneSignal.

export enum TransactionalNotificationEventEnum {
  KYC_SUCCESS = "transactional_kyc_success",
  DEPOSIT_FAILURE = "transactional_deposit_failed",
  DEPOSIT_AVAILABLE = "transactional_deposit_available",
  ORDER_SETTLED = "transactional_order_settled",
  REFERRAL_SUCCESS_2A = "prompt_referrals_2a",
  REFERRAL_SUCCESS_2B = "prompt_referrals_2b",
  REFERRAL_SUCCESS_3 = "prompt_referrals_3",
  GIFT_RECEIPT_UNVERIFIED = "prompt_receive_gift_unverified",
  GIFT_RECEIPT_VERIFIED = "prompt_receive_gift_verified",
  DIVIDEND_RECEIVED = "transactional_dividend_received",
  SAVINGS_DIVIDEND_RECEIVED = "transactional_savings_dividend_received",
  WEALTHYHOOD_DIVIDEND_CREATED = "transactional_wealthyhood_dividend_created",
  REBALANCE_COMPLETED = "transactional_rebalance_completed",
  REPEATING_INVESTMENT_SETTLED = "transactional_repeating_investment_settled",
  AUTOMATED_REBALANCE_COMPLETED = "transactional_automated_rebalancing_settled"
}

export enum LearningNotificationEventEnum {
  ANALYSIS_CREATED = "learning_analysis_created",
  WEEKLY_REVIEW_CREATED = "learning_weekly_review_created",
  QUICK_TAKE_CREATED = "learning_quick_take_created",
  GUIDE_CREATED = "learning_guide_created",
  DAILY_MARKET_SUMMARY = "learning_daily_market_recap"
}

export const mailchimpWebhookEvents = [
  // Verify prompts
  "prompt_verify_3a",
  "prompt_verify_3b",

  // Invest prompts
  "prompt_invest_4a",
  "prompt_invest_4b",

  // Referrals
  "prompt_referrals_1a",

  // Free ETF campaign prompt
  "prompt_free_etf_campaign_1a",

  // Send £10 gift prompt
  "prompt_send_gift",

  // Autopilot prompt
  "prompt_autopilot_1a",
  "prompt_autopilot_1b"
] as const;

export type MailchimpWebhookNotificationEventType = (typeof mailchimpWebhookEvents)[number];
