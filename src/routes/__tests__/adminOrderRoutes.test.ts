import request from "supertest";
import app from "../../app";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildChargeTransaction,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildReward,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import { WealthkernelService, CurrencyEnum } from "../../external-services/wealthkernelService";
import { ASSET_CONFIG } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { Order, OrderDocument, OrderSubmissionIntentEnum } from "../../models/Order";
import Decimal from "decimal.js";
import DateUtil from "../../utils/dateUtil";
import { ProviderEnum } from "../../configs/providersConfig";

describe("AdminOrderRoutes", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("AdminOrderRoutes"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("/orders", function () {
    let user: UserDocument;
    const CONSIDERATION_AMOUNT = 5;
    const QUANTITY = 10;

    beforeEach(async () => {
      user = await buildUser();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    describe("POST /orders/analytics", () => {
      describe("when a non numeric page query param is passed", () => {
        it("should return 400", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=something&pageSize=10")
            .set("Accept", "application/json");

          expect(response.status).toEqual(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Invalid parameter",
                message: "Invalid value for param 'page' , should be numeric"
              }
            })
          );
        });
      });

      describe("when there is one buy order submitted the day before", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: SUBMISSION_DATE.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 1,
                        buyConsideration: CONSIDERATION_AMOUNT,
                        sellOrders: 0,
                        sellConsideration: 0,
                        allMatched: false
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there is one buy order and one reward submitted the day before", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();
        const REWARD_AMOUNT = 1000; // £10
        const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          await buildPortfolio({
            owner: referrer.id,
            providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
          });
          await buildPortfolio({
            owner: referral.id,
            providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
          });
          await buildReward({
            referrer: referrer.id,
            referral: referral.id,
            targetUser: referral.id,
            status: "Settled",
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched",
                  submittedAt: SUBMISSION_DATE
                }
              }
            },
            asset: "equities_uk",
            consideration: {
              amount: REWARD_AMOUNT,
              orderAmount: REWARD_AMOUNT,
              bonusAmount: REWARD_AMOUNT,
              currency: "GBP"
            }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's orders", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: SUBMISSION_DATE.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 2,
                        buyConsideration: Decimal.add(
                          CONSIDERATION_AMOUNT,
                          Decimal.div(REWARD_AMOUNT, 100)
                        ).toNumber(),
                        sellOrders: 0,
                        sellConsideration: 0,
                        allMatched: false
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there is one sell order submitted the day before and it is matched", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: SUBMISSION_DATE.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 0,
                        buyConsideration: 0,
                        sellOrders: 1,
                        sellConsideration: CONSIDERATION_AMOUNT,
                        allMatched: true
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there are three sell orders submitted the day before and it is matched, rejected, cancelled accordingly", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
          await buildOrder({
            status: "Rejected",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Rejected",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
          await buildOrder({
            status: "Cancelled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Cancelled",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?pageSize=1&page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: SUBMISSION_DATE.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 0,
                        buyConsideration: 0,
                        sellOrders: 3,
                        sellConsideration: CONSIDERATION_AMOUNT * 3,
                        allMatched: false,
                        allRejected: false,
                        allInTerminalState: true
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there is one sell order submitted the day before and it is not matched", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();

        const QUANTITY = 1;
        const ASSET_PRICE = 500;

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1, { price: ASSET_PRICE });

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: undefined,
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: SUBMISSION_DATE.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 0,
                        buyConsideration: 0,
                        sellOrders: 1,
                        sellConsideration: QUANTITY * ASSET_PRICE, // When the order is not matched, we estimate the amount from QUANTITY * ASSET_PRICE
                        allMatched: false
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there is one sell order and one buy order submitted one and two days ago respectively", () => {
        const ONE_DAY_AGO = DateUtil.getDateAfterNdays(DateUtil.getFirstDayOfLastMonth(), 1);
        const TWO_DAYS_AGO = DateUtil.getFirstDayOfLastMonth();

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: ONE_DAY_AGO
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: TWO_DAYS_AGO
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: ONE_DAY_AGO.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 0,
                        buyConsideration: 0,
                        sellOrders: 1,
                        sellConsideration: CONSIDERATION_AMOUNT,
                        allMatched: true
                      }
                    }
                  },
                  {
                    date: TWO_DAYS_AGO.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 1,
                        buyConsideration: CONSIDERATION_AMOUNT,
                        sellOrders: 0,
                        sellConsideration: 0,
                        allMatched: true
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there are two sell orders and two buy orders all submitted the day before for the same ETF", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();

        const QUANTITY = 1;
        const ASSET_PRICE = 10;

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1, { price: ASSET_PRICE });

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await Promise.all([
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched",
                  submittedAt: SUBMISSION_DATE
                }
              },
              consideration: {
                amount: CONSIDERATION_AMOUNT * 100, // stored in cents
                currency: "GBP"
              },
              side: "Sell",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_uk"].isin
            }),
            await buildOrder({
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Pending",
                  submittedAt: SUBMISSION_DATE
                }
              },
              consideration: undefined,
              quantity: QUANTITY,
              side: "Sell",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_uk"].isin
            }),
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched",
                  submittedAt: SUBMISSION_DATE
                }
              },
              consideration: {
                amount: CONSIDERATION_AMOUNT * 100, // stored in cents
                currency: "GBP"
              },
              side: "Buy",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_uk"].isin
            }),
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched",
                  submittedAt: SUBMISSION_DATE
                }
              },
              consideration: {
                amount: 2 * CONSIDERATION_AMOUNT * 100, // stored in cents
                currency: "GBP"
              },
              side: "Buy",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_uk"].isin
            })
          ]);
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?page=1")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: SUBMISSION_DATE.toISOString().substr(0, 10),
                    lines: {
                      equities_uk: {
                        buyOrders: 2,
                        buyConsideration: 3 * CONSIDERATION_AMOUNT, // One buy order is for CONSIDERATION_AMOUNT and the other for 2 * CONSIDERATION_AMOUNT
                        sellOrders: 2,
                        sellConsideration: CONSIDERATION_AMOUNT + QUANTITY * ASSET_PRICE,
                        allMatched: false // One of the sell orders was Pending
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });

      describe("when there is one buy order submitted the day before and a different date is passed as a query parameter", () => {
        const SUBMISSION_DATE = DateUtil.getFirstDayOfLastMonth();

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?date=2022-07-15")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: []
              })
            )
          );
        });
      });

      describe("when there is one buy order submitted the day before and that date is passed as a query parameter", () => {
        const SUBMISSION_DATE = new Date("2022-07-16T11:00:00Z");

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: SUBMISSION_DATE
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with given date's order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics?date=2022-07-16")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                analytics: [
                  {
                    date: "2022-07-16",
                    lines: {
                      equities_uk: {
                        buyOrders: 1,
                        buyConsideration: CONSIDERATION_AMOUNT,
                        sellOrders: 0,
                        sellConsideration: 0,
                        allMatched: false
                      }
                    }
                  }
                ]
              })
            )
          );
        });
      });
    });

    describe("POST /orders/analytics/unsubmitted", () => {
      describe("when there are no unsubmitted orders", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          // We build an order that is already submitted and therefore should not be returned.
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is an unsubmitted order that belongs to a transaction pending deposit", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id, status: "PendingDeposit" });
          await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is an unsubmitted order that belongs to a cancelled transaction", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id, status: "Cancelled" });
          await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is an unsubmitted order that belongs to a rejected transaction", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id, status: "Rejected" });
          await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is an unsubmitted order that belongs to a PendingGift transaction", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id, status: "PendingGift" });
          await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is an unsubmitted order that belongs to a DepositFailed transaction", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id, status: "DepositFailed" });
          await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is an unsubmitted order that is cancelled and belongs to a Pending transaction", () => {
        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({ owner: user.id, status: "Pending" });
          await buildOrder({
            status: "Cancelled",
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is a reward that has no deposit", () => {
        const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();

        beforeEach(async () => {
          const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          await buildPortfolio({
            owner: referrer.id,
            providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
          });
          await buildPortfolio({
            owner: referral.id,
            providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
          });
          await buildReward({
            referrer: referrer.id,
            referral: referral.id,
            targetUser: referral.id,
            consideration: {
              amount: 1,
              orderAmount: 1,
              bonusAmount: 1,
              currency: "GBP"
            }
          });
        });

        it("should return an empty 200", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toEqual(JSON.parse(JSON.stringify({ lines: {} })));
        });
      });

      describe("when there is one unsubmitted sell order that belongs to a savings top-up transaction", () => {
        const ASSET_PRICE = 500;
        const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();

        beforeEach(async () => {
          const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          await buildPortfolio({
            owner: referrer.id,
            providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
          });
          await buildPortfolio({
            owner: referral.id,
            providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
          });

          await buildHoldingDTO(true, "equities_uk", 1, { price: ASSET_PRICE });

          await buildSavingsTopup({
            owner: user.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT,
              currency: "GBP"
            }
          });
        });

        it("should return 200 without the order", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject({});
        });
      });

      describe("when there is one unsubmitted sell order and a pending reward", () => {
        const QUANTITY = 1;
        const ASSET_PRICE = 500;
        const REWARD_AMOUNT = 1000; // £10
        const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();

        beforeEach(async () => {
          const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          await buildPortfolio({
            owner: referrer.id,
            providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
          });
          await buildPortfolio({
            owner: referral.id,
            providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
          });
          await buildReward({
            referrer: referrer.id,
            referral: referral.id,
            targetUser: referral.id,
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            },
            asset: "equities_uk",
            consideration: {
              amount: REWARD_AMOUNT,
              orderAmount: REWARD_AMOUNT,
              bonusAmount: REWARD_AMOUNT,
              currency: "GBP"
            }
          });

          await buildHoldingDTO(true, "equities_uk", 1, { price: ASSET_PRICE });

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            consideration: undefined,
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with the two orders", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                lines: {
                  equities_uk: {
                    buyOrders: 1,
                    buyConsideration: REWARD_AMOUNT / 100,
                    sellOrders: 1,
                    sellConsideration: QUANTITY * ASSET_PRICE // When the order is not matched, we estimate the amount from QUANTITY * ASSET_PRICE
                  }
                }
              })
            )
          );
        });
      });

      describe("when there is one unsubmitted sell order and one unsubmitted buy order for the same ETFs", () => {
        const QUANTITY = 1;
        const ASSET_PRICE = 10;

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1, { price: ASSET_PRICE });

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await buildOrder({
            consideration: undefined,
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
          await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
        });

        it("should return 200 with both orders", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                lines: {
                  equities_uk: {
                    buyOrders: 1,
                    buyConsideration: CONSIDERATION_AMOUNT,
                    sellOrders: 1,
                    sellConsideration: QUANTITY * ASSET_PRICE
                  }
                }
              })
            )
          );
        });
      });

      describe("when there are two unsubmitted sell orders and two unsubmitted buy orders for different ETFs", () => {
        const QUANTITY = 1;
        const ASSET_PRICE = 10;

        beforeEach(async () => {
          await buildHoldingDTO(true, "equities_uk", 1, { price: ASSET_PRICE });
          await buildHoldingDTO(true, "equities_eu", 1, { price: ASSET_PRICE * 2 });

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          await Promise.all([
            await buildOrder({
              consideration: undefined,
              quantity: QUANTITY,
              side: "Sell",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_uk"].isin
            }),
            await buildOrder({
              consideration: undefined,
              quantity: QUANTITY,
              side: "Sell",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_eu"].isin
            }),
            await buildOrder({
              consideration: {
                amount: CONSIDERATION_AMOUNT * 100, // stored in cents
                currency: "GBP"
              },
              side: "Buy",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_uk"].isin
            }),
            await buildOrder({
              consideration: {
                amount: 2 * CONSIDERATION_AMOUNT * 100, // stored in cents
                currency: "GBP"
              },
              side: "Buy",
              transaction: assetTransaction.id,
              isin: ASSET_CONFIG["equities_eu"].isin
            })
          ]);
        });

        it("should return 200 with all order analytics", async () => {
          const response = await request(app)
            .get("/api/admin/m2m/orders/analytics/unsubmitted")
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            JSON.parse(
              JSON.stringify({
                lines: {
                  equities_uk: {
                    buyOrders: 1,
                    buyConsideration: CONSIDERATION_AMOUNT, // One buy order is for CONSIDERATION_AMOUNT and the other for 2 * CONSIDERATION_AMOUNT
                    sellOrders: 1,
                    sellConsideration: QUANTITY * ASSET_PRICE
                  },
                  equities_eu: {
                    buyOrders: 1,
                    buyConsideration: 2 * CONSIDERATION_AMOUNT,
                    sellOrders: 1,
                    sellConsideration: 2 * QUANTITY * ASSET_PRICE
                  }
                }
              })
            )
          );
        });
      });
    });

    describe("POST /orders/create-wealthkernel", () => {
      let order: OrderDocument;

      describe("when we have a single order that already has a WK ID", () => {
        it("should return 204, not hit Wealthkernel for any operations and not edit our order", async () => {
          jest.spyOn(WealthkernelService.UKInstance, "createOrder");

          const assetTransaction = await buildAssetTransaction({ owner: user.id });
          order = await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder?.providers?.wealthkernel).toEqual(order.providers.wealthkernel);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when asset transaction is PendingDeposit", () => {
        it("should return 204, not hit Wealthkernel for any operations and not edit our order", async () => {
          jest.spyOn(WealthkernelService.UKInstance, "createOrder");

          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({
            status: "PendingDeposit",
            owner: user.id
          });
          const order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder?.providers?.wealthkernel).toEqual(order.providers?.wealthkernel);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when asset transaction is Cancelled", () => {
        it("should return 204, not hit Wealthkernel for any operations and not edit our order", async () => {
          jest.spyOn(WealthkernelService.UKInstance, "createOrder");

          await buildHoldingDTO(true, "equities_uk", 1);

          const assetTransaction = await buildAssetTransaction({
            status: "Cancelled",
            owner: user.id
          });
          const order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, //stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder?.providers?.wealthkernel).toEqual(order.providers?.wealthkernel);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when a savings topup transaction is internally filled but it's still pending", () => {
        it("should return 204, and not create any new orders", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T10:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false);

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsTopup = await buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: CONSIDERATION_AMOUNT * 100,
                currency: "GBP"
              }
            },
            {
              status: "InternallyFilled"
            }
          );
          const order = savingsTopup.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when a savings withdrawal transaction is internally filled but it's still pending", () => {
        it("should return 204, and not create any new orders", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T10:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false);

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsWithdrawal = await buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: CONSIDERATION_AMOUNT * 100,
                currency: "GBP"
              }
            },
            {
              status: "InternallyFilled"
            }
          );
          const order = savingsWithdrawal.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a buy order that does not have a WK ID but ETF line is paused", () => {
        it("should return 204 and not create a WK order for the paused line", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

          await buildInvestmentProduct(true, { assetId: "equities_uk", buyLine: { active: false } });
          await buildInvestmentProduct(true, { assetId: "equities_us", buyLine: { active: true } });

          const orderForPausedLine = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, //stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
          order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, //stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_us"].isin
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const updatedOrderForActiveLine = (await Order.findById(order.id)) as OrderDocument;
          expect(updatedOrderForActiveLine?.providers?.wealthkernel?.id).toEqual(WEALTHKERNEL_ID);

          const updatedOrderForPausedLine = (await Order.findById(orderForPausedLine.id)) as OrderDocument;
          expect(updatedOrderForPausedLine?.providers?.wealthkernel).toEqual(undefined);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(1);
        });
      });

      describe("when we have a sell order that does not have a WK ID but ETF line is paused", () => {
        it("should return 204 and not create a WK order for the paused line", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

          await buildInvestmentProduct(true, { assetId: "equities_uk", sellLine: { active: false } });
          await buildInvestmentProduct(true, { assetId: "equities_us", sellLine: { active: true } });

          const orderForPausedLine = await buildOrder({
            quantity: 1,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });
          order = await buildOrder({
            quantity: 1,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_us"].isin
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const updatedOrderForActiveLine = (await Order.findById(order.id)) as OrderDocument;
          expect(updatedOrderForActiveLine?.providers?.wealthkernel?.id).toEqual(WEALTHKERNEL_ID);

          const updatedOrderForPausedLine = (await Order.findById(orderForPausedLine.id)) as OrderDocument;
          expect(updatedOrderForPausedLine?.providers?.wealthkernel).toEqual(undefined);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(1);
        });
      });

      describe("when we have a sell order that does not have a WK ID and ETF line is paused but it belongs to a charge transaction", () => {
        it("should return 204 and create a WK order for the paused line", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          const subscription = await buildSubscription({
            category: "FeeBasedSubscription",
            active: true,
            price: "free_monthly",
            owner: user.id
          });

          const chargeTransaction = await buildChargeTransaction({
            owner: user.id,
            subscription: subscription.id
          });

          await buildInvestmentProduct(true, { assetId: "equities_uk", activeSell: false });

          const orderForPausedLine = await buildOrder({
            quantity: 1,
            side: "Sell",
            transaction: chargeTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const updatedOrderForPausedLine = (await Order.findById(orderForPausedLine.id)) as OrderDocument;
          expect(updatedOrderForPausedLine?.providers?.wealthkernel?.id).toEqual(WEALTHKERNEL_ID);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(1);
        });
      });

      describe("when we have a single buy ETF order that does not have a WK ID and we are within WK order submission hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_uk" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              consideration: expect.objectContaining({
                amount: CONSIDERATION_AMOUNT * 100,
                amountSubmitted: CONSIDERATION_AMOUNT * 100
              }),
              providers: expect.objectContaining({
                wealthkernel: expect.objectContaining({
                  id: WEALTHKERNEL_ID,
                  status: "Pending",
                  submittedAt: TODAY
                })
              }),
              isSubmittedToBroker: true
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              consideration: {
                currency: CurrencyEnum.GBP,
                amount: (order.consideration?.amount as number) / 100
              },
              aggregate: true
            }),
            order.id
          );
        });
      });

      describe("when we have a single buy ETF order that does not have a WK ID and we are outside WK order submission hours", () => {
        it("should return 204, without creating an order in WK and with no change in order document", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:10:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_uk" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          });

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              isSubmittedToBroker: false
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when we have a single buy stock order created 20mins ago that does not have a WK ID and we are within WK market hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T15:00:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
            createdAt: new Date("2022-08-31T14:40:00Z") // created 20 mins ago
          });

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              consideration: expect.objectContaining({
                amount: CONSIDERATION_AMOUNT * 100,
                amountSubmitted: CONSIDERATION_AMOUNT * 100
              }),
              providers: expect.objectContaining({
                wealthkernel: expect.objectContaining({
                  id: WEALTHKERNEL_ID,
                  status: "Pending",
                  submittedAt: TODAY
                })
              }),
              isSubmittedToBroker: true
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              consideration: {
                currency: CurrencyEnum.GBP,
                amount: (order.consideration?.amount as number) / 100
              },
              aggregate: false
            }),
            order.id
          );
        });
      });

      describe("when we have a single buy stock order created 5mins ago that does not have a WK ID and we are within WK market hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T15:00:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
            createdAt: new Date("2022-08-31T14:55:00Z") // created 5 mins ago
          });

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              providers: undefined,
              isSubmittedToBroker: false
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single buy stock order that does not have a WK ID and we are outside WK market hours", () => {
        it("should return 204 and not create order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T11:00:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin
          });

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              isSubmittedToBroker: false
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when we have a single sell ETF order that does not have a WK ID and we are within WK order submission hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_uk" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: undefined
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.consideration?.amount).toBe(undefined);
          expect(newOrder?.providers?.wealthkernel).toEqual({
            id: WEALTHKERNEL_ID,
            status: "Pending",
            submittedAt: TODAY
          });
          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              quantity: order.quantity,
              aggregate: true
            }),
            order.id
          );
        });
      });

      describe("when we have a single sell ETF order that does not have a WK ID and we are outside WK order submission hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T12:10:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_uk" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: undefined
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when we have a single sell stock order created 20mins ago that does not have a WK ID and we are within WK market hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T15:00:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
            createdAt: new Date("2022-08-31T14:40:00Z") // 20 minutes ago
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.consideration?.amount).toBe(undefined);
          expect(newOrder?.providers?.wealthkernel).toEqual({
            id: WEALTHKERNEL_ID,
            status: "Pending",
            submittedAt: TODAY
          });
          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              quantity: order.quantity,
              aggregate: false
            }),
            order.id
          );
        });
      });

      describe("when we have a single sell stock order created 5mins ago that does not have a WK ID and we are within WK market hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T15:00:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
            createdAt: new Date("2022-08-31T14:55:00Z") // 5 minutes ago
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder?.providers?.wealthkernel).toBeUndefined();
          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single sell stock order that does not have a WK ID and we are outside WK market hours", () => {
        it("should return 204 and not create order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2025-03-10T20:35:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
            createdAt: DateUtil.getDateOfMinutesAgo(20)
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toEqual(false);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when we have a single sell stock order that does not have a WK ID and we are within WK market hours but it's a US bank holiday", () => {
        it("should return 204 and not create order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          // Birthday of Martin Luther King, Jr.
          const TODAY = new Date("2024-01-15T15:00:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
            createdAt: DateUtil.getDateOfMinutesAgo(20)
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toEqual(false);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when we have a single sell stock order that does not have a WK ID and we are within WK realtime market hours (but we're past our aggregate window) but the estimated sell amount is less than our realtime minimum", () => {
        it("should return 204 and not create order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T15:30:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple", price: 1.01 });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: 1,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            createdAt: DateUtil.getDateOfMinutesAgo(20)
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toEqual(false);
          expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
        });
      });

      describe("when we have a single sell stock order that does not have a WK ID and we are within stock Aggregate market hours and the estimated sell amount is less than our realtime minimum", () => {
        it("should return 204 and create order in WK, with aggregate field set to true", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_apple", price: 1.01 });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: 1,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            createdAt: DateUtil.getDateOfMinutesAgo(20)
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toEqual(true);
          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              quantity: order.quantity,
              aggregate: true
            }),
            order.id
          );
        });
      });

      describe("when we have a single sell stock order that does not have a WK ID and we are within stock Aggregate market hours and the asset is only submitted aggregated in our config", () => {
        it("should return 204 and create order in WK, with aggregate field set to true", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-08-31T12:50:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });

          await buildInvestmentProduct(true, { assetId: "equities_polestar" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });
          order = await buildOrder({
            quantity: QUANTITY,
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_polestar"].isin,
            consideration: undefined,
            createdAt: DateUtil.getDateOfMinutesAgo(20)
          });

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toEqual(true);
          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              quantity: order.quantity,
              aggregate: true
            }),
            order.id
          );
        });
      });

      describe("when we have a single buy savings order that does not have a WK ID and we are within WK order submission hours", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T10:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsTopup.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              consideration: expect.objectContaining({
                amount: CONSIDERATION_AMOUNT * 100,
                amountSubmitted: CONSIDERATION_AMOUNT * 100
              }),
              providers: expect.objectContaining({
                wealthkernel: expect.objectContaining({
                  id: WEALTHKERNEL_ID,
                  status: "Pending",
                  submittedAt: TODAY
                })
              }),
              isSubmittedToBroker: true
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              consideration: {
                currency: CurrencyEnum.GBP,
                amount: (order.consideration?.amount as number) / 100
              },
              aggregate: true
            }),
            order.id
          );
        });
      });

      describe("when we have a single buy savings order that does not have a WK ID and we are within WK order submission hours but it's a UK bank holiday ", () => {
        it("should return 204, without creating an order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2024-03-29T12:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsTopup.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single buy savings order that does not have a WK ID and we are outside WK order submission hours", () => {
        it("should return 204, without creating an order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2024-03-27T12:10:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false);

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsTopup.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single buy savings order that does not have a WK ID but line is paused", () => {
        it("should return 204, without creating an order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2024-04-05T12:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false, {
            buyLine: { active: false, updatedAt: TODAY }
          });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsTopup.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single sell savings order that does not have a WK ID and we are within WK order submission hours ", () => {
        it("should return 204, create order in WK and add the WK ID to our order", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2022-01-31T10:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsWithdrawal = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsWithdrawal.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder).toEqual(
            expect.objectContaining({
              consideration: expect.objectContaining({
                amount: CONSIDERATION_AMOUNT * 100,
                amountSubmitted: CONSIDERATION_AMOUNT * 100
              }),
              providers: expect.objectContaining({
                wealthkernel: expect.objectContaining({
                  id: WEALTHKERNEL_ID,
                  status: "Pending",
                  submittedAt: TODAY
                })
              }),
              isSubmittedToBroker: true
            })
          );

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
            expect.objectContaining({
              isin: order.isin,
              settlementCurrency: CurrencyEnum.GBP,
              side: order.side,
              quantity: (order.consideration?.amount as number) / 100,
              aggregate: true
            }),
            order.id
          );
        });
      });

      describe("when we have a single sell savings order that does not have a WK ID and we are within WK order submission hours but it's a UK bank holiday ", () => {
        it("should return 204, without creating an order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2024-04-01T12:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsWithdrawal = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsWithdrawal.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single sell savings order that does not have a WK ID and we are outside WK order submission hours ", () => {
        it("should return 204, without creating an order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2024-04-02T12:10:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false);

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsWithdrawal = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsWithdrawal.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });

      describe("when we have a single sell savings order that does not have a WK ID but line is paused", () => {
        it("should return 204, without creating an order in WK", async () => {
          const WEALTHKERNEL_ID = "WEALTHKERNEL_ID";

          const TODAY = new Date("2024-04-02T12:20:00Z");
          Date.now = jest.fn(() => TODAY.valueOf());
          jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WEALTHKERNEL_ID });
          await buildSavingsProduct(false, {
            sellLine: { active: false, updatedAt: TODAY }
          });

          const portfolio = await buildPortfolio({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });

          const savingsWithdrawal = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100,
              currency: "GBP"
            }
          });
          const order = savingsWithdrawal.orders[0];

          expect(order.isSubmittedToBroker).toBe(false);

          const response = await request(app)
            .post("/api/admin/m2m/orders/create-wealthkernel")
            .send({})
            .set("Accept", "application/json");
          expect(response.status).toEqual(204);

          const newOrder = (await Order.findById(order.id)) as OrderDocument;

          expect(newOrder.isSubmittedToBroker).toBe(false);

          expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
        });
      });
    });

    describe("GET /orders", () => {
      let validOrder: OrderDocument;

      beforeEach(async () => {
        const transaction = await buildAssetTransaction();
        // not eligible orders
        await Promise.all([
          // different asset id
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: new Date("2022-10-07")
              }
            },
            consideration: undefined,
            quantity: QUANTITY,
            side: "Sell",
            transaction: transaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          }),
          // no wealthkernel submittedAt
          await buildOrder({
            consideration: undefined,
            quantity: QUANTITY,
            side: "Sell",
            transaction: transaction.id,
            isin: ASSET_CONFIG["equities_eu"].isin
          }),
          // different asset id
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: new Date("2022-10-07")
              }
            },
            consideration: {
              amount: CONSIDERATION_AMOUNT * 100, // stored in cents
              currency: "GBP"
            },
            side: "Buy",
            transaction: transaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          })
        ]);

        validOrder = await buildOrder({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending",
              submittedAt: new Date("2022-10-07")
            }
          },
          consideration: {
            amount: 2 * CONSIDERATION_AMOUNT * 100, // stored in cents
            currency: "GBP"
          },
          side: "Buy",
          transaction: transaction.id,
          isin: ASSET_CONFIG["equities_eu"].isin
        });
      });

      it("should return status 400 with proper message for missing 'assetId' param", async () => {
        const response = await request(app)
          .get("/api/admin/m2m/orders?submissionDay=2022-10-07")
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Invalid parameter", message: "Param 'assetId' is required" }
          })
        );
      });

      it("should return status 400 with proper message for invalid 'assetId' param", async () => {
        const invalidAssetId = "garbage";
        const response = await request(app)
          .get(`/api/admin/m2m/orders?assetId=${invalidAssetId}&submissionDay=2022-10-07`)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Invalid parameter", message: `${invalidAssetId} is not a valid asset` }
          })
        );
      });

      it("should return status 400 with proper message for missing 'submissionDay' param", async () => {
        const response = await request(app)
          .get("/api/admin/m2m/orders?assetId=equities_us")
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Invalid parameter", message: "Param 'submissionDay' is required" }
          })
        );
      });

      it("should return status 400 with proper message for invalid 'submissionDay' param", async () => {
        const invalidDate = "garbage";
        const response = await request(app)
          .get(`/api/admin/m2m/orders?assetId=equities_us&submissionDay=${invalidDate}`)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "Invalid value for param 'submissionDay' , should be date with format YYYY-MM-DD"
            }
          })
        );
      });

      it("when all params are valid it should return 200 and contain only matching orders", async () => {
        const response = await request(app)
          .get("/api/admin/m2m/orders?assetId=equities_eu&submissionDay=2022-10-07")
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);
        // reparse object to resolve problem with dates comparison
        expect(JSON.parse(response.text)).toEqual([JSON.parse(JSON.stringify(validOrder))]);
      });
    });
  });
});
