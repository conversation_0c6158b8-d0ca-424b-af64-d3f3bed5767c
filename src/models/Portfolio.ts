import Decimal from "decimal.js";
import mongoose, { Document, Schema } from "mongoose";
import { currenciesConfig, investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { AccountDocument } from "./Account";
import { InvestmentProductDocument } from "./InvestmentProduct";
import { UserDocument } from "./User";
import { PortfolioStatusArray, PortfolioStatusType } from "../external-services/wealthkernelService";
import { TenorEnum } from "../configs/durationConfig";
import { ProviderEnum } from "../configs/providersConfig";
import { IntraDayPortfolioTickerDocument } from "./IntraDayTicker";
import { PartialRecord } from "utils";

const { MainCurrencies } = currenciesConfig;
const { AssetClassArray, InvestmentGeographyArray, InvestmentSectorArray } = investmentUniverseConfig;

/**
 * ENUMS
 */

// refers to where the user first sets the portfolio target allocation
export enum AllocationCreationFlowEnum {
  BUILDER = "builder",
  FROM_SCRATCH = "from_scratch",
  ROBO_ADVISOR = "robo_advisor"
}

/**
 * Mode enum is considered legacy but kept for backwards compatibility.
 */
export enum PortfolioModeEnum {
  REAL = "REAL"
}
export enum PortfolioPopulationFieldsEnum {
  CURRENT_TICKER = "currentTicker",
  OWNER = "owner",
  ACCOUNT = "account",
  HOLDINGS_ASSET_TICKERS = "holdings.asset.currentTicker"
}

/**
 * TYPES
 */
export type CashType = {
  // in pounds, not cents
  available: number;
  settled: number;
  reserved: number;
};
export type HoldingsType = {
  asset?: InvestmentProductDocument;
  assetCommonId: investmentUniverseConfig.AssetType;
  quantity: number;
};
export type GiftedHoldingType = {
  unrestrictedAt: Date;
  createdAt: Date;
  quantity: number;
};
export type InitialHoldingsAllocationType = {
  asset?: InvestmentProductDocument;
  assetCommonId: investmentUniverseConfig.AssetType;
  percentage: number;
};
export type PersonalisationType = {
  assetClasses: investmentUniverseConfig.AssetClassType[];
  geography: investmentUniverseConfig.InvestmentGeographyType;
  risk: number;
  sectors: investmentUniverseConfig.InvestmentSectorType[];
};
export type SavingType = {
  amount: number; // In cents
  currency: currenciesConfig.CurrencyType;
};

export interface PortfolioDTOInterface {
  account?: mongoose.Types.ObjectId;
  allocationCreationFlow?: AllocationCreationFlowEnum;
  // Cash values are in pounds, not cents
  cash?: {
    EUR?: CashType;
    GBP?: CashType;
    USD?: CashType;
  };
  holdings: HoldingsType[];
  giftedHoldings?: Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
  initialHoldingsAllocation: InitialHoldingsAllocationType[];
  mode: PortfolioModeEnum;
  currency: currenciesConfig.MainCurrencyType;
  name?: string;
  owner: mongoose.Types.ObjectId;
  personalisationPreferences?: PersonalisationType;
  activeProviders?: ProviderEnum[]; // Only present in real portfolios
  providers?: {
    wealthkernel?: {
      status: PortfolioStatusType;
      id: string;
    };
  };
  savings?: Map<savingsUniverseConfig.SavingsProductType, SavingType>;
}

export interface PortfolioInterface extends Omit<PortfolioDTOInterface, "account" | "owner"> {
  account?: mongoose.Types.ObjectId | AccountDocument;
  // Cash values are in pounds, not cents
  cash: {
    EUR?: CashType;
    GBP?: CashType;
    USD?: CashType;
  };
  createdAt: Date;
  lastUpdated: Date;
  owner: mongoose.Types.ObjectId | UserDocument;

  // VIRTUALS
  readonly amountAvailableToWithdraw: number;
  readonly currentTicker: IntraDayPortfolioTickerDocument;
  readonly isReal: boolean;
  readonly isSubmittedToBroker?: boolean;
  readonly isTargetAllocationSetup: boolean;
  readonly hasHoldings: boolean;
  readonly unsettledCash: number;
  readonly calculatedPrice?: number;

  readonly getCalculatedPrice?: (investmentProductsWithTickers: InvestmentProductDocument[]) => number;
}

/**
 * DOCUMENTS
 */
export interface PortfolioDocument extends PortfolioInterface, Document {}

export interface PortfolioWithReturnsByTenorDocument extends PortfolioDocument {
  returnsValues: PartialRecord<TenorEnum, number>;
  upByValues: PartialRecord<TenorEnum, number>;
  holdings: (HoldingsType & { sinceBuyReturns?: number })[];
}

/**
 * SCHEMA
 */
const allocationPercentageSchema: Schema = new mongoose.Schema(
  {
    assetCommonId: String,
    percentage: Number
  },
  { _id: false, toJSON: { virtuals: true }, toObject: { virtuals: true } }
);
allocationPercentageSchema.virtual("asset", {
  ref: "InvestmentProduct",
  localField: "assetCommonId",
  foreignField: "commonId",
  justOne: true
});

const cashSchema: Schema = new mongoose.Schema(
  {
    // Settled cash is cash that is received when an order is settled and is now available to be withdrawn.
    // Available cash is cash that is available to be used for new orders, but cannot be withdrawn, until
    // the order is settled.
    available: { type: Number, default: 0 },
    settled: { type: Number, default: 0 },
    reserved: { type: Number, default: 0 }
  },
  { _id: false, toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const holdingsSchema: Schema = new mongoose.Schema(
  {
    assetCommonId: String,
    quantity: Number
  },
  { _id: false, toJSON: { virtuals: true }, toObject: { virtuals: true } }
);
holdingsSchema.virtual("asset", {
  ref: "InvestmentProduct",
  localField: "assetCommonId",
  foreignField: "commonId",
  justOne: true
});

const savingSchema = new mongoose.Schema(
  {
    amount: Number,
    currency: {
      type: String,
      enum: currenciesConfig.Currencies
    }
  },
  { _id: false } // Disable _id field for the values inside the Map
);

const portfolioSchema: Schema = new mongoose.Schema(
  {
    account: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Account",
      /**
       * we still need this required function, for backwards compatible test cases
       * because of "VIRTUAL" legacy portfolio mode enum
       */
      required: function () {
        return this?.mode === PortfolioModeEnum.REAL;
      }
    },
    allocationCreationFlow: {
      type: String,
      enum: Object.values(AllocationCreationFlowEnum),
      required: false
    },
    cash: {
      EUR: {
        type: cashSchema,
        default: {}
      },
      GBP: {
        type: cashSchema,
        default: {}
      },
      USD: cashSchema
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    currency: {
      type: String,
      enum: MainCurrencies,
      default: "GBP"
    },
    holdings: [holdingsSchema],
    giftedHoldings: {
      type: Map,
      // Type of values (keys in Mongo maps are always of String type):
      of: [
        {
          quantity: Number,
          unrestrictedAt: Date,
          createdAt: Date
        }
      ],
      default: {}
    },
    initialHoldingsAllocation: [allocationPercentageSchema],
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    mode: {
      type: String,
      /**
       * VIRTUAL enum value is LEGACY
       * we still need it, for backwards compatible test cases
       */
      enum: ["REAL", "VIRTUAL"],
      required: true
    },
    name: String,
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Portfolio owner cannot be empty"],
      index: true
    },
    personalisationPreferences: {
      assetClasses: [{ type: String, enum: AssetClassArray }],
      geography: { type: String, enum: InvestmentGeographyArray },
      risk: Number,
      sectors: [{ type: String, enum: InvestmentSectorArray }]
    },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          status: { type: String, enum: PortfolioStatusArray },
          id: String
        }
      }
    },
    savings: {
      type: Map,
      // Type of values (keys in Mongo maps are always of String type):
      of: savingSchema,
      default: {}
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * INDEXES
 */
portfolioSchema.index({ "providers.wealthkernel.status": 1, "providers.wealthkernel.id": 1 });

/**
 * VIRTUALS
 */

/**
 * @description Returns the amount of cash available to withdraw from the portfolio.
 * The settled cash can be negative if there is cash pending to be settled, and in the
 * meantime, the user has spent all the available cash. In this case we display 0.
 */
portfolioSchema.virtual("amountAvailableToWithdraw").get(function (): number {
  const settledCash = this.cash?.[this.currency]?.settled;
  return settledCash >= 0 ? settledCash : 0;
});

/**
 * @description Returns the latest intra-day ticker of the portfolio.
 */
portfolioSchema.virtual("currentTicker", {
  ref: "IntraDayPortfolioTicker",
  localField: "_id",
  foreignField: "portfolio",
  justOne: true,
  options: { sort: { timestamp: -1 }, limit: 1 }
});

portfolioSchema.virtual("isReal").get(function (): boolean {
  return this.mode === "REAL";
});

portfolioSchema.virtual("wealthkernel").get(function (): any {
  return {
    status: this.providers?.wealthkernel?.status,
    portfolioId: this.providers?.wealthkernel?.id
  };
});

portfolioSchema.virtual("isSubmittedToBroker").get(function (): any {
  const activeProvider = this.activeProviders[0];

  return !!this?.providers?.[activeProvider]?.id;
});

portfolioSchema.virtual("isTargetAllocationSetup").get(function (): boolean {
  return this?.initialHoldingsAllocation?.length > 0;
});

portfolioSchema.virtual("hasHoldings").get(function (): boolean {
  return this?.holdings?.length > 0;
});

/**
 * @description Returns the amount of cash that is pending to be settled.
 */
portfolioSchema.virtual("unsettledCash").get(function (): number {
  const availableCash = this?.cash?.[this.currency]?.available ?? 0;
  const settledCash = this?.cash?.[this.currency]?.settled ?? 0;
  return Decimal.sub(availableCash, settledCash).toNumber();
});

/**
 * @description The real-time calculated portfolio price we're showing users based on their current holdings (in whole
 * currency).
 *
 * This is useful for cases we want to show something more real-time than the intra-day portfolio ticker.
 */
portfolioSchema.virtual("getCalculatedPrice").get(function (): (
  investmentProductsWithTickers: InvestmentProductDocument[]
) => number {
  const document = this as PortfolioDocument;

  return (investmentProductsWithTickers) => {
    const investmentProductsDict = Object.fromEntries(
      investmentProductsWithTickers.map((investmentProduct) => [investmentProduct.commonId, investmentProduct])
    );

    return document.holdings
      .map((holding) => {
        return { quantity: holding.quantity, assetCommonId: holding.assetCommonId };
      })
      .reduce((sum, { quantity, assetCommonId }) => {
        const investmentProduct = investmentProductsDict[assetCommonId];
        return sum.add(Decimal.mul(quantity, investmentProduct.currentTicker.getPrice(this.currency)));
      }, Decimal(0))
      .toDecimalPlaces(2, Decimal.ROUND_DOWN)
      .toNumber();
  };
});

export const Portfolio = mongoose.model<PortfolioDocument>("Portfolio", portfolioSchema);
