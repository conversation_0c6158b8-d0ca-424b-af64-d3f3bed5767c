import { countriesConfig } from "@wealthyhood/shared-configs";
import mongoose, { Document } from "mongoose";
import { AmlScreeningResultEnum } from "../configs/riskAssessmentConfig";
import {
  EmploymentStatusArray,
  EmploymentStatusType,
  SourceOfWealthArray,
  SourceOfWealthType
} from "../external-services/wealthkernelService";
import { UserDocument } from "./User";
import { getRiskScoreClassification } from "../utils/riskAssessmentUtil";

export enum RiskScoreClassificationEnum {
  LowRisk = "Low risk",
  MediumRisk = "Medium risk",
  HighRisk = "High risk",
  Prohibited = "Prohibited"
}

export enum SourceOfFundsEnum {
  LinkedUKBankAccount = "LinkedUKBankAccount",
  LinkedEUBankAccount = "LinkedEUBankAccount",
  LinkedNonEUBankAccount = "LinkedNonEUBankAccount"
}

export enum DueDiligenceClassificationEnum {
  Simplified = "Simplified",
  Standard = "Standard",
  Enhanced = "Enhanced",
  Blocked = "Blocked"
}

export type RiskAssessmentNationality = {
  value: countriesConfig.CountryCodesType;
  score: number;
};

export type RiskAssessmentSourcesOfFunds = {
  value: SourceOfFundsEnum[];
  score: number;
};

export type RiskAssessmentEmploymentStatus = {
  value: EmploymentStatusType;
  score: number;
};

export type RiskAssessmentVolumeOfTransactions = {
  value: number; // stored in pounds
  score: number;
};

export type RiskAssessmentAmlScreening = {
  value: AmlScreeningResultEnum;
  score: number;
};

export type RiskAssessmentSourcesOfWealth = {
  value: SourceOfWealthType[];
  score: number;
};

export interface RiskAssessmentDTOInterface {
  owner: mongoose.Types.ObjectId;
  createdAt: Date;
  nationality: RiskAssessmentNationality;
  sourcesOfFunds: RiskAssessmentSourcesOfFunds;
  employmentStatus: RiskAssessmentEmploymentStatus;
  volumeOfTransactions: RiskAssessmentVolumeOfTransactions;
  amlScreening: RiskAssessmentAmlScreening;
  sourcesOfWealth: RiskAssessmentSourcesOfWealth;
  totalScore: number;
  dueDiligenceClassification?: DueDiligenceClassificationEnum;
}

export interface RiskAssessmentInterface extends Omit<RiskAssessmentDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;

  // virtuals
  readonly classification: RiskScoreClassificationEnum;
}

export interface RiskAssessmentDocument extends RiskAssessmentInterface, Document {}

const riskAssessmentSchema = new mongoose.Schema(
  {
    nationality: {
      value: { type: String, enum: countriesConfig.countryCodesArray, required: true },
      score: { type: Number, required: true }
    },
    sourcesOfFunds: {
      value: { type: [String], enum: Object.values(SourceOfFundsEnum), required: true },
      score: { type: Number, required: true }
    },
    sourcesOfWealth: {
      value: { type: [String], enum: SourceOfWealthArray, required: true },
      score: { type: Number, required: true }
    },
    employmentStatus: {
      value: { type: String, enum: EmploymentStatusArray, required: true },
      score: { type: Number, required: true }
    },
    volumeOfTransactions: {
      value: { type: Number, required: true },
      score: { type: Number, required: true }
    },
    amlScreening: {
      value: { type: String, enum: Object.values(AmlScreeningResultEnum), required: true },
      score: { type: Number, required: true }
    },
    totalScore: { type: Number, required: true },
    dueDiligenceClassification: {
      type: String,
      enum: Object.values(DueDiligenceClassificationEnum),
      required: false
    },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
riskAssessmentSchema.index({ owner: 1, createdAt: -1 });

riskAssessmentSchema.virtual("classification").get(function (): RiskScoreClassificationEnum {
  return getRiskScoreClassification(this.totalScore);
});

export const RiskAssessment = mongoose.model<RiskAssessmentDocument>("RiskAssessment", riskAssessmentSchema);
