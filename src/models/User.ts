import { NotificationSettingsDocument } from "./NotificationSettings";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";
import crypto from "crypto";
import mongoose, { Document, Schema } from "mongoose";
import validator from "validator";
import {
  countriesConfig,
  currenciesConfig,
  entitiesConfig,
  taxResidencyConfig,
  whitelistConfig
} from "@wealthyhood/shared-configs";
import { AccountDocument } from "./Account";
import { AddressDocument } from "./Address";
import { BankAccountDocument } from "./BankAccount";
import { ParticipantDocument } from "./Participant";
import { PortfolioDocument, PortfolioModeEnum } from "./Portfolio";
import { SubscriptionDocument } from "./Subscription";
import {
  EmploymentStatusArray,
  EmploymentStatusType,
  IndustryArray,
  IndustryType,
  PortfolioWrapperTypeEnum,
  SourceOfWealthArray,
  SourceOfWealthType
} from "../external-services/wealthkernelService";
import { LifetimeEnum, ReferralCodeDocument } from "./ReferralCode";
import { UserDataRequestDocument } from "./UserDataRequest";
import DateUtil from "../utils/dateUtil";
import { KycOperationDocument } from "./KycOperation";
import { RiskAssessmentDocument } from "./RiskAssessment";
import UserValidationUtil from "../utils/userValidationUtil";
import { titleCaseString } from "../utils/stringUtil";
import { WalletDocument } from "./Wallet";
import { ProviderEnum } from "../configs/providersConfig";
import { AmlScreeningResultEnum } from "../configs/riskAssessmentConfig";
import ConfigUtil from "../utils/configUtil";
import { AppRatingDocument } from "./AppRating";
import ConfigCatService from "../external-services/configCatService";
import { AccountingClientSegment } from "../types/accounting";

const { MainCurrencies } = currenciesConfig;

export enum UserPopulationFieldsEnum {
  ACCOUNTS = "accounts",
  ADDRESSES = "addresses",
  ONE_TIME_REFERRAL_CODE = "oneTimeReferralCode",
  PORTFOLIOS = "portfolios",
  GIA = "generalInvestmentAccount",
  SUBSCRIPTION = "subscription",
  USER_DATA_REQUESTS = "userDataRequests",
  BANK_ACCOUNTS = "bankAccounts",
  WALLET = "wallet",
  PARTICIPANT = "participant",
  KYC_OPERATION = "kycOperation",
  LATEST_RISK_ASSESSMENT = "latestRiskAssessment",
  LATEST_APP_RATING = "latestAppRating",
  NOTIFICATION_SETTINGS = "notificationSettings"
}

const SWEATCOIN_REFERRER = "<EMAIL>";

export type Auth0ObjectType = {
  id: string; // The primary Auth0 ID of the user (the one used when the user was created)
} & Partial<Record<AuthProviderType, string>>;

const AuthProviderArray = ["email", "apple", "google", "username-password"] as const;
export type AuthProviderType = (typeof AuthProviderArray)[number];

export const PlatformArray = ["android", "ios"] as const;
export type PlatformType = (typeof PlatformArray)[number];
export enum KycStatusEnum {
  PENDING = "pending",
  FAILED = "failed",
  PASSED = "passed"
}
export const KycStatusArray = ["pending", "failed", "passed"] as const;
export type KycStatusType = (typeof KycStatusArray)[number];

const PortfolioConversionStatusArrayConst = ["notStarted", "inProgress", "completed", "fullyWithdrawn"] as const;
export type PortfolioConversionStatusType = (typeof PortfolioConversionStatusArrayConst)[number];
export type TaxResidencyType = {
  countryCode: countriesConfig.CountryCodesType;
  proofType: taxResidencyConfig.IdentifierType;
  value: string;
};

export const W8BenFormStatusArrayConst = ["Pending", "Completed"] as const;
export type W8BenFormStatusType = (typeof W8BenFormStatusArrayConst)[number];

export enum UserTypeEnum {
  ADMIN = "ADMIN",
  INVESTOR = "INVESTOR",
  TEST_ACCOUNT = "TEST_ACCOUNT"
}

export type EmploymentInfoWithIncomeRangeIdType = {
  incomeRangeId: string;
} & EmploymentInfoType;

type EmploymentInfoType = {
  sourcesOfWealth: SourceOfWealthType[];
  industry?: IndustryType;
  annualIncome: AnnualIncomeType;
  employmentStatus: EmploymentStatusType;
};

type AnnualIncomeType = {
  currency: currenciesConfig.MainCurrencyType;
  amount: number;
};

export type EmploymentInfoTypeDTO = {
  sourcesOfWealth: SourceOfWealthType[];
  industry?: IndustryType;
  incomeRangeId: string;
  employmentStatus: EmploymentStatusType;
};

export interface UserInterface {
  auth0: Auth0ObjectType;
  submittedRequiredInfoAt?: Date;
  currency?: currenciesConfig.MainCurrencyType;
  companyEntity?: entitiesConfig.CompanyEntityEnum;
  dateOfBirth?: Date;
  deletionFeedback?: string;
  deviceTokens: Partial<Record<PlatformType, string>>;
  email: string;
  emailDisposable: boolean;
  emailVerified: boolean;
  hasAcceptedTerms: boolean;
  hasSeenBilling: boolean;
  firstName?: string;
  isUKTaxResident: boolean;
  img: string;
  initialInvestment: number;
  kycStatus: KycStatusType;
  kycFailedAt: Date;
  lastName?: string;
  lastLogin: Date;
  nationalities: countriesConfig.CountryCodesType[];
  role: UserTypeEnum[];
  passports: string[];
  monthlyInvestment: number;
  portfolioConversionStatus: PortfolioConversionStatusType;
  referredByEmail: string;
  residencyCountry?: countriesConfig.CountryCodesType;
  taxResidency: TaxResidencyType;
  viewedWelcomePage: boolean;
  viewedKYCSuccessPage: boolean;
  isPotentiallyDuplicateAccount: boolean;
  lastLoginPlatform: PlatformType;
  canSendGiftUntil: Date;
  canUnlockFreeShare?: boolean;
  canReceiveCashback?: boolean;
  viewedReferralCodeScreen: boolean;
  viewedWealthybitesScreen: boolean;
  joinedWaitingListAt?: Date;
  activeProviders?: ProviderEnum[]; // Not present before users submits residency country
  w8BenForm?: {
    activeProviders: ProviderEnum[];
    completedAt?: Date;
    providers?: {
      wealthkernel?: {
        id: string;
        status: W8BenFormStatusType;
      };
    };
  };
  providers?: {
    wealthkernel?: {
      id: string;
    };
    gocardless?: {
      id: string;
    };
    stripe?: {
      id: string;
    };
    saltedge?: {
      id: string;
    };
    sumsub?: {
      id: string;
    };
    complyAdvantage?: {
      id: string;
    };
    jumio?: {
      id: string;
    };
  };
  employmentInfo: EmploymentInfoWithIncomeRangeIdType;
  isPassportVerified: boolean;
  amlScreening: AmlScreeningResultEnum;
  skippedPortfolioCreation?: boolean;
  joinedWithCode?: mongoose.Types.ObjectId | ReferralCodeDocument;
  usedWhitelistCode?: boolean;

  // virtual getters
  readonly accounts: AccountDocument[];
  readonly addresses: AddressDocument[];
  readonly addressSubmitted: boolean;
  readonly passportSubmitted: boolean;
  readonly taxResidencySubmitted: boolean;
  readonly bankLinked: boolean;
  readonly bankAccounts: BankAccountDocument[];
  readonly portfolios: PortfolioDocument[];
  readonly generalInvestmentAccount: AccountDocument;
  readonly subscription: SubscriptionDocument;
  readonly userDataRequests: UserDataRequestDocument[];
  readonly submittedRequiredInfo: boolean;
  readonly isConvertingPortfolio: boolean;
  readonly hasConvertedPortfolio: boolean;
  readonly participant: ParticipantDocument;
  readonly hasSubscription: boolean;
  readonly hasDisassociationRequest: boolean;
  readonly hasClosedAccount: boolean;
  readonly hasSuspendedAccount: boolean;
  readonly intercomUserIdHashAndroid: string;
  readonly intercomUserIdHashIos: string;
  readonly intercomUserIdHashWeb: string;
  readonly hasFailedKyc: boolean;
  readonly hasPassedKyc: boolean;
  readonly isVerified: boolean;
  readonly oneTimeReferralCode: ReferralCodeDocument | mongoose.Types.ObjectId;
  readonly wallet: WalletDocument | mongoose.Types.ObjectId;
  // TODO: This virtual is temporary until we migrate to the new fields on the mobile clients
  readonly kycPassed: boolean;
  readonly isManuallyKycPassed: boolean;
  readonly employmentInfoSubmitted: boolean;
  readonly shouldShowKYCSuccessPage: boolean;
  readonly shouldDisplayReferralCodeScreen: boolean;
  readonly isSubmittedToBroker?: boolean;
  readonly isSubmittedToKycProvider?: boolean;
  readonly isDeleted?: boolean;
  readonly isCashbackFeatureEnabled: boolean;
  readonly isRoboAdvisorEnabled: boolean;
  readonly hasDeviceToken: boolean;
  readonly androidDeviceToken?: string;
  readonly iosDeviceToken?: string;
  readonly hasJoinedWaitingList: boolean;
  readonly isEuropeanEntity: boolean;

  readonly isGivenAccess: boolean;

  // For sweatcoin deal
  readonly isSweatcoinReferred: boolean;
  readonly kycOperation?: KycOperationDocument;
  readonly hasCompletedKycJourney: boolean;
  readonly fullName: string;
  readonly latestRiskAssessment?: RiskAssessmentDocument;
  readonly latestAppRating?: AppRatingDocument;
  readonly notificationSettings: NotificationSettingsDocument;

  /**
   * Returns true if user has whitelist access OR is a referred by any user and can access the waitlist promo.
   */
  readonly isEuWhitelisted: boolean;
  /**
   * Returns true if user has explicitly used a whitelist code or has a whitelisted email.
   * This is the stricter whitelist check used for reward eligibility.
   */
  readonly usedEuWhitelistCodeOrEmail: boolean;

  readonly hideWaitlistPromo: boolean;

  // Added by mongoose timestamps
  readonly createdAt: Date;
  readonly updatedAt: Date;

  // Feature flags
  readonly isPriceMomentumSentimentEnabled: boolean;
  readonly isRealtimeETFExecutionEnabled: boolean;

  // Accounting reporting
  readonly accountingClientSegment: AccountingClientSegment;
}

export interface UserDocument extends UserInterface, Document {}

const userSchema: Schema = new mongoose.Schema(
  {
    submittedRequiredInfoAt: Date, // introduced to avoid user verification race conditions between ongoing-cron and realtime flow
    dateOfBirth: Date,
    deletionFeedback: String,
    deviceTokens: {
      _id: false,
      type: {
        android: { type: String },
        ios: { type: String }
      }
    },
    email: {
      type: String,
      unique: true,
      lowercase: true,
      trim: true,
      validate: [validator.isEmail, "Invalid Email Address"],
      required: [true, "Please supply an email address"]
    },
    emailDisposable: { type: Boolean, default: false },
    emailVerified: { type: Boolean, default: false },
    hasAcceptedTerms: { type: Boolean, default: false },
    hasSeenBilling: { type: Boolean, default: false },
    auth0: {
      id: { type: String, required: true },
      ...Object.fromEntries(
        AuthProviderArray.map((provider) => {
          return [provider, { type: String, required: false }];
        })
      )
    },
    isUKTaxResident: { type: Boolean, default: false },
    img: { type: String, default: "" },
    initialInvestment: {
      type: Number,
      default: 5000
    },
    firstName: { type: String, trim: true },
    kycStatus: { type: String, enum: KycStatusArray, default: KycStatusEnum.PENDING },
    kycFailedAt: Date,
    lastName: { type: String, trim: true },
    lastLogin: Date,
    nationalities: [{ type: String, enum: countriesConfig.countryCodesArray }],
    currency: {
      type: String,
      enum: MainCurrencies
    },
    companyEntity: {
      type: String,
      enum: entitiesConfig.CompanyEntityEnum
    },
    monthlyInvestment: {
      type: Number,
      default: 250
    },
    passports: [String],
    portfolioConversionStatus: {
      type: String,
      default: "notStarted",
      enum: PortfolioConversionStatusArrayConst,
      index: true
    },
    referredByEmail: String,
    residencyCountry: { type: String, enum: countriesConfig.countryCodesArray },
    role: [
      {
        type: String,
        default: "INVESTOR",
        enum: ["ADMIN", "INVESTOR", "TEST_ACCOUNT"]
      }
    ],
    taxResidency: {
      countryCode: { type: String, enum: countriesConfig.countryCodesArray },
      proofType: { type: String, enum: taxResidencyConfig.IdentifierArray },
      value: String
    },
    viewedWelcomePage: { type: Boolean, default: false },
    viewedKYCSuccessPage: { type: Boolean, default: false },
    viewedReferralCodeScreen: { type: Boolean, default: false },
    viewedWealthybitesScreen: { type: Boolean, default: false },
    joinedWaitingListAt: Date,
    activeProviders: { type: Object.values(ProviderEnum) },
    w8BenForm: {
      activeProviders: { type: Object.values(ProviderEnum) },
      completedAt: Date,
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: W8BenFormStatusArrayConst }
          }
        }
      }
    },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.GOCARDLESS]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.SALTEDGE]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.STRIPE]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.SUMSUB]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.JUMIO]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.COMPLY_ADVANTAGE]: {
          _id: false,
          id: { type: String }
        }
      }
    },
    lastLoginPlatform: {
      type: String,
      enum: PlatformArray
    },
    canSendGiftUntil: Date,
    isPotentiallyDuplicateAccount: Boolean,
    employmentInfo: {
      incomeRangeId: String,
      industry: { type: String, enum: IndustryArray },
      sourcesOfWealth: {
        type: [String],
        enum: SourceOfWealthArray
      },
      employmentStatus: { type: String, enum: EmploymentStatusArray },
      annualIncome: {
        type: {
          currency: {
            type: String,
            enum: MainCurrencies,
            default: "GBP"
          },
          amount: { type: Number },
          amountSubmitted: { type: Number },
          originalAmount: { type: Number }
        },
        _id: false,
        required: false
      }
    },
    isPassportVerified: Boolean,
    amlScreening: {
      type: String,
      default: AmlScreeningResultEnum.NoHit,
      enum: Object.values(AmlScreeningResultEnum)
    },
    skippedPortfolioCreation: { type: Boolean, default: false },
    usedWhitelistCode: { type: Boolean, default: false },
    joinedWithCode: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ReferralCode"
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

/**
 * VIRTUALS
 */
userSchema.virtual("accounts", {
  ref: "Account",
  localField: "_id",
  foreignField: "owner",
  justOne: false
});
userSchema.virtual("addresses", {
  ref: "Address",
  localField: "_id",
  foreignField: "owner",
  justOne: false
});
userSchema.virtual("oneTimeReferralCode", {
  ref: "ReferralCode",
  localField: "_id",
  foreignField: "owner",
  justOne: true,
  options: {
    match: { active: true, lifetime: LifetimeEnum.EXPIRING },
    sort: { createdAt: -1 },
    limit: 1
  }
});
userSchema.virtual("wallet", {
  ref: "Wallet",
  localField: "_id",
  foreignField: "owner",
  justOne: true,
  options: {
    sort: { createdAt: -1 },
    limit: 1
  }
});
userSchema.virtual("portfolios", {
  ref: "Portfolio",
  localField: "_id",
  foreignField: "owner",
  justOne: false,
  options: {
    match: { mode: PortfolioModeEnum.REAL }
  }
});
userSchema.virtual("generalInvestmentAccount", {
  ref: "Account",
  localField: "_id",
  foreignField: "owner",
  justOne: true,
  options: {
    match: { wrapperType: PortfolioWrapperTypeEnum.GIA }
  }
});
userSchema.virtual("subscription", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "owner",
  justOne: true
});
userSchema.virtual("userDataRequests", {
  ref: "UserDataRequest",
  localField: "_id",
  foreignField: "owner",
  justOne: false
});
userSchema.virtual("bankAccounts", {
  ref: "BankAccount",
  localField: "_id",
  foreignField: "owner",
  justOne: false
});
userSchema.virtual("participant", {
  ref: "Participant",
  localField: "email",
  foreignField: "email",
  justOne: true
});
userSchema.virtual("kycOperation", {
  ref: "KycOperation",
  localField: "_id",
  foreignField: "owner",
  justOne: true,
  perDocumentLimit: 1,
  options: {
    sort: { createdAt: -1 }
  }
});
userSchema.virtual("latestRiskAssessment", {
  ref: "RiskAssessment",
  localField: "_id",
  foreignField: "owner",
  justOne: true,
  perDocumentLimit: 1,
  options: {
    sort: { createdAt: -1 }
  }
});
userSchema.virtual("latestAppRating", {
  ref: "AppRating",
  localField: "_id",
  foreignField: "owner",
  justOne: true,
  perDocumentLimit: 1,
  options: {
    sort: { createdAt: -1 }
  }
});

userSchema.virtual("notificationSettings", {
  ref: "NotificationSettings",
  localField: "_id",
  foreignField: "owner",
  justOne: true
});

userSchema.virtual("addressSubmitted").get(function (): boolean {
  if (!(this.addresses?.length > 0)) {
    return false;
  }
  const { line1, city, countryCode, postalCode } = this.addresses[0];
  return Boolean(line1 && city && countryCode && postalCode);
});
userSchema.virtual("passportSubmitted").get(function (): boolean {
  return Boolean(this.firstName && this.lastName && this.dateOfBirth && this.nationalities?.length > 0);
});
userSchema.virtual("taxResidencySubmitted").get(function (): boolean {
  if (!this.taxResidency) {
    return false;
  }
  const { countryCode, proofType, value } = this.taxResidency;
  return Boolean(countryCode && proofType && value);
});
userSchema.virtual("employmentInfoSubmitted").get(function (): boolean {
  return UserValidationUtil.validateEmploymentInfo(this.employmentInfo);
});
userSchema.virtual("bankLinked").get(function (): boolean {
  return this.bankAccounts && this.bankAccounts.length > 0;
});
userSchema.virtual("hasSubscription").get(function (): boolean {
  return this.subscription !== null && this.subscription !== undefined;
});
userSchema.virtual("shouldShowStatements").get(function (): boolean {
  return this.hasConvertedPortfolio;
});
userSchema.virtual("hasDisassociationRequest").get(function (): boolean {
  return this.userDataRequests?.some(
    (dataReq: UserDataRequestDocument) => dataReq.requestType == "disassociation"
  );
});
userSchema.virtual("hasClosedAccount").get(function (): boolean {
  return this.accounts?.some((account: AccountDocument) => account.providers?.wealthkernel?.status == "Closed");
});
userSchema.virtual("hasSuspendedAccount").get(function (): boolean {
  return this.accounts?.some((account: AccountDocument) => account.providers?.wealthkernel?.status == "Suspended");
});
userSchema.virtual("hasFailedKyc").get(function (): boolean {
  return (this.kycStatus as KycStatusType) === "failed";
});
userSchema.virtual("hasPassedKyc").get(function (): boolean {
  return (this.kycStatus as KycStatusType) === "passed";
});

/**
 * This field makes sure the user is **verified**, meaning they have both KYC passed and they have a portfolio
 * submitted to WK (or other provider based on residency). This allows the user to proceed with transactions.
 *
 * NOTE: Only works with populated portfolios.
 */
userSchema.virtual("isVerified").get(function (): boolean {
  const user = this as UserDocument;

  if (user.portfolios?.[0]?._id) {
    return user.hasPassedKyc && user.portfolios[0].isSubmittedToBroker;
  }
});

// TODO: This virtual is temporary until we migrate to the new fields on the mobile clients
userSchema.virtual("kycPassed").get(function (): boolean {
  return (this.kycStatus as KycStatusType) === "passed";
});
/**
 * NOTE: Because this depends on `isVerified` virtual, portfolios MUST be populated
 */
userSchema.virtual("shouldShowKYCSuccessPage").get(function (): boolean {
  const user = this as UserDocument;
  return Boolean(user.isVerified) && !user.viewedKYCSuccessPage;
});

userSchema.virtual("shouldDisplayReferralCodeScreen").get(function (): boolean {
  const user = this as UserDocument;

  if (user.viewedReferralCodeScreen) return false;
  if (user.referredByEmail) return false;
  if (user.passportSubmitted) return false;

  return true;
});

userSchema.virtual("isSubmittedToBroker").get(function (): boolean {
  if (!this.companyEntity) {
    return false;
  }

  const brokerageProviders = ProviderService.getProviders(this.companyEntity, [ProviderScopeEnum.BROKERAGE]);
  return brokerageProviders.every((provider) => !!this?.providers?.[provider]?.id);
});

userSchema.virtual("isSubmittedToKycProvider").get(function (): boolean {
  if (!this.companyEntity) {
    return false;
  }

  const kycProviders = ProviderService.getProviders(this.companyEntity, [ProviderScopeEnum.KYC]);
  return kycProviders.every((provider) => !!this?.providers?.[provider]?.id);
});

userSchema.virtual("shouldDisplayWealthybitesScreen").get(function (): boolean {
  const user = this as UserDocument;

  return !user.viewedWealthybitesScreen && !user.submittedRequiredInfo; // this screen in showed only on onboarding
});

/**
 * This field is used by the clients to set the user id hash to intercom in order to enable
 * identity verification.
 */
userSchema.virtual("intercomUserIdHashAndroid").get(function (): string {
  const hmac = crypto.createHmac("sha256", process.env.INTERCOM_ANDROID_VERIFICATION_SECRET);
  hmac.update(this.id as string);
  return hmac.digest("hex");
});
userSchema.virtual("intercomUserIdHashIos").get(function (): string {
  const hmac = crypto.createHmac("sha256", process.env.INTERCOM_IOS_VERIFICATION_SECRET);
  hmac.update(this.id as string);
  return hmac.digest("hex");
});
userSchema.virtual("intercomUserIdHashWeb").get(function (): string {
  const hmac = crypto.createHmac("sha256", process.env.INTERCOM_WEB_VERIFICATION_SECRET);
  hmac.update(this.id as string);
  return hmac.digest("hex");
});

/**
 * Indicates whether the user has submitted all the required info that will allow the admin to fill-in the
 * missing fields in order to submit the party to wealthkernel.
 * The required info is:
 * - Passport
 * - Address
 * - Tax residency (skipped or submitted)
 * - Employment info
 *
 * This field is irrelevant to whether the party can be created or whether the user has passed the kyc check.
 */
userSchema.virtual("submittedRequiredInfo").get(function (): boolean {
  const isTaxVerificationRequired = ConfigUtil.getTaxResidencyConfig(this.residencyCountry).required;
  const isTaxResidencySkippedOrSubmitted = isTaxVerificationRequired ? this.taxResidencySubmitted : true;

  return (
    this.passportSubmitted &&
    this.addressSubmitted &&
    isTaxResidencySkippedOrSubmitted &&
    this.employmentInfoSubmitted
  );
});
userSchema.virtual("hasConvertedPortfolio").get(function (): boolean {
  return this.portfolioConversionStatus === "completed";
});
userSchema.virtual("isConvertingPortfolio").get(function (): boolean {
  return this.portfolioConversionStatus === "inProgress";
});
userSchema.virtual("isManuallyKycPassed").get(function (): boolean {
  const tenMinutesAgo = DateUtil.getDateOfMinutesAgo(10);
  return this.kycStatus === KycStatusEnum.PASSED && this.submittedRequiredInfoAt < tenMinutesAgo;
});

userSchema.virtual("isSweatcoinReferred").get(function (): boolean {
  return (this as UserDocument).referredByEmail === SWEATCOIN_REFERRER;
});

userSchema.virtual("isCashbackFeatureEnabled").get(function (): boolean {
  return this.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK;
});

userSchema.virtual("isRoboAdvisorEnabled").get(function (): boolean {
  return this.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE;
});

userSchema.virtual("hasCompletedKycJourney").get(function (): boolean {
  const user = this as UserDocument;
  // Assume 'kyc passed' users have completed ID verification
  if (user.hasPassedKyc) return true;

  const kycOperation = user.kycOperation as KycOperationDocument;
  return kycOperation?.isJourneyCompleted ?? false;
});

userSchema.virtual("isPriceMomentumSentimentEnabled").get(function (): boolean {
  const user = this as UserDocument;

  return (
    user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE ||
    ConfigCatService.isPriceMomentumSentimentEnabled(user.email)
  );
});

userSchema.virtual("isRealtimeETFExecutionEnabled").get(function (): boolean {
  const user = this as UserDocument;

  return user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE;
});

userSchema.virtual("fullName").get(function (): string {
  const user = this as UserDocument;

  if (user.firstName && user.lastName) {
    return titleCaseString(user.firstName) + " " + titleCaseString(user.lastName);
  }
});

userSchema.virtual("isDeleted").get(function (): boolean {
  const user = this as UserDocument;
  return user.email.startsWith("deleted_");
});

userSchema.virtual("hasDeviceToken").get(function (): boolean {
  const user = this as UserDocument;
  return !!(user.deviceTokens?.android || user.deviceTokens?.ios);
});

userSchema.virtual("androidDeviceToken").get(function (): string | undefined {
  const user = this as UserDocument;
  return user.deviceTokens?.android;
});

userSchema.virtual("iosDeviceToken").get(function (): string | undefined {
  const user = this as UserDocument;
  return user.deviceTokens?.ios;
});

/**
 * Indicates whether the user has joined the waiting list
 */
userSchema.virtual("hasJoinedWaitingList").get(function (): boolean {
  return !!this.joinedWaitingListAt;
});

userSchema.virtual("isEuropeanEntity").get(function (): boolean {
  const user = this as UserDocument;
  return user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE;
});

userSchema.virtual("accountingClientSegment").get(function (): AccountingClientSegment {
  const user = this as UserDocument;
  if (user.residencyCountry === "GR") {
    return AccountingClientSegment.DOMESTIC_GR;
  } else if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
    return AccountingClientSegment.EU_EEA_EXCLUDING_GR;
  } else {
    return AccountingClientSegment.REST_OF_WORLD;
  }
});

userSchema.virtual("isEuWhitelisted").get(function (): boolean {
  const user = this as UserDocument;

  const isWhitelisted =
    user.usedEuWhitelistCodeOrEmail || (!!user.referredByEmail && user.residencyCountry === "GR");
  return isWhitelisted;
});

userSchema.virtual("usedEuWhitelistCodeOrEmail").get(function (): boolean {
  const user = this as UserDocument;

  const usedWhitelistCodeOrEmail = whitelistConfig.isEmailWhitelisted(user.email) || user.usedWhitelistCode;
  return usedWhitelistCodeOrEmail;
});

userSchema.virtual("isGivenAccess").get(function (): boolean {
  const user = this as UserDocument;

  if (user.kycPassed) {
    return true;
  }

  return user.residencyCountry === "GB" || user.isEuWhitelisted;
});

export const User = mongoose.model<UserDocument>("User", userSchema);
