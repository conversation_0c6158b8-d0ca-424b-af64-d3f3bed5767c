import mongoose, { Document, Schema } from "mongoose";

export interface StreamLockTokenInterface {
  processedAt: Date;
}

export interface StreamLockTokenDocument extends StreamLockTokenInterface, Document {}

const streamLockTokenSchema: Schema = new mongoose.Schema(
  {
    changeId: {
      type: String,
      required: true,
      unique: true
    },
    processedAt: {
      type: Date,
      default: Date.now,
      expires: 60 * 60 * 24 * 7 // Auto-delete after 7 days (TTL index)
    }
  },
  {
    timestamps: false // We don't need createdAt/updatedAt since we have processedAt
  }
);

export const StreamLockToken = mongoose.model<StreamLockTokenDocument>("StreamLockToken", streamLockTokenSchema);
