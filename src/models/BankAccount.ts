import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { BankAccountStatusArray, BankAccountStatusType } from "../external-services/wealthkernelService";
import { ProviderEnum } from "../configs/providersConfig";
import { MandateDocument } from "./Mandate";
import { banksConfig, currenciesConfig } from "@wealthyhood/shared-configs";
import BanksUtil, { UNKNOWN_LOGO_URL } from "../utils/banksUtil";

const { MainCurrencies } = currenciesConfig;

/**
 * ENUMS
 */
export enum DeactivationReasonEnum {
  BANK_NAME_MISMATCH = "bank-name-mismatch"
}

export enum BankAccountPopulationFieldsEnum {
  OWNER = "owner"
}

export const WealthyhoodBankAccountStatusArray = ["Active", "Pending", "Suspended"] as const;
export type WealthyhoodBankAccountStatusType = (typeof WealthyhoodBankAccountStatusArray)[number];

export interface BankAccountDTOInterface {
  owner: mongoose.Types.ObjectId;
  name: string;
  active: boolean;
  currency: currenciesConfig.MainCurrencyType;
  number?: string;
  sortCode?: string;
  iban?: string;
  bic?: string;
  bankName?: string;
  bankId?: banksConfig.BankType;
  createdAt?: Date;
  activeProviders: ProviderEnum[];
  deactivationReason?: DeactivationReasonEnum;
  providers?: {
    [ProviderEnum.TRUELAYER]?: {
      bankId: string;
    };
    [ProviderEnum.WEALTHKERNEL]?: {
      id: string;
      status?: BankAccountStatusType;
    };
    [ProviderEnum.WEALTHYHOOD]?: {
      status?: WealthyhoodBankAccountStatusType;
    };
    [ProviderEnum.GOCARDLESS_DATA]?: {
      bankId: string;
      id: string;
    };
    [ProviderEnum.GOCARDLESS]?: {
      id: string;
    };
    [ProviderEnum.DEVENGO]?: {
      id: string;
    };
  };
  stagnantEventEmitted?: boolean;
}

export interface BankAccountInterface extends Omit<BankAccountDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;

  // virtuals
  readonly isAvailableForDirectDebit?: boolean;
  readonly displayBankName?: string;
  readonly isProviderPending?: boolean;
  readonly isProviderActive?: boolean;
  readonly bankIconURL?: string;
  readonly supportsEasyTransfer?: boolean;
  readonly displayAccountIdentifier?: string;
  readonly truelayerProviderId?: string;
  readonly mandate?: MandateDocument;
}

export interface BankAccountDocument extends BankAccountInterface, Document {}

const bankAccountSchema = new mongoose.Schema(
  {
    name: String,
    number: String,
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },
    sortCode: String,
    iban: String,
    bic: String,
    bankName: String,
    bankId: String,
    currency: {
      type: String,
      enum: MainCurrencies
    },
    deactivationReason: { type: String, enum: DeactivationReasonEnum },
    active: { type: Boolean, default: true },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.TRUELAYER]: {
          _id: false,
          bankId: { type: String }
        },
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: BankAccountStatusArray }
        },
        [ProviderEnum.WEALTHYHOOD]: {
          _id: false,
          status: { type: String, enum: WealthyhoodBankAccountStatusArray }
        },
        [ProviderEnum.GOCARDLESS_DATA]: {
          _id: false,
          id: { type: String },
          bankId: { type: String }
        },
        [ProviderEnum.GOCARDLESS]: {
          _id: false,
          id: { type: String }
        },
        [ProviderEnum.DEVENGO]: {
          _id: false,
          id: { type: String }
        }
      }
    },
    stagnantEventEmitted: { type: Boolean, default: false }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

// This field could have been renamed to "activeTopUpMandate", but the criteria could
// change in the future for which mandate to fetch, so for now we have it named as
// "mandate" intentionally.
bankAccountSchema.virtual("mandate", {
  ref: "Mandate",
  localField: "_id",
  foreignField: "bankAccount",
  justOne: true,
  options: {
    match: { category: "Top-Up", "providers.wealthkernel.status": "Active" }
  }
});

bankAccountSchema.virtual("truelayerProviderId").get(function (): string {
  return this.providers?.truelayer?.bankId;
});

bankAccountSchema.virtual("isAvailableForDirectDebit").get(function (): boolean {
  const document = this as BankAccountDocument;

  if (document.iban) {
    return !document.iban.startsWith("GB");
  } else return true;
});

bankAccountSchema.virtual("displayAccountIdentifier").get(function (): string {
  const document = this as BankAccountDocument;

  if (document.iban) {
    return document.iban;
  } else if (document.sortCode && document.number) {
    return `${document.sortCode} ${document.number}`;
  }
});

bankAccountSchema.virtual("displayBankName").get(function (): string {
  const document = this as BankAccountDocument;

  if (document.bankId) {
    return banksConfig.BANKS_CONFIG[document.bankId as banksConfig.BankType].name;
  } else if (document.bankName) {
    return document.bankName;
  }
});

bankAccountSchema.virtual("bankIconURL").get(function (): string {
  const document = this as BankAccountDocument;

  if (document.bankId && banksConfig.BANKS_CONFIG[document.bankId as banksConfig.BankType].logo) {
    return banksConfig.BANKS_CONFIG[document.bankId as banksConfig.BankType].logo;
  } else if (document.bic && BanksUtil.getLogoURLFromBIC(document.bic)) {
    return BanksUtil.getLogoURLFromBIC(document.bic);
  } else return UNKNOWN_LOGO_URL;
});

bankAccountSchema.virtual("isProviderActive").get(function (): boolean {
  const document = this as BankAccountDocument;

  if (document.activeProviders.includes(ProviderEnum.WEALTHKERNEL)) {
    return document.providers?.wealthkernel?.status === "Active";
  } else if (document.activeProviders.includes(ProviderEnum.WEALTHYHOOD)) {
    return document.providers?.wealthyhood?.status === "Active";
  }
});

bankAccountSchema.virtual("isProviderPending").get(function (): boolean {
  const document = this as BankAccountDocument;

  if (document.activeProviders.includes(ProviderEnum.WEALTHKERNEL)) {
    return document.providers?.wealthkernel?.status === "Pending";
  } else if (document.activeProviders.includes(ProviderEnum.WEALTHYHOOD)) {
    return document.providers?.wealthyhood?.status === "Pending";
  }
});

bankAccountSchema.virtual("supportsEasyTransfer").get(function (): boolean {
  return false;
});

export const BankAccount = mongoose.model<BankAccountDocument>("BankAccount", bankAccountSchema);
