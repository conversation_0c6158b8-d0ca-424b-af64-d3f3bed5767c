import { cloudflareConfig, entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import CloudflareService, { BucketsEnum, FilesEnum } from "../../external-services/cloudflareService";
import logger from "../../external-services/loggerService";
import { OrderDocument } from "../../models/Order";
import { AssetTransaction } from "../../models/Transaction";
import InvestmentProductService from "../../services/investmentProductService";
import { concatenateUnique } from "../../utils/arrayUtil";
import ConfigUtil from "../../utils/configUtil";
import DateUtil from "../../utils/dateUtil";
import * as InvestmentUniverseUtil from "../../utils/investmentUniverseUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { KvNamespaceKeys } = cloudflareConfig;

/**
 * TYPES
 */
export type TopMoversType = {
  best: {
    asset: investmentUniverseConfig.AssetType;
    returnPercentage: number;
  }[];
  worst: {
    asset: investmentUniverseConfig.AssetType;
    returnPercentage: number;
  }[];
};
type AssetCollectionType = Record<
  investmentUniverseConfig.InvestmentCollectionType,
  investmentUniverseConfig.AssetCollectionConfigType
>;
type EtfSectionType = {
  top: investmentUniverseConfig.AssetType[];
  popularIndex: investmentUniverseConfig.AssetType[];
};

type AssetDiscoveryDataType = {
  topMovers: TopMoversType;
  collections: AssetCollectionType;
  popularAssetsSection: investmentUniverseConfig.AssetType[];
  etfSection: EtfSectionType;
};

/**
 * CONSTANTS
 */
const TOP_MOVERS_LIMIT = 8;
const MOST_TRADED_ASSETS_LIMIT = 12;
const TOP_ETFS: Record<entitiesConfig.CompanyEntityEnum, investmentUniverseConfig.AssetType[]> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: [
    "equities_global_ai",
    "equities_global_tech_blockchain_vaneck",
    "equities_us_biotech_dist",
    "equities_global_tech_electric_vehicles",
    "equities_us_dividend_aristocrats"
  ],
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: [
    "equities_global",
    "equities_us_tech_broad",
    "equities_global_ai",
    "commodities_gold"
  ]
};

const POPULAR_INDEX_ETFS: Record<entitiesConfig.CompanyEntityEnum, investmentUniverseConfig.AssetType[]> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: [
    "equities_us_vanguard_acc",
    "equities_us_tech_broad_nasdaq_100_ishares",
    "equities_global_msci_world_spdr",
    "equities_eu_stoxx_600",
    "commodities_gold"
  ],
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: [
    "equities_us",
    "equities_uk",
    "equities_global_tech_broad_nasdaq_100"
  ]
};

export default class AssetDiscoveryDataCronService {
  private static _isinToCommonIdDict: Record<string, investmentUniverseConfig.AssetType> = Object.fromEntries(
    Object.entries(ASSET_CONFIG).map((assetEntry) => [
      assetEntry[1].isin,
      assetEntry[0] as investmentUniverseConfig.AssetType
    ])
  );

  public static async updateAssetDiscoveryData(): Promise<void> {
    await Promise.all(
      [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK, entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE].map(
        (companyEntity) => AssetDiscoveryDataCronService._updateAssetDiscoveryDataForCompanyEntity(companyEntity)
      )
    );
  }

  private static async _updateAssetDiscoveryDataForCompanyEntity(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Promise<void> {
    const assetDiscoveryData = await AssetDiscoveryDataCronService._getAssetDiscoveryData(companyEntity);
    AssetDiscoveryDataCronService._validateAssetDiscoveryData(assetDiscoveryData);

    await CloudflareService.Instance.updateKeyValuePair(
      AssetDiscoveryDataCronService._getKvAssetDiscoveryDataKey(companyEntity) as cloudflareConfig.KvNamespaceKeys,
      JSON.stringify(assetDiscoveryData)
    );
  }

  private static _getKvAssetDiscoveryDataKey(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): cloudflareConfig.KvNamespaceKeys {
    if (companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK;
    }

    return KvNamespaceKeys.ASSET_DISCOVERY_DATA_EUROPE;
  }

  private static async _getAssetDiscoveryData(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Promise<AssetDiscoveryDataType> {
    const [topMovers, mostTradedAssets, popularAssetsOverride] = await Promise.all([
      AssetDiscoveryDataCronService._getTopMovers(companyEntity),
      AssetDiscoveryDataCronService._getMostTradedAssets(companyEntity),
      AssetDiscoveryDataCronService._getPopularAssetOverrides()
    ]);
    const collections = AssetDiscoveryDataCronService._getAssetCollections();
    const etfSection = AssetDiscoveryDataCronService._getEtfSection(companyEntity);
    const popularAssetsSection = concatenateUnique(popularAssetsOverride, mostTradedAssets);

    return {
      topMovers,
      popularAssetsSection,
      collections,
      etfSection
    };
  }

  private static _validateAssetDiscoveryData(assetDiscoveryData: AssetDiscoveryDataType): void {
    const { topMovers, popularAssetsSection } = assetDiscoveryData;

    const popularAssetSectionIsValid = popularAssetsSection.length >= MOST_TRADED_ASSETS_LIMIT;
    const topMoversAreValid =
      topMovers.best.length === topMovers.worst.length &&
      topMovers.best.length === TOP_MOVERS_LIMIT &&
      !topMovers.best.some((bestMover) =>
        topMovers.worst.some((worstMover) => worstMover.asset === bestMover.asset)
      );

    if (!topMoversAreValid || !popularAssetSectionIsValid) {
      logger.error("Invalid asset discovery data!", {
        module: "AssetDiscoveryDataCronService",
        method: "_validateAssetDiscoveryData",
        data: {
          topMovers,
          popularAssetsSection
        }
      });
      throw Error("Invalid asset discovery data!");
    }
  }

  private static async _getTopMovers(companyEntity: entitiesConfig.CompanyEntityEnum): Promise<TopMoversType> {
    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("id", true, {
      listedOnly: true
    });

    const investmentUniverse = ConfigUtil.getInvestmentUniverse(companyEntity);
    const allowedAssets = new Set(Object.keys(investmentUniverse));

    const assetReturns = Object.values(investmentProductsDict)
      .filter(
        (investmentProduct) => investmentProduct.currentTicker && allowedAssets.has(investmentProduct.commonId)
      )
      .map((investmentProduct) => ({
        asset: investmentProduct.commonId,
        returnPercentage: investmentProduct.currentTicker!.dailyReturnPercentage
      }));

    const topMoversBest = assetReturns
      .sort((a, b) => b.returnPercentage - a.returnPercentage)
      .slice(0, TOP_MOVERS_LIMIT);
    const topMoversWorst = assetReturns
      .sort((a, b) => a.returnPercentage - b.returnPercentage)
      .slice(0, TOP_MOVERS_LIMIT);

    return {
      best: topMoversBest,
      worst: topMoversWorst
    };
  }

  private static async _getMostTradedAssets(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Promise<investmentUniverseConfig.AssetType[]> {
    const yesterday = DateUtil.getYesterday(new Date(Date.now()));
    const { start, end } = DateUtil.getLastNDaysRange(yesterday, 7);

    const assetTransactions = await AssetTransaction.find({
      createdAt: { $gte: start, $lte: end },
      pendingGift: { $exists: false }
    }).populate({ path: "orders", strictPopulate: false });

    const tradesPerAssetDict: Record<string, number> = {};
    assetTransactions
      .flatMap(({ orders }) => orders)
      .filter((order: OrderDocument) => order.assetCategory === "stock")
      .forEach((order) => {
        if (tradesPerAssetDict[order.isin]) {
          tradesPerAssetDict[order.isin]++;
        } else {
          tradesPerAssetDict[order.isin] = 1;
        }
      });

    const entityInvestmentUniverse = ConfigUtil.getInvestmentUniverse(companyEntity);
    const allowedAssets = new Set(Object.keys(entityInvestmentUniverse));

    return Object.entries(tradesPerAssetDict)
      .map((tradesPerAssetEntry) => ({ isin: tradesPerAssetEntry[0], trades: tradesPerAssetEntry[1] }))
      .sort((entryA, entryB) => entryB.trades - entryA.trades)
      .map(({ isin }) => AssetDiscoveryDataCronService._isinToCommonIdDict[isin])
      .filter((asset) => allowedAssets.has(asset))
      .filter(InvestmentUniverseUtil.isAssetActive)
      .slice(0, MOST_TRADED_ASSETS_LIMIT);
  }

  private static async _getPopularAssetOverrides(): Promise<investmentUniverseConfig.AssetType[]> {
    const popularAssetOverrides = await CloudflareService.Instance.getDataFromFile<
      investmentUniverseConfig.AssetType[]
    >(FilesEnum.POPULAR_ASSETS_OVERRIDE, BucketsEnum.POPULAR_ASSETS);
    const popularAssetOverridesIsValid =
      Array.isArray(popularAssetOverrides) &&
      popularAssetOverrides.every((popularAssetOverride) =>
        InvestmentUniverseUtil.isAssetActive(popularAssetOverride)
      );
    if (!popularAssetOverridesIsValid) {
      logger.error("Invalid format for popular asset overrides", {
        module: "AssetDiscoveryDataCronService",
        method: "_getPopularAssetOverrides",
        data: {
          popularAssetOverrides
        }
      });
      return [];
    }

    return popularAssetOverrides;
  }

  private static _getAssetCollections(): AssetCollectionType {
    return investmentUniverseConfig.COLLECTION_CONFIG;
  }

  private static _getEtfSection(companyEntity: entitiesConfig.CompanyEntityEnum): EtfSectionType {
    return {
      top: TOP_ETFS[companyEntity],
      popularIndex: POPULAR_INDEX_ETFS[companyEntity]
    };
  }
}
