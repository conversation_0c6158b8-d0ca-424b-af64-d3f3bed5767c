import Decimal from "decimal.js";
import mongoose from "mongoose";
import { captureException } from "@sentry/node";
import { ProviderEnum } from "../../configs/providersConfig";
import events from "../../event-handlers/events";
import logger from "../../external-services/loggerService";
import analytics, { TrackPortfolioPropertiesType } from "../../external-services/segmentAnalyticsService";
import { RedisClientService } from "../../loaders/redis";
import {
  RebalanceAutomationDocument,
  SavingsTopUpAutomationDocument,
  TopUpAutomationDocument
} from "../../models/Automation";
import { PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import AutomationService from "../../services/automationService";
import PortfolioService from "../../services/portfolioService";
import { TransactionService } from "../../services/transactionService";
import RewardService from "../../services/rewardService";
import DateUtil from "../../utils/dateUtil";
import InvestmentProductService from "../../services/investmentProductService";
import PortfolioUtil from "../../utils/portfolioUtil";

export default class PortfolioCronService {
  /**
   * @description This method iterates on all portfolios with holdings and caches their MWRR and Up by values.
   * MWRR is an expensive and blocking calculation so we run it at the end of the day to use it
   * real time as part of time-weighted returns.
   *
   * The method is intended to run at the end of the day.
   */
  public static async cacheAllPortfolioMWRRsAndUpByValues(): Promise<void> {
    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("commonId", true, {
      listedOnly: true
    });

    await PortfolioService.getPortfoliosStreamed(
      {
        mode: PortfolioModeEnum.REAL,
        holdings: { $gt: { $size: 0 } }
      },
      { currentTicker: true, owner: false }
    ).eachAsync(async (portfolio) => {
      // Calculate portfolio MWRR for all tenors
      const ownerId = portfolio.owner as mongoose.Types.ObjectId;
      const [dividendTransactions, rewardsForReturns] = await Promise.all([
        TransactionService.getDividendTransactionsForReturnsUpBy(ownerId),
        RewardService.getSettledRewards(portfolio.owner as mongoose.Types.ObjectId)
      ]);
      const portfolioWithPopulatedAssets = PortfolioUtil.populatePortfolioWithInvestmentProducts(
        portfolio,
        investmentProductsDict
      );

      const portfolioValue = portfolio.getCalculatedPrice(
        portfolioWithPopulatedAssets.holdings.map((holding) => holding.asset)
      );

      // Even though the portfolio has holdings it might have a very low quantity rounded to a portfolio value of 0.
      if (portfolioValue === 0) {
        return;
      }

      const transactions = { dividendTransactions, rewards: rewardsForReturns };
      const [mwrrByTenor, upByValues] = await Promise.all([
        PortfolioService.getPortfolioMWRR(portfolioWithPopulatedAssets, transactions),
        PortfolioService.calculateUpByValues(portfolioWithPopulatedAssets, transactions)
      ]);

      // Cache MWRR per portfolio id
      await RedisClientService.Instance.mSet({
        [`portfolios:mwrr:${portfolio.id}`]: mwrrByTenor,
        [`portfolios:value_at_mwrr:${portfolio.id}`]: portfolioValue ?? 0,
        [`portfolios:up_by:${portfolio.id}`]: upByValues,
        [`portfolios:value_at_up_by:${portfolio.id}`]: portfolioValue ?? 0
      });
    });
  }

  /**
   * @description
   * We submit to WK portfolios that:
   * 1. Have not already been submitted
   * 2. Whose owners are KYC passed & have submitted their required info > 10 minutes ago.
   */
  public static async createAllWkPortfolios() {
    // Retrieving the portfolios will fetch more than 10k portfolios that most of them are not eligible.
    // Instead we query for eligible users and map to the portfolios.
    const verifiedUsers = await User.find({
      kycStatus: KycStatusEnum.PASSED,
      $or: [
        {
          updatedAt: {
            $gt: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7),
            $lt: DateUtil.getDateOfMinutesAgo(10)
          }
        },
        {
          submittedRequiredInfoAt: {
            $gt: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7),
            $lt: DateUtil.getDateOfMinutesAgo(10)
          }
        }
      ]
    }).populate("portfolios");
    const unsubmittedPortfolios = verifiedUsers
      .map((user) => user.portfolios[0] as PortfolioDocument)
      .filter(
        (portfolio) =>
          portfolio.activeProviders.includes(ProviderEnum.WEALTHKERNEL) &&
          portfolio.isReal &&
          !portfolio.isSubmittedToBroker
      );

    for (let i = 0; i < unsubmittedPortfolios.length; i++) {
      const portfolio = unsubmittedPortfolios[i];

      try {
        await PortfolioService.createBrokeragePortfolio(portfolio);
      } catch (err) {
        captureException(err);
        logger.error(`Portfolio creation failed for user ${portfolio.owner._id}`, {
          module: "PortfolioCronService",
          method: "createAllWkPortfolios",
          data: { userId: portfolio.owner._id, portfolioId: portfolio._id, error: err }
        });
      }
    }
  }

  /**
   * @description The function sends a portfolio valuation event for portfolios with cash or holdings.
   * (ignoring empty/withdrawn portfolios)
   */
  public static async identifyPortfolioHoldingsCashValue(): Promise<void> {
    await PortfolioService.getPortfoliosStreamed(
      {
        mode: PortfolioModeEnum.REAL,
        $or: [
          { holdings: { $gt: { $size: 0 } } },
          { "cash.GBP.available": { $gt: 0 } },
          { "cash.EUR.available": { $gt: 0 } },
          { "cash.USD.available": { $gt: 0 } }
        ]
      },
      { currentTicker: true, owner: true }
    ).eachAsync(async (portfolio) => {
      const user = portfolio.owner as UserDocument;
      if (user.isDeleted) return;

      const cash = portfolio?.cash?.[user.currency]?.available ?? 0;
      const portfolioValue = user.hasConvertedPortfolio ? portfolio.currentTicker.getPrice(user.currency) : 0;
      const savingsGBP = Decimal.div(portfolio?.savings?.get("mmf_dist_gbp")?.amount ?? 0, 100).toNumber();
      const savingsEUR = Decimal.div(portfolio?.savings?.get("mmf_dist_eur")?.amount ?? 0, 100).toNumber();

      const props: TrackPortfolioPropertiesType = {
        cash,
        currency: portfolio.currency,
        portfolioValue,
        savingsGBP,
        savingsEUR,
        hasRepeatingRebalance: false,
        hasRepeatingTopup: false,
        hasRepeatingSavingsTopup: false
      };

      const automations = await AutomationService.getAutomations({
        owner: user.id,
        activeOnly: true
      });

      // Setup repeating investment properties
      const activeTopupAutomation = automations.data.find(
        (automation) => automation.category === "TopUpAutomation"
      ) as TopUpAutomationDocument;
      if (activeTopupAutomation) {
        props.hasRepeatingTopup = true;
        props.repeatingTopupAmount = Decimal.div(activeTopupAutomation.consideration.amount, 100).toNumber();
      }

      // Setup repeating savings properties
      const activeSavingsTopupAutomation = automations.data.find(
        (automation) => automation.category === "SavingsTopUpAutomation"
      ) as SavingsTopUpAutomationDocument;
      if (activeSavingsTopupAutomation) {
        props.hasRepeatingSavingsTopup = true;
        props.repeatingSavingsTopupAmount = Decimal.div(
          activeSavingsTopupAutomation.consideration.amount,
          100
        ).toNumber();
      }

      // Setup automated rebalance properties
      const activeRebalanceAutomation = automations.data.find(
        (automation) => automation.category === "RebalanceAutomation"
      ) as RebalanceAutomationDocument;
      if (activeRebalanceAutomation) {
        props.hasRepeatingRebalance = true;
      }

      analytics.identify(user, { cash, portfolioValue, savingsGBP, savingsEUR }, { All: false, Mixpanel: true });
      analytics.track(user, events.portfolio.portfolioValuation.name, { All: false, Mixpanel: true }, props);
    });
  }
}
