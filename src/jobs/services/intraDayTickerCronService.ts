import Decimal from "decimal.js";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import eodService, { ExchangeRates } from "../../external-services/eodService";
import logger from "../../external-services/loggerService";
import { RedisClientService } from "../../loaders/redis";
import { IntraDayAssetTicker, IntraDayPortfolioTicker } from "../../models/IntraDayTicker";
import { PortfolioDocument } from "../../models/Portfolio";
import { UserDocument } from "../../models/User";
import InvestmentProductService from "../../services/investmentProductService";
import UserService from "../../services/userService";
import type { Entries, PartialRecord } from "types/utils";
import DateUtil from "../../utils/dateUtil";
import * as CacheUtil from "../../utils/cacheUtil";
import CurrencyUtil from "../../utils/currencyUtil";
import * as TickerUtil from "../../utils/tickerUtil";
import { addBreadcrumb, captureException } from "@sentry/node";
import PortfolioUtil from "../../utils/portfolioUtil";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { ASSET_CONFIG } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { FMPService } from "../../external-services/fmpService";
import { getFMPSymbolFromAssetId } from "../../utils/fmpUtil";
import { envIsProd } from "../../utils/environmentUtil";
import SocketEmitter from "../../socket/emitter";
import { RoomActionEnum, RoomEnum } from "../../socket/rooms";
import { InvestmentProduct } from "../../models/InvestmentProduct";

const { AssetArrayConst } = investmentUniverseConfig;

const DB_BATCH_SIZE = 500;
const FMP_BATCH_SIZE = 100;
const EOD_BATCH_SIZE = 20;
const BROADCAST_ASSET_PRICES_BATCH_SIZE = 50;

const MINUTES_AFTER_CLOSING_TO_RETRIEVE_PRICES = 50;

// We mark those assets for a more lenient staleness check, because they are constantly late to be updated by EOD.
const LENIENT_STALE_CHECK_ASSETS: investmentUniverseConfig.AssetType[] = [
  "government_bonds_us",
  "government_bonds_eu",
  "government_bonds_em",
  "government_bonds_em_esg",
  "government_bonds_global",
  "government_bonds_global_inflation",
  "government_bonds_uk_invesco",
  "corporate_bonds_us",
  "corporate_bonds_eu",
  "corporate_bonds_global_gbp_spdr",
  "corporate_bonds_global_usd",
  "equities_global_financials_broad",
  "equities_global_materials_broad",
  "equities_global_consumer_discretionary_broad",
  "equities_global_consumer_staples_broad",
  "equities_us_health_care_broad",
  "equities_global_industrials_broad",
  "equities_global_pharma",
  "equities_global_utilities_broad",
  "equities_us_utilities_broad",
  "equities_us_consumer_discretionary_broad",
  "equities_us_mid_cap",
  "equities_us_industrials_broad",
  "equities_global_health_care_broad",
  "equities_us_energy_broad",
  "equities_global_energy_broad",
  "equities_us_biotech",
  "equities_us_biotech_dist",
  "equities_us_consumer_staples_broad",
  "equities_us_communication_broad",
  "equities_us_materials_broad",
  "equities_us_small_cap",
  "equities_eu_high_dividend_low_vol",
  "equities_em",
  "equities_france",
  "equities_germany",
  "equities_brazil",
  "equities_taiwan",
  "equities_global_battery_solutions",
  "equities_us_income",
  "equities_em_income",
  "equities_eu_income",
  "equities_asia",
  "equities_asia_dividend_aristocrats",
  "equities_global_climate",
  "equities_uk_sustainable_equity",
  "equities_us_high_dividend_low_vol",
  "equities_ibex",
  "equities_global_oil_gas",
  "equities_global_tech_disruptive_tech_esg",
  "equities_mib",
  "equities_global_cloud_computing",
  "equities_global_esg",
  "commodities_agriculture",
  "commodities_all",
  "commodities_oil",
  "real_estate_us",
  "readymade_lifestrategy_20_equity",
  "readymade_lifestrategy_40_equity",
  "readymade_lifestrategy_60_equity",
  "readymade_lifestrategy_80_equity"
];

export default class IntraDayTickerCronService {
  /**
   * @description Method that caches today's asset prices using real-time FMP API & EOD API for the assets that do not
   * have realtime data in FMP.
   *
   * If the prices are not today's they're skipped.
   */
  public static async cacheTodaysAssetPrices(): Promise<void> {
    try {
      // fetch prices from both EOD & FMP.
      const [eodTickers, fmpTickers] = await Promise.all([
        IntraDayTickerCronService._fetchEodRealTimeTickers(),
        IntraDayTickerCronService._fetchFmpRealTimeTickers()
      ]);

      const latestTickers: PartialRecord<
        investmentUniverseConfig.AssetType,
        { close: number; timestamp: number }
      > = { ...eodTickers, ...fmpTickers };

      // filter out any empty prices & prices that are not today's
      const { start } = DateUtil.getStartAndEndOfToday();
      const filteredLatestTickers = Object.fromEntries(
        Object.entries(latestTickers).filter(
          ([, entry]) => entry && entry.close > 0 && entry.timestamp > new Date(start).getTime()
        )
      );

      if (Object.keys(filteredLatestTickers).length > 0) {
        await RedisClientService.Instance.set("fmp:today:latest_tickers", filteredLatestTickers);
      }
    } catch (err) {
      captureException(err);
      logger.error("Caching today's asset prices failed.", {
        module: "IntraDayTickerCronService",
        method: "cacheTodaysAssetPrices",
        data: { error: err }
      });
    }
  }

  /**
   * @description Method that fetches todays real-time asset prices from redis cache
   * and stores them in database by creating intra-day asset tickers.
   */
  public static async storeAssetPrices(): Promise<void> {
    logger.info("Storing intra-day asset prices", {
      module: "IntraDayTickerCronService",
      method: "storeAssetPrices"
    });

    // 1. get today's cached asset prices & exit if there are not any for today
    const latestTickers = await TickerUtil.getCachedTodaysTickers();
    if (Object.keys(latestTickers).length === 0) return;

    // 2. get investment products & cached fx rates
    const [investmentProductsDict, latestFXRates, cached30dPricesValues, cached1dPricesValues] = await Promise.all(
      [
        InvestmentProductService.getInvestmentProductsDict("commonId", false),
        CacheUtil.getCachedDataWithFallback<ExchangeRates>(
          "fxRates",
          async (): Promise<ExchangeRates> => eodService.getLatestFXRates()
        ),
        RedisClientService.Instance.mGet<number>(
          Object.keys(latestTickers).map((assetId) => `eod:price_30d_ago:${assetId}`)
        ),
        RedisClientService.Instance.mGet<number>(
          Object.keys(latestTickers).map((assetId) => `eod:price_1d_ago:${assetId}`)
        )
      ]
    );

    const cached30dPricesDict = {} as PartialRecord<investmentUniverseConfig.AssetType, number>;
    Object.keys(latestTickers).forEach((commonId: investmentUniverseConfig.AssetType, index: number) => {
      if (cached30dPricesValues[index]) {
        cached30dPricesDict[commonId] = cached30dPricesValues[index];
      }
    });

    const cached1dPricesDict = {} as PartialRecord<investmentUniverseConfig.AssetType, number>;
    Object.keys(latestTickers).forEach((commonId: investmentUniverseConfig.AssetType, index: number) => {
      if (cached1dPricesValues[index]) {
        cached1dPricesDict[commonId] = cached1dPricesValues[index];
      }
    });

    // 3. store prices in asset ticker docs
    const dbOperations = (
      Object.entries(latestTickers) as Entries<
        Record<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
      >
    )
      .filter(
        ([commonId]) =>
          investmentProductsDict[commonId]?.listed &&
          InvestmentProductService.isAssetCurrentlyTraded(commonId, {
            includeAfterCloseMinutes: MINUTES_AFTER_CLOSING_TO_RETRIEVE_PRICES
          })
      )
      .map(
        ([commonId, { close, timestamp }]: [
          investmentUniverseConfig.AssetType,
          { close: number; timestamp: number }
        ]) => {
          try {
            const assetTradedCurrency = InvestmentProductService.getMainTradedCurrency(commonId);
            const assetPricePerCurrency = CurrencyUtil.mapFxDataToAmountPerCurrency(
              assetTradedCurrency,
              close,
              latestFXRates
            );

            return {
              updateOne: {
                filter: { investmentProduct: investmentProductsDict[commonId].id, timestamp: new Date(timestamp) },
                update: {
                  $setOnInsert: {
                    currency: assetTradedCurrency,
                    timestamp: new Date(timestamp),
                    pricePerCurrency: assetPricePerCurrency,
                    investmentProduct: investmentProductsDict[commonId].id,
                    dailyReturnPercentage: PortfolioUtil.getReturns({
                      startValue: new Decimal(cached1dPricesDict[commonId]),
                      endValue: new Decimal(assetPricePerCurrency[assetTradedCurrency])
                    }),
                    monthlyReturnPercentage: PortfolioUtil.getReturns({
                      startValue: new Decimal(cached30dPricesDict[commonId]),
                      endValue: new Decimal(assetPricePerCurrency[assetTradedCurrency])
                    })
                  }
                },
                upsert: true
              }
            };
          } catch (err) {
            logger.error(`Error storing intra-day asset ticker for ${commonId}`, {
              module: "IntraDayTickerCronService",
              method: "storeAssetPrices",
              data: {
                error: err,
                commonId,
                close,
                timestamp,
                "1dPrice": cached1dPricesDict[commonId],
                "30dPrice": cached30dPricesDict[commonId]
              }
            });
            captureException(err);
          }
        }
      );

    await IntraDayAssetTicker.bulkWrite(dbOperations.filter((operation) => operation !== undefined));
  }

  /**
   * @description Method that fetches all intra-day asset tickers from the last 20 minutes and
   * broadcasts their pricing to the Socket.io pricing room.
   */
  public static async broadcastAssetPrices(): Promise<void> {
    logger.info("Broadcasting intra-day asset prices", {
      module: "IntraDayTickerCronService",
      method: "broadcastAssetPrices"
    });

    const twentyMinutesAgo = DateUtil.getDateOfMinutesAgo(20);
    let didBroadcastPrices = false;

    // Query investment products and populate their latest ticker
    await InvestmentProduct.find({ listed: true })
      .populate({
        path: "currentTicker",
        match: { timestamp: { $gte: twentyMinutesAgo } }
      })
      .cursor()
      .addCursorFlag("noCursorTimeout", envIsProd())
      .eachAsync(
        (products) => {
          const productsWithRecentTickers = products.filter((product) => product.currentTicker != null);

          if (productsWithRecentTickers.length > 0) {
            didBroadcastPrices = true;

            SocketEmitter.Instance.emit(
              {
                key: `${RoomEnum.PRICING}:${RoomActionEnum.UPDATE}`,
                value: JSON.stringify(
                  productsWithRecentTickers.map((product) => {
                    return {
                      assetId: product.commonId,
                      pricePerCurrency: product.currentTicker.pricePerCurrency,
                      dailyReturnPercentage: product.currentTicker.dailyReturnPercentage,
                      monthlyReturnPercentage: product.currentTicker.monthlyReturnPercentage
                    };
                  })
                )
              },
              RoomEnum.PRICING
            );
          }
        },
        { batchSize: BROADCAST_ASSET_PRICES_BATCH_SIZE }
      );

    if (didBroadcastPrices) {
      SocketEmitter.Instance.emit(
        { key: `${RoomEnum.PRICING}:${RoomActionEnum.UPDATE_COMPLETED}` },
        RoomEnum.PRICING
      );

      // Clear investment product cache after broadcasting asset prices to avoid having stale data in endpoints used
      // by the client.
      await RedisClientService.Instance.del("investmentProducts");
      await RedisClientService.Instance.del("investmentProductsWithTickers");
    }
  }

  /**
   * @description Method that fetches the intra-day asset prices from the database, checks if an asset's ticker
   * hasn't been updated as expected and if not, sends a notification to Slack.
   */
  public static async checkStaleTickers(): Promise<void> {
    logger.info("Check for stale intra-day tickers", {
      module: "IntraDayTickerCronService",
      method: "checkStaleTickers"
    });

    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: true,
      useCache: true,
      listedOnly: true
    });

    // If an asset hasn't been updated for more than 2 hours (or for more than a business day for some assets)
    // and is currently traded, send notification
    const staleTickers = investmentProducts
      .filter((product) =>
        InvestmentProductService.isAssetCurrentlyTraded(product.commonId, { doNotIncludeAfterOpenMinutes: 60 })
      )
      .filter((product) => {
        if (LENIENT_STALE_CHECK_ASSETS.includes(product.commonId)) {
          return (
            DateUtil.calculateMostRecentUKWorkDay(new Date(Date.now())) > new Date(product.currentTicker.timestamp)
          );
        }
        return DateUtil.dateDiffInExactHours(new Date(product.currentTicker.timestamp), new Date(Date.now())) >= 2;
      })
      .map((product) => {
        return {
          assetName: ASSET_CONFIG[product.commonId].simpleName,
          lastUpdateDate: product.currentTicker.timestamp
        };
      });

    if (staleTickers.length > 0) {
      eventEmitter.emit(events.investmentProduct.staleTicker.eventId, staleTickers);
    }
  }

  /**
   * @description Method that fetches todays intra-day asset prices from database
   * calculates the portfolio values and stores them in database by creating
   * intra-day portfolio tickers.
   *
   * Note that the method will create tickers whenever it runs, regardless of whether
   * the asset tickers are today's or not.
   *
   * Portfolio ticker creation checks that we haven't created a ticker within one minute,
   * to avoid creating duplicate data.
   *
   * Prices are stored only for portfolios that have target allocation set.
   */
  public static async storePortfolioPrices(): Promise<void> {
    logger.info("Storing intra-day portfolio prices", {
      module: "IntraDayTickerCronService",
      method: "storePortfolioPrices"
    });

    // 1. Get all investment products with asset tickers populated & cached fx rates
    const [investmentProductsDict, latestFXRates] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("commonId", true, {
        listedOnly: true
      }),
      CacheUtil.getCachedDataWithFallback<ExchangeRates>(
        "fxRates",
        async (): Promise<ExchangeRates> => eodService.getLatestFXRates()
      )
    ]);

    // 2. Iterate on all users with invested portfolios (can be no holdings) to create an intra day ticker for each of them.
    // The iteration happens in batches and for each batch we bulk write on the db to create the tickers.
    await UserService.getUsersStreamed({ portfolioConversionStatus: "completed" }, "portfolios").eachAsync(
      async (users) => {
        const dbOperations = users
          .filter((user) => !user.isDeleted)
          .map((owner: UserDocument) => {
            try {
              const portfolio = owner.portfolios[0] as PortfolioDocument;

              // Portfolio value in user currency (settlement currency)
              const portfolioValue = new Decimal(
                portfolio.holdings
                  .map(({ assetCommonId, quantity }) =>
                    Decimal.mul(
                      investmentProductsDict[assetCommonId].currentTicker.getPrice(owner.currency),
                      quantity
                    )
                  )
                  .reduce((sum, value) => sum.plus(value), new Decimal(0)) || new Decimal(0)
              )
                .toDecimalPlaces(2, Decimal.ROUND_DOWN)
                .toNumber();
              const portfolioPricePerCurrency = CurrencyUtil.mapFxDataToAmountPerCurrency(
                owner.currency ?? "GBP",
                portfolioValue,
                latestFXRates
              );
              return {
                updateOne: {
                  // We want to avoid having more than one portfolio tickers within the same minute.
                  // That would be an indication that the implementation is wrong.
                  // So we filter by timestamp
                  filter: { portfolio: portfolio.id, timestamp: { $gt: DateUtil.getDateOfMinutesAgo(1) } },
                  update: {
                    $setOnInsert: {
                      currency: owner.currency,
                      timestamp: new Date(),
                      pricePerCurrency: portfolioPricePerCurrency,
                      portfolio: portfolio.id
                    }
                  },
                  upsert: true
                }
              };
            } catch (err) {
              const portfolio = owner.portfolios[0] as PortfolioDocument;
              addBreadcrumb({
                message: "Error processing portfolio for intra-day ticker",
                category: "portfolio.error",
                data: {
                  portfolioId: portfolio?.id,
                  userId: owner.id,
                  holdingsCount: portfolio?.holdings?.length
                },
                level: "error"
              });
              captureException(err);
              logger.error(`Error processing portfolio ${portfolio?.id} for intra-day ticker`, {
                module: "IntraDayTickerCronService",
                method: "storePortfolioPrices",
                data: {
                  error: err,
                  portfolioId: portfolio?.id,
                  userId: owner.id
                }
              });
              return undefined;
            }
          })
          .filter((operation) => operation !== undefined);

        await IntraDayPortfolioTicker.bulkWrite(dbOperations);
      },
      { batchSize: DB_BATCH_SIZE }
    );
  }

  private static async _fetchEodRealTimeTickers(): Promise<
    PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
  > {
    const tickers: PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }> = {};

    const eodOnlyTradedAssetIDs = AssetArrayConst.filter(
      (assetId) =>
        InvestmentProductService.isAssetCurrentlyTraded(assetId, {
          includeAfterCloseMinutes: MINUTES_AFTER_CLOSING_TO_RETRIEVE_PRICES
        }) && (ASSET_CONFIG[assetId] as investmentUniverseConfig.ETFAssetConfigType).eodOnly
    );

    for (let i = 0; i < eodOnlyTradedAssetIDs.length; i += EOD_BATCH_SIZE) {
      const batch = eodOnlyTradedAssetIDs.slice(i, i + EOD_BATCH_SIZE);

      try {
        const eodResponse = await eodService.getRealTimeAssetsData(batch);

        eodResponse.forEach((realTimeTicker) => {
          if (!realTimeTicker) return;

          const { assetCommonId, timestamp, close } = realTimeTicker;
          tickers[assetCommonId] = { close, timestamp };
        });
      } catch (err) {
        captureException(err);
        logger.error(`Getting EOD real-time asset data failed for assets ${batch.join(", ")}`, {
          module: "IntraDayTickerCronService",
          method: "_fetchEodRealTimeTickers",
          data: { batch, error: err }
        });
      }
    }

    return tickers;
  }

  private static async _fetchFmpRealTimeTickers(): Promise<
    PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
  > {
    const tickers: PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }> = {};

    const tradedAssetIDs = AssetArrayConst.filter(
      (assetId) =>
        InvestmentProductService.isAssetCurrentlyTraded(assetId, {
          includeAfterCloseMinutes: MINUTES_AFTER_CLOSING_TO_RETRIEVE_PRICES
        }) && !(ASSET_CONFIG[assetId] as investmentUniverseConfig.ETFAssetConfigType).eodOnly
    );

    for (let i = 0; i < tradedAssetIDs.length; i += FMP_BATCH_SIZE) {
      const batch = tradedAssetIDs.slice(i, i + FMP_BATCH_SIZE);

      // Keep a reverse mapping so that we can map from FMP symbols in the response to asset IDs.
      const fmpSymbolsToAssetIds: { [key: string]: investmentUniverseConfig.AssetType } = Object.fromEntries(
        batch.map((assetId) => [getFMPSymbolFromAssetId(assetId), assetId])
      );
      const symbols = Object.keys(fmpSymbolsToAssetIds);

      try {
        const fmpResponse = await FMPService.Instance.getBatchQuote(symbols);

        if (fmpResponse.length !== batch.length) {
          logger.warn("FMP response size does not match request size!", {
            module: "IntraDayTickerCronService",
            method: "_fetchFmpRealTimeTickers",
            data: {
              requested: symbols,
              received: fmpResponse.map((q) => q.symbol)
            }
          });
        }

        fmpResponse.forEach((quote) => {
          const assetCommonId = fmpSymbolsToAssetIds[quote.symbol];
          const { price, timestamp } = quote;

          tickers[assetCommonId] = {
            close: CurrencyUtil.convertAmountToMainCurrency(
              investmentUniverseConfig.ASSET_CONFIG[assetCommonId].tradedCurrency,
              price
            ),
            timestamp: Decimal.mul(timestamp, 1000).toNumber()
          };
        });
      } catch (err) {
        captureException(err);
        logger.error(`Getting FMP real-time asset data failed for assets ${batch.join(", ")}`, {
          module: "IntraDayTickerCronService",
          method: "_fetchFmpRealTimeTickers",
          data: { batch, error: err }
        });
      }
    }

    return tickers;
  }
}
