import { faker } from "@faker-js/faker";
import { cloudflareConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import MockCloudflareService from "../../../external-services/__mocks__/cloudflareService";
import CloudflareService from "../../../external-services/cloudflareService";
import { CurrencyEnum } from "../../../external-services/wealthkernelService";
import { IntraDayAssetTicker } from "../../../models/IntraDayTicker";
import { InvestmentProduct } from "../../../models/InvestmentProduct";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildGift,
  buildIntraDayAssetTicker,
  buildInvestmentProduct,
  buildOrder
} from "../../../tests/utils/generateModels";
import DateUtil from "../../../utils/dateUtil";
import AssetDiscoveryDataCronService, { TopMoversType } from "../assetDiscoveryDataCronService";

const { KvNamespaceKeys } = cloudflareConfig;
const { ASSET_CONFIG } = investmentUniverseConfig;

const buildInvestmentProductWithIntraDayReturn = async (
  assetId: investmentUniverseConfig.AssetType,
  date: Date,
  returnPercentage: number
): Promise<{
  asset: investmentUniverseConfig.AssetType;
  returnPercentage: number;
}> => {
  const investmentProduct = await buildInvestmentProduct(false, {
    assetId
  });
  const { start, end } = DateUtil.getStartAndEndOfDay(date);
  const firstPrice = new Decimal(1); // Hardcode initial price at 1
  const returnRate = new Decimal(returnPercentage).div(100);
  const lastPrice = firstPrice.mul(returnRate.plus(1));
  await Promise.all([
    buildIntraDayAssetTicker({
      investmentProduct: investmentProduct.id,
      currency: "GBP",
      timestamp: start,
      dailyReturnPercentage: returnRate.toNumber(),
      pricePerCurrency: {
        GBP: firstPrice.toNumber()
      }
    }),
    buildIntraDayAssetTicker({
      investmentProduct: investmentProduct.id,
      currency: "GBP",
      timestamp: end,
      dailyReturnPercentage: returnRate.toNumber(),
      pricePerCurrency: {
        GBP: lastPrice.toNumber()
      }
    })
  ]);
  return {
    asset: investmentProduct.commonId,
    returnPercentage: returnPercentage
  };
};

const generateTopMovers = async (
  bestMovers: investmentUniverseConfig.AssetType[],
  worstMovers: investmentUniverseConfig.AssetType[],
  date: Date = new Date(Date.now()),
  limit = 8
): Promise<TopMoversType> => {
  // Add 2 decimal precision
  const returnPercentages = Array.from(Array(limit).keys())
    .map((returnPercentage) => Decimal.add(returnPercentage, 1.03).toNumber())
    .reverse();
  const negativePercentages = returnPercentages.map((returnPercentage) => -returnPercentage);

  const topMoversBest = await Promise.all(
    returnPercentages.map((returnPercentage, index) =>
      buildInvestmentProductWithIntraDayReturn(bestMovers[index], date, returnPercentage)
    )
  );

  const topMoversWorst = await Promise.all(
    negativePercentages.map((returnPercentage, index) =>
      buildInvestmentProductWithIntraDayReturn(worstMovers[index], date, returnPercentage)
    )
  );

  return {
    best: topMoversBest.map(({ asset, returnPercentage }) => ({
      asset,
      returnPercentage: Decimal.div(returnPercentage, 100).toDecimalPlaces(4).toNumber()
    })),
    worst: topMoversWorst.map(({ asset, returnPercentage }) => ({
      asset,
      returnPercentage: Decimal.div(returnPercentage, 100).toDecimalPlaces(4).toNumber()
    }))
  };
};

const generateGiftedAssetTransactionWithOrders = async (
  asset: investmentUniverseConfig.AssetType,
  date: Date
): Promise<void> => {
  const gift = await buildGift();
  const assetTransaction = await buildAssetTransaction({
    status: "Settled",
    createdAt: date,
    pendingGift: gift.id
  });
  const order = await buildOrder({
    providers: {
      wealthkernel: {
        id: faker.string.uuid(),
        status: "Matched",
        submittedAt: new Date("2024-01-08") // mock
      }
    },
    consideration: {
      amount: 100, // stored in cents
      currency: CurrencyEnum.GBP
    },
    side: "Buy",
    transaction: assetTransaction.id,
    isin: ASSET_CONFIG[asset]?.isin
  });
  await assetTransaction.updateOne({ orders: [order.id] });
};

const generateAssetTransactionWithNthOrders = async (
  asset: investmentUniverseConfig.AssetType,
  date: Date,
  orderCount: number
): Promise<void> => {
  const assetTransaction = await buildAssetTransaction({ status: "Settled", createdAt: date });
  const orders = await Promise.all(
    Array.from(Array(orderCount).keys()).map(() =>
      buildOrder({
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: new Date("2024-01-08") // mock
          }
        },
        consideration: {
          amount: 100, // stored in cents
          currency: CurrencyEnum.GBP
        },
        side: "Buy",
        transaction: assetTransaction.id,
        isin: ASSET_CONFIG[asset]?.isin
      })
    )
  );
  await assetTransaction.updateOne({ orders: orders.map(({ id }) => id) });
};

const generatePopularAssets = async (date: Date): Promise<investmentUniverseConfig.AssetType[]> => {
  await Promise.all([
    generateAssetTransactionWithNthOrders("equities_baidu", date, 12),
    generateAssetTransactionWithNthOrders("equities_bank_of_america", date, 11),
    generateAssetTransactionWithNthOrders("equities_apple", date, 10),
    generateAssetTransactionWithNthOrders("equities_jpmorgan_chase", date, 9),
    generateAssetTransactionWithNthOrders("equities_3m", date, 8),
    generateAssetTransactionWithNthOrders("equities_abbott", date, 7),
    generateAssetTransactionWithNthOrders("equities_airbnb", date, 6),
    generateAssetTransactionWithNthOrders("equities_alphabet", date, 5),
    generateAssetTransactionWithNthOrders("equities_wells_fargo", date, 4),
    generateAssetTransactionWithNthOrders("equities_amazon", date, 3),
    generateAssetTransactionWithNthOrders("equities_cloudflare", date, 2),
    generateAssetTransactionWithNthOrders("equities_kraft_heinz", date, 1)
  ]);

  return [
    "equities_baidu",
    "equities_bank_of_america",
    "equities_apple",
    "equities_jpmorgan_chase",
    "equities_3m",
    "equities_abbott",
    "equities_airbnb",
    "equities_alphabet",
    "equities_wells_fargo",
    "equities_amazon",
    "equities_cloudflare",
    "equities_kraft_heinz"
  ];
};

describe("AssetDiscoveryDataCronService", () => {
  beforeAll(async () => await connectDb("AssetDiscoveryDataCronService"));
  afterAll(async () => await closeDb());

  describe("updateAssetDiscoveryData", () => {
    describe("topMovers", () => {
      // Wednesday
      const TODAY = new Date("2024-01-10");
      const YESTERDAY = new Date("2024-01-09");

      beforeEach(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);
      });
      afterEach(async () => await clearDb());

      it("should return the top movers based on the latest available ticker of each asset", async () => {
        Date.now = jest.fn(() => +TODAY);

        const bestMovers = await Promise.all([
          buildInvestmentProductWithIntraDayReturn("commodities_gold", TODAY, 8),
          buildInvestmentProductWithIntraDayReturn("commodities_silver", YESTERDAY, 7),
          buildInvestmentProductWithIntraDayReturn("equities_adobe", TODAY, 6),
          buildInvestmentProductWithIntraDayReturn("equities_apple", YESTERDAY, 5),
          buildInvestmentProductWithIntraDayReturn("equities_microsoft", TODAY, 4),
          buildInvestmentProductWithIntraDayReturn("equities_nvidia", YESTERDAY, 3),
          buildInvestmentProductWithIntraDayReturn("equities_meta", TODAY, 2),
          buildInvestmentProductWithIntraDayReturn("equities_alphabet", YESTERDAY, 1)
        ]);

        const worstMovers = await Promise.all([
          buildInvestmentProductWithIntraDayReturn("corporate_bonds_eu", TODAY, -8),
          buildInvestmentProductWithIntraDayReturn("corporate_bonds_uk", YESTERDAY, -7),
          buildInvestmentProductWithIntraDayReturn("equities_ryanair", TODAY, -6),
          buildInvestmentProductWithIntraDayReturn("equities_airbnb", YESTERDAY, -5),
          buildInvestmentProductWithIntraDayReturn("equities_booking_holdings", TODAY, -4),
          buildInvestmentProductWithIntraDayReturn("equities_paramount", YESTERDAY, -3),
          buildInvestmentProductWithIntraDayReturn("equities_sony", TODAY, -2),
          buildInvestmentProductWithIntraDayReturn("equities_toyota", YESTERDAY, -1)
        ]);

        const aggregateTopMovers: TopMoversType = {
          best: bestMovers.map((x) => ({
            ...x,
            returnPercentage: x.returnPercentage / 100
          })),
          worst: worstMovers.map((x) => ({
            ...x,
            returnPercentage: x.returnPercentage / 100
          }))
        };

        await generatePopularAssets(new Date("2024-01-09"));

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { topMovers } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(topMovers).toEqual(aggregateTopMovers);
      });

      it("should only return EU assets for EU company entity and UK assets for UK company entity", async () => {
        Date.now = jest.fn(() => +TODAY);

        await Promise.all([
          buildInvestmentProductWithIntraDayReturn("government_bonds_uk_invesco", TODAY, 8), // This is a EU-only asset!
          buildInvestmentProductWithIntraDayReturn("commodities_gold", YESTERDAY, 7),
          buildInvestmentProductWithIntraDayReturn("commodities_silver", YESTERDAY, 7),
          buildInvestmentProductWithIntraDayReturn("equities_adobe", TODAY, 6),
          buildInvestmentProductWithIntraDayReturn("equities_apple", YESTERDAY, 5),
          buildInvestmentProductWithIntraDayReturn("equities_microsoft", TODAY, 4),
          buildInvestmentProductWithIntraDayReturn("equities_nvidia", YESTERDAY, 3),
          buildInvestmentProductWithIntraDayReturn("equities_meta", TODAY, 2),
          buildInvestmentProductWithIntraDayReturn("equities_alphabet", YESTERDAY, 1)
        ]);

        await Promise.all([
          buildInvestmentProductWithIntraDayReturn("corporate_bonds_eu", TODAY, -8),
          buildInvestmentProductWithIntraDayReturn("corporate_bonds_uk", YESTERDAY, -7),
          buildInvestmentProductWithIntraDayReturn("equities_ryanair", TODAY, -6),
          buildInvestmentProductWithIntraDayReturn("equities_airbnb", YESTERDAY, -5),
          buildInvestmentProductWithIntraDayReturn("equities_booking_holdings", TODAY, -4),
          buildInvestmentProductWithIntraDayReturn("equities_paramount", YESTERDAY, -3),
          buildInvestmentProductWithIntraDayReturn("equities_sony", TODAY, -2),
          buildInvestmentProductWithIntraDayReturn("equities_toyota", YESTERDAY, -1)
        ]);

        await generatePopularAssets(new Date("2024-01-09"));

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();

        const { topMovers: topMoversUK } =
          MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(
          (topMoversUK as TopMoversType).best.find((mover) => mover.asset === "government_bonds_uk_invesco")
        ).toBeUndefined();

        const { topMovers: topMoversEU } =
          MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_EUROPE];
        expect(
          (topMoversEU as TopMoversType).best.find((mover) => mover.asset === "government_bonds_uk_invesco")
        ).not.toBeUndefined();
      });

      it("should throw an error if no top movers were found", async () => {
        await generatePopularAssets(new Date("2024-01-09"));

        await expect(AssetDiscoveryDataCronService.updateAssetDiscoveryData()).rejects.toThrow(
          new Error("Invalid asset discovery data!")
        );
      });

      it("should throw an error if best and worst top movers don't have the same length", async () => {
        const bestMovers = [
          "equities_microsoft",
          "equities_amazon",
          "equities_apple",
          "equities_nvidia",
          "equities_meta",
          "equities_tesla",
          "equities_berkshire_hathaway",
          "equities_visa"
        ] as investmentUniverseConfig.AssetType[];

        const worstMovers = [
          "equities_marriott",
          "equities_merck",
          "equities_mondelez",
          "equities_monster",
          "equities_moodys",
          "equities_nextera",
          "equities_oracle",
          "equities_philip_morris"
        ] as investmentUniverseConfig.AssetType[];

        // Create top movers
        await generateTopMovers(bestMovers, worstMovers, new Date("2024-01-09"));
        await generatePopularAssets(new Date("2024-01-09"));

        // Remove one of the best performing tickers, in order to have unequal amount of top movers
        const bestMoverToRemove = await InvestmentProduct.findOne({ commonId: "equities_microsoft" });

        await IntraDayAssetTicker.deleteMany({
          investmentProduct: bestMoverToRemove?.id
        });

        await expect(AssetDiscoveryDataCronService.updateAssetDiscoveryData()).rejects.toThrow(
          new Error("Invalid asset discovery data!")
        );
      });

      it("should successfully handle deprecated assets in the top traded list", async () => {
        Date.now = jest.fn(() => +TODAY);

        const bestMovers = [
          "equities_microsoft",
          "equities_amazon",
          "equities_apple",
          "equities_nvidia",
          "equities_meta",
          "equities_tesla",
          "equities_berkshire_hathaway",
          "equities_visa"
        ] as investmentUniverseConfig.AssetType[];

        const worstMovers = [
          "equities_marriott",
          "equities_merck",
          "equities_mondelez",
          "equities_monster",
          "equities_moodys",
          "equities_nextera",
          "equities_oracle",
          "equities_philip_morris"
        ] as investmentUniverseConfig.AssetType[];

        // First create the top movers data that's required
        await generateTopMovers(bestMovers, worstMovers, new Date("2024-01-09"));

        // Generate normal popular assets first
        await generatePopularAssets(new Date("2024-01-09"));

        // Add a "deprecated" and a non-deprecated asset with high trade count to make it appear in top traded
        await generateAssetTransactionWithNthOrders("equities_apple", new Date("2024-01-09"), 19);
        await generateAssetTransactionWithNthOrders("equities_nikola", new Date("2024-01-09"), 20);

        // Run the method - should not throw even with a deprecated asset
        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();

        // Verify the data saved to Cloudflare
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];

        // Check correct number of assets and no deprecated asset
        expect(popularAssetsSection.length).toBe(12);
        expect(popularAssetsSection).toContain("equities_apple");
        expect(popularAssetsSection).not.toContain("equities_nikola");
      });
    });

    describe("etfSection", () => {
      beforeEach(async () => {
        jest.resetAllMocks();
        // Wednesday
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => +TODAY);

        const bestMovers = [
          "equities_microsoft",
          "equities_amazon",
          "equities_apple",
          "equities_nvidia",
          "equities_meta",
          "equities_tesla",
          "equities_berkshire_hathaway",
          "equities_visa"
        ] as investmentUniverseConfig.AssetType[];

        const worstMovers = [
          "equities_marriott",
          "equities_merck",
          "equities_mondelez",
          "equities_monster",
          "equities_moodys",
          "equities_nextera",
          "equities_oracle",
          "equities_philip_morris"
        ] as investmentUniverseConfig.AssetType[];

        // Create top movers
        await generateTopMovers(bestMovers, worstMovers, new Date("2024-01-09"));
        // Create popular assets
        await generatePopularAssets(new Date("2024-01-09"));
      });
      afterEach(async () => await clearDb());

      it("should return correct ETF section", async () => {
        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { etfSection: etfSectionUK } =
          MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(etfSectionUK.top).toEqual([
          "equities_global",
          "equities_us_tech_broad",
          "equities_global_ai",
          "commodities_gold"
        ]);
        expect(etfSectionUK.popularIndex).toEqual([
          "equities_us",
          "equities_uk",
          "equities_global_tech_broad_nasdaq_100"
        ]);

        const { etfSection: etfSectionEU } =
          MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_EUROPE];
        expect(etfSectionEU.top).toEqual([
          "equities_global_ai",
          "equities_global_tech_blockchain_vaneck",
          "equities_us_biotech_dist",
          "equities_global_tech_electric_vehicles",
          "equities_us_dividend_aristocrats"
        ]);
        expect(etfSectionEU.popularIndex).toEqual([
          "equities_us_vanguard_acc",
          "equities_us_tech_broad_nasdaq_100_ishares",
          "equities_global_msci_world_spdr",
          "equities_eu_stoxx_600",
          "commodities_gold"
        ]);
      });
    });

    describe("collections", () => {
      beforeEach(async () => {
        jest.resetAllMocks();
        // Wednesday
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => +TODAY);

        const bestMovers = [
          "equities_microsoft",
          "equities_amazon",
          "equities_apple",
          "equities_nvidia",
          "equities_meta",
          "equities_tesla",
          "equities_berkshire_hathaway",
          "equities_visa"
        ] as investmentUniverseConfig.AssetType[];

        const worstMovers = [
          "equities_marriott",
          "equities_merck",
          "equities_mondelez",
          "equities_monster",
          "equities_moodys",
          "equities_nextera",
          "equities_oracle",
          "equities_philip_morris"
        ] as investmentUniverseConfig.AssetType[];

        // Create top movers
        await generateTopMovers(bestMovers, worstMovers, new Date("2024-01-09"));
        // Create popular assets
        await generatePopularAssets(new Date("2024-01-09"));
      });
      afterEach(async () => await clearDb());

      it("should return the expected collections", async () => {
        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { collections } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];

        expect(collections).toMatchObject({
          us_stocks: {
            label: "US stocks",
            emoji: "🇺🇸",
            assets: expect.arrayContaining([
              "equities_american_airlines",
              "equities_apple",
              "equities_abbvie",
              "equities_airbnb",
              "equities_abbott",
              "equities_adobe",
              "equities_amd",
              "equities_amgen",
              "equities_amazon",
              "equities_broadcom",
              "equities_american_express",
              "equities_boeing",
              "equities_bank_of_america",
              "equities_booking_holdings",
              "equities_blackrock",
              "equities_berkshire_hathaway",
              "equities_blackstone",
              "equities_citigroup",
              "equities_cadence",
              "equities_constellation_energy",
              "equities_comcast",
              "equities_coinbase",
              "equities_costco",
              "equities_salesforce",
              "equities_cisco",
              "equities_chevron",
              "equities_delta_airlines",
              "equities_doordash",
              "equities_dropbox",
              "equities_datadog",
              "equities_dell",
              "equities_disney",
              "equities_ebay",
              "equities_ford_motor",
              "equities_fedex",
              "equities_ge",
              "equities_gilead",
              "equities_alphabet",
              "equities_goldman_sachs",
              "equities_home_depot",
              "equities_robinhood",
              "equities_hubspot",
              "equities_ibm",
              "equities_intel",
              "equities_intuit",
              "equities_johnson_johnson",
              "equities_jpmorgan_chase",
              "equities_kraft_heinz",
              "equities_kkr_co",
              "equities_coca_cola",
              "equities_eli_lilly",
              "equities_cheniere",
              "equities_lowes",
              "equities_lyft",
              "equities_mastercard",
              "equities_marriott",
              "equities_mcdonalds",
              "equities_moodys",
              "equities_mondelez",
              "equities_meta",
              "equities_3m",
              "equities_monster",
              "equities_merck",
              "equities_morgan_stanley",
              "equities_microsoft",
              "equities_nextera",
              "equities_cloudflare",
              "equities_netflix",
              "equities_nike",
              "equities_nvidia",
              "equities_oracle",
              "equities_pepsico",
              "equities_pfizer",
              "equities_procter_gamble",
              "equities_pinterest",
              "equities_palantir_technologies",
              "equities_philip_morris",
              "equities_paypal",
              "equities_qualcomm",
              "equities_starbucks",
              "equities_charles_schwab",
              "equities_snap",
              "equities_snowflake",
              "equities_block",
              "equities_att",
              "equities_target",
              "equities_thermo_fisher",
              "equities_tmobile",
              "equities_tesla",
              "equities_united_airlines",
              "equities_uber",
              "equities_unitedhealth",
              "equities_ups",
              "equities_visa",
              "equities_verizon",
              "equities_warner_bros",
              "equities_wells_fargo",
              "equities_walmart",
              "equities_exxonmobil",
              "equities_zoom",
              "equities_altria",
              "equities_amc",
              "equities_bny_mellon",
              "equities_box",
              "equities_bristol_myers_squibb",
              "equities_bumble",
              "equities_cardinal_health",
              "equities_chewy",
              "equities_colgate",
              "equities_confluent",
              "equities_conocophillips",
              "equities_coursera",
              "equities_cvs",
              "equities_deere",
              "equities_digitalocean",
              "equities_docusign",
              "equities_dominion_energy",
              "equities_dominos",
              "equities_duolingo",
              "equities_equifax",
              "equities_estee_lauder",
              "equities_etsy",
              "equities_expedia",
              "equities_first_solar",
              "equities_general_dynamics",
              "equities_gitlab",
              "equities_guardant_health",
              "equities_hilton",
              "equities_hp",
              "equities_incyte",
              "equities_interactive_brokers",
              "equities_jefferies",
              "equities_kimberly_clark",
              "equities_kinder_morgan",
              "equities_marathon_digital",
              "equities_marvell_technology",
              "equities_metlife",
              "equities_moderna",
              "equities_mongodb",
              "equities_motorola",
              "equities_msci",
              "equities_nasdaq",
              "equities_newmont",
              "equities_northern_trust",
              "equities_peloton",
              "equities_phillips_66",
              "equities_prologis",
              "equities_qualys",
              "equities_quantumscape",
              "equities_regeneron",
              "equities_riot_platforms",
              "equities_roblox",
              "equities_ross_stores",
              "equities_sp_global",
              "equities_servicenow",
              "equities_sofi_technologies",
              "equities_state_street",
              "equities_t_rowe_price",
              "equities_texas_instruments",
              "equities_carlyle",
              "equities_new_york_times",
              "equities_twilio",
              "equities_udemy",
              "equities_union_pacific",
              "equities_vertex_pharma",
              "equities_zscaler"
            ])
          },
          uk_stocks: {
            label: "UK stocks",
            emoji: "🇬🇧",
            assets: expect.arrayContaining([
              "equities_arm",
              "equities_astrazeneca",
              "equities_barclays",
              "equities_bp",
              "equities_british_american_tobacco",
              "equities_diageo",
              "equities_gsk",
              "equities_hsbc",
              "equities_lloyds",
              "equities_natwest",
              "equities_rio_tinto",
              "equities_shell",
              "equities_unilever",
              "equities_vodafone"
            ])
          },
          china_stocks: {
            label: "Chinese stocks",
            emoji: "🇨🇳",
            assets: expect.arrayContaining(["equities_alibaba", "equities_baidu", "equities_nio"])
          },
          japan_stocks: {
            label: "Japanese stocks",
            emoji: "🇯🇵",
            assets: expect.arrayContaining([
              "equities_mufg",
              "equities_nomura",
              "equities_toyota",
              "equities_sony"
            ])
          },
          eu_stocks: {
            label: "European stocks",
            emoji: "🇪🇺",
            assets: expect.arrayContaining([
              "equities_biontech",
              "equities_ing",
              "equities_nokia",
              "equities_novartis",
              "equities_novo_nordisk",
              "equities_philips",
              "equities_ryanair",
              "equities_santander",
              "equities_sap",
              "equities_totalenergies"
            ])
          },
          big_tech: {
            label: "Big tech",
            emoji: "💪",
            assets: expect.arrayContaining([
              "equities_microsoft",
              "equities_amazon",
              "equities_nvidia",
              "equities_apple",
              "equities_netflix",
              "equities_alphabet",
              "equities_meta",
              "equities_adobe",
              "equities_salesforce",
              "equities_oracle",
              "equities_ibm",
              "equities_cisco",
              "equities_intel"
            ])
          },
          ai_robotics: {
            label: "AI & robotics",
            emoji: "🤖",
            assets: expect.arrayContaining([
              "equities_alphabet",
              "equities_amazon",
              "equities_nvidia",
              "equities_tesla",
              "equities_amd",
              "equities_qualcomm",
              "equities_broadcom"
            ])
          },
          cloud_computing: {
            label: "Cloud computing",
            emoji: "☁️",
            assets: expect.arrayContaining([
              "equities_alphabet",
              "equities_amazon",
              "equities_microsoft",
              "equities_dropbox",
              "equities_twilio",
              "equities_zscaler",
              "equities_box",
              "equities_gitlab",
              "equities_qualys",
              "equities_alibaba",
              "equities_dell",
              "equities_servicenow",
              "equities_cloudflare",
              "equities_ibm"
            ])
          },
          banks: {
            label: "Banks",
            emoji: "🏦",
            assets: expect.arrayContaining([
              "equities_goldman_sachs",
              "equities_citigroup",
              "equities_jpmorgan_chase",
              "equities_morgan_stanley",
              "equities_bank_of_america",
              "equities_santander",
              "equities_nomura",
              "equities_natwest",
              "equities_mufg",
              "equities_lloyds",
              "equities_jefferies",
              "equities_hsbc",
              "equities_bny_mellon",
              "equities_barclays"
            ])
          },
          crypto_currencies: {
            label: "Crypto companies",
            emoji: "🤑",
            assets: expect.arrayContaining([
              "equities_coinbase",
              "equities_marathon_digital",
              "equities_riot_platforms"
            ])
          },
          fighting_cancer: {
            label: "Fighting cancer",
            emoji: "⚔️",
            assets: expect.arrayContaining([
              "equities_bristol_myers_squibb",
              "equities_novartis",
              "equities_eli_lilly",
              "equities_regeneron",
              "equities_gilead",
              "equities_abbvie",
              "equities_astrazeneca",
              "equities_biontech",
              "equities_guardant_health",
              "equities_johnson_johnson",
              "equities_merck",
              "equities_pfizer"
            ])
          },
          biotech: {
            label: "Biotech",
            emoji: "🧬",
            assets: expect.arrayContaining([
              "equities_vertex_pharma",
              "equities_amgen",
              "equities_gilead",
              "equities_moderna",
              "equities_regeneron",
              "equities_astrazeneca",
              "equities_johnson_johnson",
              "equities_incyte",
              "equities_abbott",
              "equities_eli_lilly",
              "equities_abbvie",
              "equities_gsk"
            ])
          },
          pharma: {
            label: "Pharma",
            emoji: "💊",
            assets: expect.arrayContaining([
              "equities_eli_lilly",
              "equities_johnson_johnson",
              "equities_novo_nordisk",
              "equities_merck",
              "equities_abbvie",
              "equities_novartis",
              "equities_regeneron",
              "equities_astrazeneca",
              "equities_pfizer",
              "equities_amgen",
              "equities_bristol_myers_squibb",
              "equities_gilead",
              "equities_gsk",
              "equities_biontech",
              "equities_moderna"
            ])
          },
          female_ceos: {
            label: "Female CEOs",
            emoji: "👩‍💼‍️",
            assets: expect.arrayContaining([
              "equities_amd",
              "equities_bumble",
              "equities_citigroup",
              "equities_cvs",
              "equities_general_dynamics",
              "equities_gsk",
              "equities_merck",
              "equities_nasdaq",
              "equities_natwest",
              "equities_oracle",
              "equities_new_york_times",
              "equities_ups",
              "equities_vertex_pharma",
              "equities_vodafone"
            ])
          }
        });
      });
    });

    describe("popularAssetsSection", () => {
      // Wednesday
      const TODAY = new Date("2024-01-10");

      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);

        const bestMovers = [
          "equities_microsoft",
          "equities_amazon",
          "equities_apple",
          "equities_nvidia",
          "equities_meta",
          "equities_tesla",
          "equities_berkshire_hathaway",
          "equities_visa"
        ] as investmentUniverseConfig.AssetType[];

        const worstMovers = [
          "equities_marriott",
          "equities_merck",
          "equities_mondelez",
          "equities_monster",
          "equities_moodys",
          "equities_nextera",
          "equities_oracle",
          "equities_philip_morris"
        ] as investmentUniverseConfig.AssetType[];

        // Create top movers
        await generateTopMovers(bestMovers, worstMovers, new Date("2024-01-09"));
      });
      afterEach(async () => await clearDb());

      it("should return correct popular assets when today is a weekend day", async () => {
        const popularAssets = await generatePopularAssets(new Date("2024-01-09"));

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(popularAssetsSection).toEqual(popularAssets);
      });

      it("should not return popular assets that weren't traded in the last 7 days (starting from yesterday)", async () => {
        const [popularAssets] = await Promise.all([
          generatePopularAssets(new Date("2024-01-09")),
          /**
           * Create some asset transactions that are the most traded, but outside of the desired "7-day" range
           */
          generateAssetTransactionWithNthOrders("equities_apple", new Date("2024-01-01"), 4), // before the "7 days" range
          generateAssetTransactionWithNthOrders("equities_cloudflare", new Date("2024-01-10"), 4), // today
          generateAssetTransactionWithNthOrders("equities_jpmorgan_chase", new Date("2024-11-24"), 4) // in 10 months
        ]);

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(popularAssetsSection).toEqual(popularAssets);
      });

      it("should not return EU assets in the UK company entity asset discovery data", async () => {
        const [popularAssets] = await Promise.all([
          generatePopularAssets(new Date("2024-01-09")),
          /**
           * Create some asset transactions that are the most traded, but for an asset that is EU-only
           */
          generateAssetTransactionWithNthOrders("government_bonds_uk_invesco", new Date("2024-01-09"), 4) // This is a EU-only asset!
        ]);

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(popularAssetsSection).toEqual(popularAssets);
      });

      it("should not return popular assets that were gifted", async () => {
        const [popularAssets] = await Promise.all([
          generatePopularAssets(new Date("2024-01-09")),
          generateGiftedAssetTransactionWithOrders("equities_3m", new Date("2024-01-02")),
          generateGiftedAssetTransactionWithOrders("equities_3m", new Date("2024-01-02"))
        ]);

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(popularAssetsSection).toEqual(popularAssets);
      });

      it("should throw an error if no popular assets were found", async () => {
        jest.spyOn(CloudflareService.Instance, "getDataFromFile").mockResolvedValue([]);

        await expect(AssetDiscoveryDataCronService.updateAssetDiscoveryData()).rejects.toThrow(
          new Error("Invalid asset discovery data!")
        );
      });

      it("should return correct popular assets when popular asset override from cloudflare exists", async () => {
        const popularAssetOverrides: investmentUniverseConfig.AssetType[] = [
          "equities_american_airlines",
          "equities_adobe"
        ];
        jest.spyOn(CloudflareService.Instance, "getDataFromFile").mockResolvedValue(popularAssetOverrides);
        const popularAssets = await generatePopularAssets(new Date("2024-01-09"));

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(popularAssetsSection).toEqual([...popularAssetOverrides, ...popularAssets]);
      });

      it("should not return etfs in popular assets", async () => {
        const [popularAssets] = await Promise.all([
          generatePopularAssets(new Date("2024-01-09")),
          generateAssetTransactionWithNthOrders("equities_global", new Date("2024-01-09"), 25) // before the "7 days" range
        ]);

        await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
        const { popularAssetsSection } = MockCloudflareService.kvStorage[KvNamespaceKeys.ASSET_DISCOVERY_DATA_UK];
        expect(popularAssetsSection).toEqual(popularAssets);
      });
    });
  });
});
