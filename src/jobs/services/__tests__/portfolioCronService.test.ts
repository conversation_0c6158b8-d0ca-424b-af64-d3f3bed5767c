import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAccount,
  buildAssetTransaction,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildOrder,
  buildPortfolio,
  buildUser
} from "../../../tests/utils/generateModels";
import PortfolioCronService from "../portfolioCronService";
import { CurrencyEnum, WealthkernelService } from "../../../external-services/wealthkernelService";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../../models/Portfolio";
import { RedisClientService } from "../../../loaders/redis";
import { KycStatusEnum } from "../../../models/User";
import DateUtil from "../../../utils/dateUtil";
import { ProviderEnum } from "../../../configs/providersConfig";
import { IntraDayPortfolioTickerDocument } from "../../../models/IntraDayTicker";

describe("PortfolioCronService", () => {
  beforeAll(async () => await connectDb("PortfolioCronService"));
  afterAll(async () => await closeDb());

  describe("cacheAllPortfolioMWRRsAndUpByValues", () => {
    let portfolioTicker: IntraDayPortfolioTickerDocument;
    let portfolioWithHoldings: PortfolioDocument;
    let portfolioWithoutHoldings: PortfolioDocument;
    let portfolioWithZeroValueHoldings: PortfolioDocument;
    let portfolioLegacyVirtual: PortfolioDocument;

    beforeAll(async () => {
      jest.resetAllMocks();
      jest.spyOn(RedisClientService.Instance, "set");

      const user = await buildUser({});
      portfolioWithHoldings = await buildPortfolio({
        owner: user.id,
        holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 206.69 })],
        mode: PortfolioModeEnum.REAL
      });
      portfolioWithoutHoldings = await buildPortfolio({
        owner: user.id,
        holdings: [],
        mode: PortfolioModeEnum.REAL
      });
      portfolioWithZeroValueHoldings = await buildPortfolio({
        owner: user.id,
        holdings: [await buildHoldingDTO(true, "real_estate_uk", 0.0001, { price: 10 })],
        mode: PortfolioModeEnum.REAL
      });
      portfolioLegacyVirtual = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL"
      });

      // portfolio with holdings setup
      portfolioTicker = await buildIntraDayPortfolioTicker({
        portfolio: portfolioWithHoldings.id,
        pricePerCurrency: { GBP: 206.69 },
        timestamp: new Date(2021, 10, 25)
      });
      const ordersConfig = [
        {
          side: "Buy",
          amount: 50,
          updatedAt: new Date(2021, 7, 18)
        },
        {
          side: "Buy",
          amount: 50,
          updatedAt: new Date(2021, 8, 10)
        },
        {
          side: "Buy",
          amount: 50,
          updatedAt: new Date(2021, 8, 29)
        },
        {
          side: "Buy",
          amount: 50.14,
          updatedAt: new Date(2021, 10, 2)
        }
      ];

      const transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolioWithHoldings.id,
        status: "Settled"
      });
      const orders = await Promise.all(
        ordersConfig.map(({ side, amount, updatedAt }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: updatedAt
              }
            },
            filledAt: updatedAt,
            transaction: transaction.id,
            side: side as "Buy" | "Sell",
            consideration: {
              amount: amount * 100,
              currency: CurrencyEnum.GBP
            },
            updatedAt
          })
        )
      );
      transaction.orders = orders.map((order) => order.id);
      await transaction.save();

      await PortfolioCronService.cacheAllPortfolioMWRRsAndUpByValues();
    });
    afterAll(async () => await clearDb());

    it("should cache mwrr for all portfolios with holdings", async () => {
      const cachedMwrr = await RedisClientService.Instance.get(`portfolios:mwrr:${portfolioWithHoldings.id}`);
      expect(cachedMwrr).toMatchObject(
        expect.objectContaining({
          "1w": 0,
          "1m": 0,
          "3m": 0,
          "6m": 0,
          "1y": 0,
          max: 0.03272709103627453
        })
      );
    });

    it("should cache up by values for all portfolios with holdings", async () => {
      const cachedMwrr = await RedisClientService.Instance.get(`portfolios:up_by:${portfolioWithHoldings.id}`);
      expect(cachedMwrr).toMatchObject(
        expect.objectContaining({
          "1w": 206.69,
          "1m": 206.69,
          "3m": 206.69,
          "6m": 206.69,
          "1y": 206.69,
          max: 6.55
        })
      );
    });

    it("should cache portfolio value at the time of mwrr caclulation for all portfolios that mwrr was cached", async () => {
      const cachedPortfolioValue = await RedisClientService.Instance.get(
        `portfolios:value_at_mwrr:${portfolioWithHoldings.id}`
      );
      expect(cachedPortfolioValue).toEqual(portfolioTicker.getPrice("GBP"));
    });

    it("should cache portfolio value at the time of up by caclulation", async () => {
      const cachedPortfolioValue = await RedisClientService.Instance.get(
        `portfolios:value_at_up_by:${portfolioWithHoldings.id}`
      );
      expect(cachedPortfolioValue).toEqual(portfolioTicker.getPrice("GBP"));
    });

    it("ignore portfolios without holdings", async () => {
      expect(await RedisClientService.Instance.get(`portfolios:mwrr:${portfolioWithoutHoldings.id}`)).toEqual(
        undefined
      );
      expect(RedisClientService.Instance.set).not.toHaveBeenCalledWith(
        `portfolios:mwrr:${portfolioWithoutHoldings.id}`,
        expect.anything()
      );
    });

    it("ignore portfolios with holdings that sum to a zero portfolio value", async () => {
      expect(
        await RedisClientService.Instance.get(`portfolios:mwrr:${portfolioWithZeroValueHoldings.id}`)
      ).toEqual(undefined);
      expect(RedisClientService.Instance.set).not.toHaveBeenCalledWith(
        `portfolios:mwrr:${portfolioWithZeroValueHoldings.id}`,
        expect.anything()
      );
    });

    it("ignore legacy VIRTUAL portfolios", async () => {
      expect(await RedisClientService.Instance.get(`portfolios:mwrr:${portfolioLegacyVirtual.id}`)).toEqual(
        undefined
      );
      expect(RedisClientService.Instance.set).not.toHaveBeenCalledWith(
        `portfolios:mwrr:${portfolioLegacyVirtual.id}`,
        expect.anything()
      );
    });
  });

  describe("createAllWkPortfolios", () => {
    describe("and portfolio already has Wealthkernel ID", () => {
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5)
        });
        const account = await buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          account: account.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        await PortfolioCronService.createAllWkPortfolios();
      });

      it("should NOT create WK portfolio", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.providers.wealthkernel.id).toBe(portfolio.providers.wealthkernel.id);
      });
    });

    describe("and portfolio does not have Wealthkernel ID but the user is KYC pending", () => {
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PENDING
        });

        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        await PortfolioCronService.createAllWkPortfolios();
      });

      it("should NOT create WK portfolio", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.providers?.wealthkernel?.id).toBeUndefined();
      });
    });

    describe("and portfolio does not have Wealthkernel ID and the user is KYC passed, but they submitted info < 10 minutes ago", () => {
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5)
        });
        const account = await buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          account: account.id,
          holdings: []
        });

        await PortfolioCronService.createAllWkPortfolios();
      });

      it("should NOT create WK portfolio", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.providers?.wealthkernel?.id).toBeUndefined();
      });
    });

    describe("and portfolio does not have Wealthkernel ID and the user is KYC passed and they submitted info > 10 minutes ago", () => {
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolios").mockResolvedValue([]);
        jest.spyOn(WealthkernelService.UKInstance, "createPortfolio").mockResolvedValue({ id: WK_PORTFOLIO_ID });

        const user = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfHoursAgo(TODAY, 1)
        });
        const account = await buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        portfolio = await buildPortfolio({
          owner: user.id,
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          mode: PortfolioModeEnum.REAL,
          account: account.id,
          holdings: []
        });

        await PortfolioCronService.createAllWkPortfolios();
      });

      it("should create WK portfolio", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.providers.wealthkernel.id).toBe(WK_PORTFOLIO_ID);
      });
    });
  });
});
