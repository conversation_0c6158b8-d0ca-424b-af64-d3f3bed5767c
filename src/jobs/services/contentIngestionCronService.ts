import {
  ContentEntry,
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument
} from "../../models/ContentEntry";
import FinimizeService, {
  FinimizeContentPieceType,
  FinimizeContentTypeEnum
} from "../../external-services/finimizeService";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import DateUtil from "../../utils/dateUtil";
import ContentfulManagementService, {
  ContentEntryContentfulType
} from "../../external-services/contentfulManagementService";
import { removeMarkdownLinksFromString } from "../../utils/stringUtil";
import logger from "../../external-services/loggerService";
import ContentEntryService from "../../services/contentEntryService";
import { captureException } from "@sentry/node";
import { ProviderEnum } from "../../configs/providersConfig";
import AnalystInsightTitleFormatter from "../../lib/analystInsightTitleFormatter";

const ANALYST_INSIGHTS_TRACKING_DAYS = 5;
const QUICK_TAKE_NOTIFY_AT_HOUR = 16;
const QUICK_TAKE_NOTIFY_AT_MINUTES = 30;
const ANALYSIS_NOTIFY_AT_HOUR = 7;
const ANALYSIS_NOTIFY_AT_MINUTES = 15;
const WEEKLY_REVIEW_NOTIFY_AT_HOUR = 9;
const WEEKLY_REVIEW_NOTIFY_AT_MINUTES = 30;

const FINIMIZE_TYPE_TO_CONTENT_TYPE: Record<FinimizeContentTypeEnum, ContentEntryContentTypeEnum> = {
  [FinimizeContentTypeEnum.INSIGHT]: ContentEntryContentTypeEnum.ANALYSIS,
  [FinimizeContentTypeEnum.RESEARCH]: ContentEntryContentTypeEnum.ANALYSIS,
  [FinimizeContentTypeEnum.WEEKLY_BRIEF]: ContentEntryContentTypeEnum.WEEKLY_REVIEW,
  [FinimizeContentTypeEnum.QUICK_TAKE]: ContentEntryContentTypeEnum.QUICK_TAKE
};

export class ContentIngestionCronService {
  /**
   * @description
   * This method retrieves content from Finimize and creates content entries in our Database.
   */
  public static async createAnalystInsightContentEntries(
    contentIngestionTrackingDays = ANALYST_INSIGHTS_TRACKING_DAYS
  ): Promise<void> {
    await FinimizeService.executeForContent(
      {
        types: [
          // Type INSIGHT also fetches type RESEARCH
          FinimizeContentTypeEnum.INSIGHT,
          FinimizeContentTypeEnum.WEEKLY_BRIEF,
          FinimizeContentTypeEnum.QUICK_TAKE
        ],
        publishedAfter: DateUtil.getDateOfDaysAgo(new Date(Date.now()), contentIngestionTrackingDays)
      },
      ContentIngestionCronService._ingestFinimizeContentPieces
    );
  }

  /**
   * @description This methods retrieves content from Finimize and uploads it to Contentful.
   * The content entries are queried by the publishAt field, which is set to the date the content
   * should be published on Contentful.
   *
   * The method can also upload old content entries (as specified by publishedAt), as long as they don't
   * exist on Contentful, but does not search future scheduled entries for more than 60 minutes. This is needed for
   * failures on content upload to Contentful.
   */
  public static async uploadAnalystInsightsToContentful(): Promise<void> {
    const contentEntries = await ContentEntry.find({
      category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
      activeProviders: ProviderEnum.CONTENTFUL,
      "providers.finimize.id": { $exists: true },
      "providers.contentful.id": { $exists: false },
      publishAt: {
        $lte: DateUtil.getDateOfMinutesFromNow(60)
      }
    });

    for (const contentEntry of contentEntries) {
      await ContentIngestionCronService._uploadFinimizeContentToContentful(contentEntry);
    }
  }

  private static _containsFinimize(text: string): boolean {
    return text.toLowerCase().includes("finimize");
  }

  private static async _excludeStoredFinimizeContentPieces(
    finimizeContentPieces: FinimizeContentPieceType[]
  ): Promise<FinimizeContentPieceType[]> {
    if (!finimizeContentPieces?.length) return [];

    const finimizeProviderIds = finimizeContentPieces.map((contentPiece) => contentPiece.contentPieceId);

    const contentEntries = await ContentEntry.find({
      category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
      "providers.finimize.id": { $in: finimizeProviderIds }
    });

    const contentEntryByFinimizeId = contentEntries.reduce(
      (contentDict, contentEntry) => {
        contentDict[contentEntry.providers.finimize.id] = contentEntry;
        return contentDict;
      },
      {} as Record<string, ContentEntryDocument>
    );

    return finimizeContentPieces.filter((contentPiece) => !contentEntryByFinimizeId[contentPiece.contentPieceId]);
  }

  private static _flagContentEntryWithFinimizeReferences(piece: FinimizeContentPieceType): void {
    const hasFinimizeReference =
      ContentIngestionCronService._containsFinimize(piece.title) ||
      ContentIngestionCronService._containsFinimize(piece.subtitle) ||
      ContentIngestionCronService._containsFinimize(piece.summary) ||
      piece.blocks.some(
        (block) => block.type === "text" && ContentIngestionCronService._containsFinimize(block.textPlain)
      );

    if (hasFinimizeReference) {
      logger.info(`Finimize reference found in ${piece.title}`, {
        module: "ContentIngestionCronService",
        method: "_flagContentEntryWithFinimizeReferences",
        data: {
          contentPieceId: piece.contentPieceId,
          title: piece.title
        }
      });

      // Emit event when Finimize reference is found
      eventEmitter.emit(events.general.finimizeRefIdentified.eventId, {
        contentPieceId: piece.contentPieceId,
        title: piece.title
      });
    }
  }

  private static _transformFinimizeContentPiece(
    finimizeContentPiece: FinimizeContentPieceType
  ): ContentEntryContentfulType {
    const contentMarkdown = finimizeContentPiece.blocks
      .filter((block) => block.type !== "quote")
      .map((block) => {
        if (block.type === "text") {
          return removeMarkdownLinksFromString(block.textMarkdown);
        } else if (block.type === "image") {
          return `![${block.caption}](${block.image.full})\n`;
        }
      })
      .join("\n");

    const wordCount = contentMarkdown.split(/\s+/).length;
    const readingTime = `${Math.ceil(wordCount / 200)} minutes`;

    return {
      slug: finimizeContentPiece.slug,
      title: finimizeContentPiece.title,
      publishedAt: finimizeContentPiece.publishedAt,
      subtitle: finimizeContentPiece.subtitle,
      readingTime: readingTime,
      summary: finimizeContentPiece.summary,
      category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
      contentType: FINIMIZE_TYPE_TO_CONTENT_TYPE[finimizeContentPiece.contentPieceTypeId],
      headerImage: finimizeContentPiece.headerImage.full,
      bannerImage: finimizeContentPiece.headerImage.full,
      content: contentMarkdown,
      notificationTitle: finimizeContentPiece.notificationTitleText,
      notificationBody: finimizeContentPiece.notificationBodyText
    };
  }

  private static async _uploadFinimizeContentToContentful(contentEntry: ContentEntryDocument): Promise<void> {
    try {
      logger.info(`Uploading finimize data to contentful for ${contentEntry.id}`, {
        module: "ContentIngestionCronService",
        method: "_uploadFinimizeContentToContentful"
      });

      const finimizeContentPiece = await FinimizeService.retrieveContentPiece({
        contentPieceId: contentEntry.providers.finimize.id,
        publishedAt: contentEntry.providers.finimize.publishedAt,
        type: contentEntry.providers.finimize.contentType
      });

      ContentIngestionCronService._flagContentEntryWithFinimizeReferences(finimizeContentPiece);

      const contentfulEntryToCreate =
        ContentIngestionCronService._transformFinimizeContentPiece(finimizeContentPiece);

      const contentfulProviderData = await ContentfulManagementService.LearnHubInstance.createContentEntry(
        contentfulEntryToCreate,
        { publishAfterCreation: true }
      );

      await ContentEntry.findByIdAndUpdate(contentEntry._id, {
        "providers.contentful": {
          id: contentfulProviderData.id,
          spaceId: contentfulProviderData.spaceId,
          environmentId: contentfulProviderData.environmentId
        }
      });

      logger.info(`Finished uploading finimize data to contentful for ${contentEntry.id}`, {
        module: "ContentIngestionCronService",
        method: "_uploadFinimizeContentToContentful"
      });
    } catch (err) {
      captureException(err);
      logger.error(`Failed to store finimize content for ${contentEntry.id}`, {
        module: "ContentIngestionCronService",
        method: "_uploadFinimizeContentToContentful"
      });
    }
  }

  private static async _ingestFinimizeContentPieces(
    finimizeContentPieces: FinimizeContentPieceType[]
  ): Promise<void> {
    try {
      const finimizeContentPiecesToStore = (
        await ContentIngestionCronService._excludeStoredFinimizeContentPieces(finimizeContentPieces)
      ).filter((piece) => Object.values(FinimizeContentTypeEnum).includes(piece.contentPieceTypeId));

      if (!finimizeContentPiecesToStore.length) {
        logger.info("✅ No new Finimize content to ingest", {
          module: "ContentIngestionCronService",
          method: "_ingestFinimizeContentPieces"
        });
        return;
      }

      for (const piece of finimizeContentPiecesToStore) {
        const originalTitle = piece.title;
        let formattedTitle;

        try {
          formattedTitle = await AnalystInsightTitleFormatter.format(originalTitle);
        } catch (err) {
          captureException(err);
          // If title formatting fails, use the original title
          formattedTitle = originalTitle;
        }

        await ContentEntryService.createContentEntry({
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          contentType: FINIMIZE_TYPE_TO_CONTENT_TYPE[piece.contentPieceTypeId],
          activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
          shouldNotifyUsers: true,
          title: formattedTitle,
          publishAt: ContentIngestionCronService._getContentEntryPublishAtDate(piece),
          providers: {
            finimize: {
              id: piece.contentPieceId,
              publishedAt: new Date(piece.publishedAt),
              contentType: piece.contentPieceTypeId,
              originalTitle
            }
          }
        });
      }
    } catch (err) {
      captureException(err);
      logger.error("Failed to ingest Finimize content", {
        module: "ContentIngestionCronService",
        method: "_ingestFinimizeContentPieces"
      });
    }
  }

  /**
   * @description Returns the date at which the Finimize content should be published on Contentful.
   * @param finimizeContentPiece
   */
  private static _getContentEntryPublishAtDate(finimizeContentPiece: FinimizeContentPieceType): Date {
    const contentType = finimizeContentPiece.contentPieceTypeId;
    const finimizePublishedAt = new Date(finimizeContentPiece.publishedAt);

    if (contentType === FinimizeContentTypeEnum.QUICK_TAKE) {
      // Quick takes are provided by Finimize during morning hours and are published same day in the evening.
      return DateUtil.getDateForTime(finimizePublishedAt, {
        atHours: QUICK_TAKE_NOTIFY_AT_HOUR,
        atMinutes: QUICK_TAKE_NOTIFY_AT_MINUTES
      });
    } else if (
      contentType === FinimizeContentTypeEnum.INSIGHT ||
      contentType === FinimizeContentTypeEnum.RESEARCH
    ) {
      // Analyst insights are provided by Finimize during evening hours and are published next day in the morning.
      return DateUtil.getDateForTime(DateUtil.getDateAfterNdays(finimizePublishedAt, 1), {
        atHours: ANALYSIS_NOTIFY_AT_HOUR,
        atMinutes: ANALYSIS_NOTIFY_AT_MINUTES
      });
    } else if (contentType === FinimizeContentTypeEnum.WEEKLY_BRIEF) {
      // Weekly briefs are provided by Finimize either Friday or Saturday and are published Sunday morning.
      const dayOfWeek = finimizePublishedAt.getDay();
      const daysUntilSunday = dayOfWeek === 0 ? 0 : 7 - dayOfWeek;
      return DateUtil.getDateForTime(DateUtil.getDateAfterNdays(finimizePublishedAt, daysUntilSunday), {
        atHours: WEEKLY_REVIEW_NOTIFY_AT_HOUR,
        atMinutes: WEEKLY_REVIEW_NOTIFY_AT_MINUTES
      });
    }

    return finimizePublishedAt;
  }
}
