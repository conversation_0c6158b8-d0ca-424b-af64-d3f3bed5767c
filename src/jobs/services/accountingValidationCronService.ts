import "../../tmp-scripts/dependencies";

import { addBreadcrumb, captureMessage, captureException } from "@sentry/node";
import logger from "../../external-services/loggerService";
import { AccountingValidationService } from "../../services/accountingValidationService";

const VALIDATION_FROM_DATE = "2025-07-01";

export class AccountingValidationCronService {
  public static async runValidationOnDbWithLedger() {
    try {
      // Run all validation checks
      const results = await AccountingValidationService.validateAllDbWithLedger(VALIDATION_FROM_DATE);

      // Flatten all results for processing
      const allResults = [
        ...results.deposits,
        ...results.withdrawals,
        ...results.dividends,
        ...results.orders,
        ...results.rewards,
        ...results.gifts
      ];

      // Check for any invalid results
      const invalidResults = allResults.filter((result) => !result.isValid);

      if (invalidResults.length > 0) {
        logger.error("⚠️ Accounting validation discrepancies found", {
          module: "AccountingValidationCronService",
          method: "runValidation",
          data: {
            totalValidationChecks: allResults.length,
            invalidChecks: invalidResults.length,
            validChecks: allResults.length - invalidResults.length,
            fromDate: VALIDATION_FROM_DATE
          }
        });

        // Log each category of discrepancies to DataDog
        for (const result of invalidResults) {
          logger.error(`Accounting discrepancy in ${result.transactionType}`, {
            module: "AccountingValidationCronService",
            method: "runValidation",
            data: {
              transactionType: result.transactionType,
              dbTotalAmount: result.dbTotalAmount,
              ledgerTotalAmount: result.ledgerTotalAmount,
              difference: result.difference,
              transactionCount: result.transactionCount,
              ledgerEntryCount: result.ledgerEntryCount,
              discrepancyCount: result.discrepancies?.length || 0,
              fromDate: VALIDATION_FROM_DATE
            }
          });

          // Add breadcrumbs to Sentry for each discrepancy
          if (result.discrepancies && result.discrepancies.length > 0) {
            for (const discrepancy of result.discrepancies) {
              addBreadcrumb({
                type: "info",
                category: "accounting-validation",
                level: "error",
                message: `Discrepancy in ${result.transactionType}`,
                data: {
                  transactionId: discrepancy.transactionId,
                  dbAmount: discrepancy.dbAmount,
                  ledgerAmount: discrepancy.ledgerAmount,
                  difference: discrepancy.difference,
                  description: discrepancy.description,
                  transactionType: result.transactionType
                }
              });
            }
          }
        }

        // Send overall discrepancy message to Sentry
        captureMessage(`Accounting validation found ${invalidResults.length} discrepancies`, {
          level: "error",
          extra: {
            totalValidationChecks: allResults.length,
            invalidChecks: invalidResults.length,
            validChecks: allResults.length - invalidResults.length,
            invalidResults: invalidResults.map((result) => ({
              transactionType: result.transactionType,
              dbTotalAmount: result.dbTotalAmount,
              ledgerTotalAmount: result.ledgerTotalAmount,
              difference: result.difference,
              transactionCount: result.transactionCount,
              discrepancyCount: result.discrepancies?.length || 0
            }))
          }
        });

        logger.error("❌ Accounting validation completed with discrepancies", {
          module: "AccountingValidationCronService",
          method: "runValidation",
          data: {
            totalValidationChecks: allResults.length,
            invalidChecks: invalidResults.length,
            validChecks: allResults.length - invalidResults.length,
            fromDate: VALIDATION_FROM_DATE
          }
        });
      } else {
        logger.info("✅ Accounting validation completed successfully - no discrepancies found", {
          module: "AccountingValidationCronService",
          method: "runValidation",
          data: {
            totalValidationChecks: allResults.length,
            allChecksValid: true,
            fromDate: VALIDATION_FROM_DATE
          }
        });
      }

      // Log summary of all validation results
      logger.info("📊 Accounting validation summary", {
        module: "AccountingValidationCronService",
        method: "runValidation",
        data: {
          deposits: {
            total: results.deposits.length,
            valid: results.deposits.filter((r) => r.isValid).length,
            invalid: results.deposits.filter((r) => !r.isValid).length
          },
          withdrawals: {
            total: results.withdrawals.length,
            valid: results.withdrawals.filter((r) => r.isValid).length,
            invalid: results.withdrawals.filter((r) => !r.isValid).length
          },
          dividends: {
            total: results.dividends.length,
            valid: results.dividends.filter((r) => r.isValid).length,
            invalid: results.dividends.filter((r) => !r.isValid).length
          },
          orders: {
            total: results.orders.length,
            valid: results.orders.filter((r) => r.isValid).length,
            invalid: results.orders.filter((r) => !r.isValid).length
          },
          rewards: {
            total: results.rewards.length,
            valid: results.rewards.filter((r) => r.isValid).length,
            invalid: results.rewards.filter((r) => !r.isValid).length
          },
          gifts: {
            total: results.gifts.length,
            valid: results.gifts.filter((r) => r.isValid).length,
            invalid: results.gifts.filter((r) => !r.isValid).length
          },
          fromDate: VALIDATION_FROM_DATE
        }
      });
    } catch (err) {
      captureException(err);
      logger.error("💥 Error during accounting validation", {
        module: "AccountingValidationCronService",
        method: "runValidation",
        data: { error: err, fromDate: VALIDATION_FROM_DATE }
      });
    }
  }
}
