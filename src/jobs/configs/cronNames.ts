export enum CronJobNameEnum {
  // staging only
  C<PERSON>AN_TRANSACTIONS = "clean-transactions",

  // both prod + staging
  INTRA_DAY_ASSET_TICKER = "intra-day-asset-ticker",
  DAILY_SUMMARY_SNAPSHOT = "daily-summary-snapshot",
  ACCOUNTING_CASH_BALANCE_SNAPSHOT = "accounting-cash-balance-snapshot",
  ACCOUNTING_VALIDATION = "accounting-validation",
  CACHE_FX_RATES = "cache-fx-rates",
  PORTFOLIO_TICKER = "portfolio-ticker",
  ASSET_FUNDAMENTALS_DATA = "asset-fundamentals-data",
  ASSET_HISTORICAL_PRICES = "asset-historical-prices",
  SUNDOWN_DIGEST = "sundown-digest",
  CACHE_INDEX_PRICES = "cache-index-prices",
  STORE_INDEX_PRICES = "store-index-prices",
  ONGOING = "ongoing",
  ORDER_SUBMISSION = "order-submission",
  SYNC_TRANSACTIONS_WITH_ORDERS = "sync-transactions-with-orders",
  SAVINGS_DIVIDENDS = "savings-dividends",
  DAILY_SAVINGS_PRODUCT_TICKER = "daily-savings-product-ticker",
  DAILY_PORTFOLIO_SAVINGS_TICKER = "daily-portfolio-savings-ticker",
  SAVINGS_PRODUCT_DATA_UPDATE_CHECK = "savings-product-data-update-check",
  CHARGE_CREATION = "charge-creation",
  STRIPE_PAYMENT_REJECTION = "stripe-payment-rejection",
  INTRA_DAY_PORTFOLIO_TICKER = "intra-day-portfolio-ticker",
  LEARN_NEWS = "learn-news",

  // prod only
  ACCOUNTING_REPORT = "accounting-report",
  WK_CASH_HOLDINGS_MISMATCH = "wk-cash-holdings-mismatch",
  CACHE_ASSETS_WEEKLY_RETURNS = "cache-assets-weekly-returns",
  CACHE_PORTFOLIO_MWRR_AND_UP_BY_VALUES = "cache-portfolio-mwrr",
  CACHE_SENTIMENT_SCORES = "cache-sentiment-scores",
  CUSTODY_CHARGES_CREATION = "custody-charges-creation",
  ASSET_DISCOVERY_DATA = "asset-discovery-data",
  DAILY_RECAP_CREATION = "daily-recap-creation",
  ETF_HOLDINGS_LOGOS = "etf-holding-logos",
  CONTENT_INGESTION = "content-ingestion",
  NOTIFICATION = "notification",
  GIFT_CAPABILITIES = "gift-capabilities",
  SAVINGS_PRODUCTS_DATA = "savings-products-data",
  INVESTMENT_DIVIDENDS = "investment-dividends",
  CHARGE_SUBMISSION_EU = "charge-submission-eu",
  CHARGE_SUBMISSION_UK = "charge-submission-uk",
  NIGHTLY = "nightly",
  STOCK_SPLIT = "stock-split",
  WH_DIVIDEND_CREATION = "wh-dividend-creation",
  REWARDS = "rewards",
  USER_DATA_REQUESTS = "user-data-requests",
  WH_DIVIDEND_BONUS = "wh-dividend-bonus",
  AUTOMATED_INVESTMENTS = "automated-investments",
  ASSET_NEWS = "asset-news",
  AUTOMATED_REBALANCES = "automated-rebalances",
  PUBLIC_ASSET_DATA = "public-asset-data",
  RISK_ASSESSMENT = "risk-assessment",
  DOWNGRADE_SUBSCRIPTIONS = "downgrade-subscriptions",
  TRANSACTION_MONITOR = "transaction-monitor",
  GIFTS = "gifts",
  STALE_ASSET_TICKER = "stale-asset-ticker",
  WEALTHYBITES_DATA_PREP = "wealthybites-data-prep",
  WEALTHYBITES_EMAIL_SEND = "wealthybites-email-send",
  MONGO_DUMP = "mongo-dump"
}
