import { TransactionCronService } from "../services/transactionCronService";
import logger from "../../external-services/loggerService";
import RewardService from "../../services/rewardService";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class SyncTransactionsWithOrdersCronJob extends CronJob {
  cronName = CronJobNameEnum.SYNC_TRANSACTIONS_WITH_ORDERS;

  /**
   * @description Cron for transaction syncing from wealthkernel. This does not include asset
   * transactions as those are synced from the order webhook.
   */
  async processFn(): Promise<void> {
    logger.info("👩‍🚀 Initiating revert reward transaction tasks...", { module: `cron:${this.cronName}` });
    await TransactionService.syncRevertRewardTransactionsFromWK();
    logger.info("✅ Completed revert reward transaction tasks!", { module: `cron:${this.cronName}` });

    logger.info("🙏 Syncing custody charges...", { module: `cron:${this.cronName}` });
    await TransactionService.syncCustodyChargeTransactionsFromWK();
    logger.info("✅ Completed syncing custody charges!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🙏 Syncing saving topups transactions...", { module: `cron:${this.cronName}` });
    await TransactionCronService.syncSavingsTopupTransactionsFromWK();
    logger.info("✅ Completed syncing saving topups transactions!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🙏 Syncing saving withdrawals transactions...", { module: `cron:${this.cronName}` });
    await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
    logger.info("✅ Completed syncing saving withdrawals transactions!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🚀 Processing 'PendingTopUp' Savings Withdrawals...", { module: `cron:${this.cronName}` });
    await TransactionCronService.processPendingTopUpSavingsWithdrawals();
    logger.info("✅ Completed processing 'PendingTopUp' Savings Withdrawals!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🚀 Syncing Savings dividends with linked Savings topup...", { module: `cron:${this.cronName}` });
    await TransactionCronService.syncSavingsDividendsLinkedToSavingsTopup();
    logger.info("✅ Completed syncing Savings dividends with linked Savings topup!", {
      module: `cron:${this.cronName}`
    });
  }
}

new SyncTransactionsWithOrdersCronJob().run();
