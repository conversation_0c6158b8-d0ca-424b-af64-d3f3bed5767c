import logger from "../../external-services/loggerService";
import SubscriptionService from "../../services/subscriptionService";
import DateUtil from "../../utils/dateUtil";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

const CHARGE_MONTH_OVERRIDE = process.env.CHARGE_MONTH_OVERRIDE;

class CustodyChargesCreationCronJob extends CronJob {
  cronName = CronJobNameEnum.CUSTODY_CHARGES_CREATION;

  /**
   * @description Cron that charges custody fees on the last work day of the month.
   * Essentially, we create all the custody charge transaction documents and their corresponding orders.
   *
   * Custody charging is enabled for UK portfolios only.
   */
  async processFn(): Promise<void> {
    if (DateUtil.isLastUKWorkDayOfThisMonth() || CHARGE_MONTH_OVERRIDE) {
      logger.info("✅ It is the last work day of the month, proceeding with charging subscribed users...", {
        module: `cron:${this.cronName}`
      });

      logger.info("💰 Creating custody charges...", { module: `cron:${this.cronName}` });
      await SubscriptionService.createCustodyFeeCharges(CHARGE_MONTH_OVERRIDE);
      logger.info("✅ Completed creating custody charges!", {
        module: `cron:${this.cronName}`
      });
    } else {
      logger.info("💔 It is not the last work day of the month, we're not creating charges yet!", {
        module: `cron:${this.cronName}`
      });
    }
  }
}

new CustodyChargesCreationCronJob().run();
