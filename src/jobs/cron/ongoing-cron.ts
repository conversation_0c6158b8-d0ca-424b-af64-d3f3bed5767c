import logger from "../../external-services/loggerService";
import AccountService from "../../services/accountService";
import AddressService from "../../services/addressService";
import BankAccountService from "../../services/bankAccountService";
import PortfolioService from "../../services/portfolioService";
import { TransactionService } from "../../services/transactionService";
import UserService from "../../services/userService";
import { envIsProd } from "../../utils/environmentUtil";
import MandateCronService from "../services/mandateCronService";
import PortfolioCronService from "../services/portfolioCronService";
import RewardService from "../../services/rewardService";
import CronJob from "./cronJob";
import KycOperationService from "../../services/kycOperationService";
import AccountCronService from "../services/accountCronService";
import { TransactionCronService } from "../services/transactionCronService";
import UserCronService from "../services/userCronService";
import BankAccountCronService from "../services/bankAccountCronService";
import { CronJobNameEnum } from "../configs/cronNames";
import WalletCronService from "../services/walletCronService";
import CreditTicketCronService from "../services/creditTicketCronService";

class OngoingCronJob extends CronJob {
  cronName = CronJobNameEnum.ONGOING;

  /**
   * @description Cron for small tasks that need to be checked in an ongoing basis.
   */
  async processFn(): Promise<void> {
    logger.info("🏦 Initiating deposit tasks...", { module: `cron:${this.cronName}` });
    await TransactionService.syncPendingTruelayerDeposits();
    await TransactionService.createAcquisitionPaymentsForEligibleDevengoDeposits();
    await TransactionService.createCollectionPaymentsForEligibleDevengoDeposits();
    await TransactionCronService.createPaymentsForEligiblePayouts();
    await TransactionService.createEligibleWealthkernelDeposits();
    await TransactionService.syncPendingWealthkernelDeposits();
    logger.info("✅ Completed deposit tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating withdrawal tasks...", { module: `cron:${this.cronName}` });
    await TransactionService.createCollectionPaymentsForEligibleDevengoWithdrawals();
    logger.info("✅ Completed withdrawal tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating lifetime subscription charge tasks...", { module: `cron:${this.cronName}` });
    await TransactionService.syncPendingTruelayerCharges();
    logger.info("✅ Completed lifetime subscription charge tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating tasks for asset transactions that are pending deposits...", {
      module: `cron:${this.cronName}`
    });
    await TransactionCronService.processAssetTransactionsPendingDeposit();
    await TransactionService.convertUsersThatHadTransactionsPendingDeposit();
    logger.info("✅ Completed tasks for asset transactions that were pending deposits", {
      module: `cron:${this.cronName}`
    });

    logger.info("🏦 Initiating tasks for asset transactions that are pending gifts...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.syncTransactionsPendingGifts();
    await TransactionService.convertUsersThatHadTransactionsPendingGift();
    logger.info("✅ Completed tasks for asset transactions that were pending gifts", {
      module: `cron:${this.cronName}`
    });

    logger.info("🏦 Initiating tasks for rebalance transactions...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.processRebalanceTransactions();
    logger.info("✅ Completed tasks for rebalance transactions...", {
      module: `cron:${this.cronName}`
    });

    logger.info("🏦 Syncing direct debit payments with WK...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.syncWealthkernelDirectDebitPayments();
    logger.info("✅ Completed syncing direct debit payments with WK...", {
      module: `cron:${this.cronName}`
    });

    logger.info("🏦 Initiating wealthkernel party tasks...", {
      module: `cron:${this.cronName}`
    });
    await UserService.createAllWkParties();
    logger.info("✅ Completed wealthkernel party tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating Wealthkernel bank account tasks...", { module: `cron:${this.cronName}` });
    await BankAccountService.createAllWkBankAccounts();
    await BankAccountService.syncAllWkBankAccounts();
    logger.info("✅ Completed Wealthkernel bank account tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating Devengo wallet tasks...", { module: `cron:${this.cronName}` });
    await WalletCronService.createAllDevengoWallets();
    await WalletCronService.syncAllDevengoWallets();
    logger.info("✅ Completed Devengo wallet tasks", { module: `cron:${this.cronName}` });

    logger.info("🏠 Creating Wealthkernel addresses...", { module: `cron:${this.cronName}` });
    await AddressService.createAllWkAddresses();
    logger.info("✅ Created Wealthkernel addresses", { module: `cron:${this.cronName}` });

    logger.info("💼 Creating wealthkernel w8ben forms...", { module: `cron:${this.cronName}` });
    await UserService.completeAllWkW8BenForms();
    await UserService.createAllWkW8BenForms();
    logger.info("✅ Wealthkernel w8ben forms created", { module: `cron:${this.cronName}` });

    logger.info("🖥️ Initiating Wealthkernel account tasks...", { module: `cron:${this.cronName}` });
    await AccountCronService.createAllWkAccounts();
    await AccountService.syncAllPendingWkAccounts();
    logger.info("✅ Completed Wealthkernel account tasks", { module: `cron:${this.cronName}` });

    logger.info("💼 Creating wealthkernel portfolios for verified users...", { module: `cron:${this.cronName}` });
    await PortfolioCronService.createAllWkPortfolios();
    await PortfolioService.syncAllWkPortfolios();
    logger.info("✅ Wealthkernel portfolios created", { module: `cron:${this.cronName}` });

    logger.info("💼 Creating GoCardless mandate entries for verified users...", {
      module: `cron:${this.cronName}`
    });
    await MandateCronService.createAllWkMandates();
    await MandateCronService.createAllGoCardlessMandates();
    logger.info("✅ GoCardless mandate entries created", { module: `cron:${this.cronName}` });

    logger.info("💼 Syncing pending wealthkernel mandates...", { module: `cron:${this.cronName}` });
    await MandateCronService.syncPendingWkMandates();
    logger.info("✅ Wealthkernel pending mandates synced", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating Stripe entity creation tasks...", { module: `cron:${this.cronName}` });
    await UserService.createAllStripeCustomers();
    logger.info("✅ Completed Stripe entity creation tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating Saltedge entity creation tasks...", { module: `cron:${this.cronName}` });
    await UserService.createAllSaltedgeLeads();
    logger.info("✅ Completed Saltedge entity creation tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating withdrawal syncing...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.syncPendingWealthkernelWithdrawals();
    logger.info("✅ Completed withdrawal syncing", { module: `cron:${this.cronName}` });

    logger.info("🎁 Initiating cashback bonus tasks...", { module: `cron:${this.cronName}` });
    // We sync bonuses first because if we create them and then immediately try to sync them, we receive 404s from WK.
    await TransactionService.syncCashbackDeposits();
    await TransactionService.createCashbackDeposits();
    logger.info("✅ Completed cashback bonus tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating GoCardless entity creation tasks...", { module: `cron:${this.cronName}` });
    await UserCronService.createAllGoCardlessCustomers();
    await BankAccountCronService.createAllGoCardlessBankAccounts();
    logger.info("✅ Completed GoCardless entity creation tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Initiating Wealthkernel withdrawal tasks...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.createMissingWealthkernelWithdrawals();
    logger.info("✅ Completed Wealthkernel withdrawal tasks", { module: `cron:${this.cronName}` });

    if (envIsProd()) {
      logger.info("🎁 Initiating reward tasks...", { module: `cron:${this.cronName}` });
      await RewardService.createAllReferralRewards();
      await RewardService.createAllEuWhitelistRewards();
      // We sync bonuses first because if we create them and then immediately try to sync them, we receive 404s from WK.
      await RewardService.syncPendingRewardDeposits();
      await RewardService.createRewardDeposits();

      await RewardService.syncPendingRewards();
      logger.info("✅ Completed reward tasks...", { module: `cron:${this.cronName}` });
    }

    logger.info("🏦 Initiating Sumsub workflow syncing tasks...", { module: `cron:${this.cronName}` });
    await KycOperationService.syncAllSumsubKycOperations();
    logger.info("✅ Completed Sumsub workflow syncing tasks", { module: `cron:${this.cronName}` });

    logger.info("🏦 Processing PendingDeposit Savings topups...", { module: `cron:${this.cronName}` });
    await TransactionCronService.processSavingsTopupsPendingDeposit();
    logger.info("✅ Completed processing PendingDeposit Savings topups", { module: `cron:${this.cronName}` });

    logger.info("💳 Processing credit ticket deposits...", { module: `cron:${this.cronName}` });
    await CreditTicketCronService.syncCreditTicketInternalTransfers();
    await CreditTicketCronService.createCreditTicketInternalTransfers();
    logger.info("✅ Completed processing credit ticket deposits", { module: `cron:${this.cronName}` });
  }
}

new OngoingCronJob().run();
