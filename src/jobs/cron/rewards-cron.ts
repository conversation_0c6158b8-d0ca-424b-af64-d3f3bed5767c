import logger from "../../external-services/loggerService";
import RewardService from "../../services/rewardService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class RewardsCronJob extends CronJob {
  cronName = CronJobNameEnum.REWARDS;

  /**
   * @description Cron running reward creation for CRM campaigns.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Initiating reward CRM creation tasks...", { module: `cron:${this.cronName}` });
    await RewardService.createAllCRMCampaignRewards();
    logger.info("✅ Completed reward CRM creation tasks", { module: `cron:${this.cronName}` });
  }
}

new RewardsCronJob().run();
