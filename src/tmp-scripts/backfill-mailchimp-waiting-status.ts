import <PERSON>riptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";

const delay = (time: number) => new Promise((resolve) => setTimeout(resolve, time));

class BackfillMailchimpWaitingStatusRunner extends ScriptRunner {
  scriptName = "backfill-mailchimp-waiting-status";

  async processFn(): Promise<void> {
    logger.info("Starting backfill for Mailchimp WAITING status...", {
      module: `script:${this.scriptName}`
    });

    let userCount = 0;
    await User.find({
      email: { $not: /^deleted_/ },
      referredByEmail: { $exists: true },
      residencyCountry: { $ne: "GB" }
    })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (user) => {
        try {
          await MailchimpService.updateMember(
            user.email,
            {
              merge_fields: { WAITING: "Given Access", REFERRED: "True", REFERREDBY: user.referredByEmail }
            },
            AudienceIdEnum.WEALTHYHOOD,
            { silent: true }
          );

          userCount++;

          if (userCount % 20 === 0) {
            logger.info(`We have updated ${userCount} users in Mailchimp!`, {
              module: `script:${this.scriptName}`
            });

            // Add delay every 20 users to avoid rate limiting
            await delay(5000);
          }
        } catch (err) {
          logger.error(`Could not update WAITING status for user ${user.email}`, {
            module: `script:${this.scriptName}`,
            data: { error: err }
          });
        }
      });

    logger.info(`✅ Finished backfill for Mailchimp WAITING status! Updated ${userCount} users.`, {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillMailchimpWaitingStatusRunner().run();
