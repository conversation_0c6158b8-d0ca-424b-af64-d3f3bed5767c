import "./dependencies";

import { Reward } from "../models/Reward";
import { ReferralCode } from "../models/ReferralCode";
import { WealthkernelService } from "../external-services/wealthkernelService";
import { DepositCashTransaction } from "../models/Transaction";
import { DepositMethodEnum } from "../types/transactions";
import Decimal from "decimal.js";

class FetchRejectedEurRewardsDepositsScriptRunner {
  // Helper method to get total deposit amount for a user (similar to _userHasTotalDepositAmount from UserService)
  private static async getUserTotalDepositAmount(userId: string): Promise<number> {
    const deposits = await DepositCashTransaction.find({
      owner: userId,
      status: { $in: ["Settled", "Pending"] }
    });

    const eligibleDeposits = deposits.filter((deposit) => {
      if (deposit.status === "Settled") {
        return true;
      }

      switch (deposit.depositMethod) {
        case DepositMethodEnum.BANK_TRANSFER: {
          const devengoStatus =
            deposit.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status;
          return devengoStatus === "confirmed";
        }
        case DepositMethodEnum.DIRECT_DEBIT:
        case DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER: {
          return deposit.isDirectDebitPaymentCollected === true;
        }
        case DepositMethodEnum.OPEN_BANKING: {
          return deposit.isPaymentAuthorised === true;
        }
        default:
          return false;
      }
    });

    const totalDepositAmount = eligibleDeposits
      .reduce((sum, deposit) => sum.plus(deposit.consideration.amount), new Decimal(0))
      .div(100);

    return totalDepositAmount.toNumber();
  }

  static async processFn(): Promise<void> {
    console.log("Starting script to fetch rejected EUR rewards bonuses...");

    // Step 1: Fetch all rewards starting from August 15, 2025 with EUR currency
    const startDate = new Date("2025-08-15");
    console.log(`Fetching rewards from ${startDate.toISOString()} with EUR consideration currency...`);

    const rewards = await Reward.find({
      createdAt: { $gte: startDate },
      "consideration.currency": "EUR",
      "deposit.providers.wealthkernel.id": { $exists: true } // Only rewards with WealthKernel bonus IDs
    })
      .populate("targetUser", "email referredByEmail joinedWithCode")
      .populate("referrer", "email")
      .populate("referral", "email");

    console.log(`Found ${rewards.length} EUR rewards with WealthKernel bonus IDs from ${startDate.toISOString()}`);

    if (rewards.length === 0) {
      console.log("No rewards found matching criteria. Exiting script.");
      return;
    }

    let rejectedBonusesCount = 0;
    const rejectedRewardsData: Array<{
      rewardId: string;
      createdAt: Date;
      rewardAmount: number;
      targetUserEmail: string;
      referrerEmail: string | null;
      referralEmail: string | null;
      referralDepositAmount: number | null;
      joinedWithCode: string | null;
      bonusId: string;
      bonusStatus: string;
    }> = [];

    // Step 2: Check each reward's bonus status
    for (const reward of rewards) {
      const bonusId = reward.deposit?.providers?.wealthkernel?.id;

      if (!bonusId) {
        console.log(`Reward ${reward.id} has no WealthKernel bonus ID, skipping...`);
        continue;
      }

      try {
        // Step 3: Fetch bonus status from EU WealthKernel
        console.log(`Fetching bonus status for reward ${reward.id}, bonus ID: ${bonusId}`);
        const bonusData = await WealthkernelService.EUInstance.retrieveBonus(bonusId);

        console.log(`Bonus ${bonusId} status: ${bonusData.status}`);

        // Step 4: Check if bonus is rejected
        if (bonusData.status === "Rejected") {
          rejectedBonusesCount++;

          // Step 5: Get user information
          const targetUser = reward.targetUser as any;
          const referrer = reward.referrer as any;
          const referral = reward.referral as any;
          let joinedWithCodeValue: string | null = null;
          let referralDepositAmount: number | null = null;

          // Check if target user is the referral and get the code they joined with
          const isTargetUserTheReferral = targetUser.id === referral?.id;
          if (isTargetUserTheReferral && targetUser.joinedWithCode) {
            try {
              const referralCode = await ReferralCode.findById(targetUser.joinedWithCode);
              joinedWithCodeValue = referralCode?.code || null;
            } catch (error) {
              console.log(`Error fetching referral code for user ${targetUser.email}: ${error.message}`);
              joinedWithCodeValue = null;
            }
          }

          // Get total deposit amount for the referral user (as checked in isRewardEligible)
          if (referral?.id) {
            try {
              referralDepositAmount = await FetchRejectedEurRewardsDepositsScriptRunner.getUserTotalDepositAmount(
                referral.id
              );
              console.log(`Referral ${referral.email} total deposit amount: €${referralDepositAmount.toFixed(2)}`);
            } catch (error) {
              console.log(`Error fetching deposit amount for referral ${referral.email}: ${error.message}`);
              referralDepositAmount = null;
            }
          }

          const rejectedRewardData = {
            rewardId: reward.id,
            createdAt: reward.createdAt,
            rewardAmount: reward.consideration.amount, // Amount in cents
            targetUserEmail: targetUser.email,
            referrerEmail: referrer?.email || null,
            referralEmail: referral?.email || null,
            referralDepositAmount: referralDepositAmount,
            joinedWithCode: joinedWithCodeValue,
            bonusId: bonusId,
            bonusStatus: bonusData.status
          };

          rejectedRewardsData.push(rejectedRewardData);

          // Step 6: Display the information
          console.log("\n" + "=".repeat(80));
          console.log("REJECTED REWARD FOUND:");
          console.log("=".repeat(80));
          console.log(`Reward ID: ${rejectedRewardData.rewardId}`);
          console.log(
            `Created At: ${rejectedRewardData.createdAt.toLocaleDateString("en-GB", {
              day: "2-digit",
              month: "2-digit",
              year: "numeric",
              hour: "2-digit",
              minute: "2-digit"
            })}`
          );
          console.log(`Reward Amount: €${(rejectedRewardData.rewardAmount / 100).toFixed(2)}`);
          console.log(`Target User Email: ${rejectedRewardData.targetUserEmail}`);
          console.log(`Referrer Email: ${rejectedRewardData.referrerEmail || "N/A"}`);
          console.log(`Referral Email: ${rejectedRewardData.referralEmail || "N/A"}`);
          console.log(
            `Referral Deposit Amount: €${rejectedRewardData.referralDepositAmount?.toFixed(2) || "N/A"}`
          );
          console.log(`Joined With Code: ${rejectedRewardData.joinedWithCode || "N/A"}`);
          console.log(`Bonus ID: ${rejectedRewardData.bonusId}`);
          console.log(`Bonus Status: ${rejectedRewardData.bonusStatus}`);
          console.log("=".repeat(80) + "\n");
        }
      } catch (error) {
        console.log(`Error fetching bonus status for reward ${reward.id}, bonus ID ${bonusId}: ${error.message}`);
        console.error(`Error fetching bonus status for reward ${reward.id}`, error);
      }
    }

    // Summary
    console.log("\n" + "=".repeat(80));
    console.log("SCRIPT SUMMARY:");
    console.log("=".repeat(80));
    console.log(`Total EUR rewards checked: ${rewards.length}`);
    console.log(`Total rejected bonuses found: ${rejectedBonusesCount}`);
    console.log("=".repeat(80));

    if (rejectedRewardsData.length > 0) {
      console.log("\nAll rejected rewards summary (grouped by referrer):");

      // Group by referrerEmail
      const groupedByReferrer = new Map<string, typeof rejectedRewardsData>();

      rejectedRewardsData.forEach((reward) => {
        const referrerKey = reward.referrerEmail || "N/A";
        if (!groupedByReferrer.has(referrerKey)) {
          groupedByReferrer.set(referrerKey, []);
        }
        groupedByReferrer.get(referrerKey)!.push(reward);
      });

      // Sort groups by count (descending) to show problematic referrers first
      const sortedGroups = Array.from(groupedByReferrer.entries()).sort((a, b) => b[1].length - a[1].length);

      // Define column widths for alignment
      const rewardIdWidth = 26;
      const dateTimeWidth = 16;
      const amountWidth = 8;
      const targetUserWidth = 32;
      const referrerWidth = 32;
      const referralWidth = 32;
      const depositAmountWidth = 12;
      const codeWidth = 12;
      const statusWidth = 12;

      // Print single header for the entire table
      console.log(
        `${"Reward ID".padEnd(rewardIdWidth)} | ${"Date & Time".padEnd(dateTimeWidth)} | ${"Amount".padEnd(amountWidth)} | ${"Target User".padEnd(targetUserWidth)} | ${"Referrer".padEnd(referrerWidth)} | ${"Referral".padEnd(referralWidth)} | ${"Deposits".padEnd(depositAmountWidth)} | ${"Code".padEnd(codeWidth)} | ${"Status".padEnd(statusWidth)}`
      );
      console.log(
        "-".repeat(
          rewardIdWidth +
            dateTimeWidth +
            amountWidth +
            targetUserWidth +
            referrerWidth +
            referralWidth +
            depositAmountWidth +
            codeWidth +
            statusWidth +
            24
        )
      ); // 24 for separators and spaces

      // Display all rows grouped by referrer
      sortedGroups.forEach(([, rewards]) => {
        rewards.forEach((reward) => {
          const rewardId = reward.rewardId.padEnd(rewardIdWidth);
          const dateTime = reward.createdAt
            .toLocaleString("en-GB", {
              day: "2-digit",
              month: "2-digit",
              year: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              hour12: false
            })
            .padEnd(dateTimeWidth);
          const amount = `€${(reward.rewardAmount / 100).toFixed(2)}`.padEnd(amountWidth);
          const targetUser = reward.targetUserEmail.padEnd(targetUserWidth);
          const referrer = (reward.referrerEmail || "N/A").padEnd(referrerWidth);
          const referral = (reward.referralEmail || "N/A").padEnd(referralWidth);
          const deposits = (
            reward.referralDepositAmount ? `€${reward.referralDepositAmount.toFixed(2)}` : "N/A"
          ).padEnd(depositAmountWidth);
          const code = (reward.joinedWithCode || "N/A").padEnd(codeWidth);
          const status = reward.bonusStatus.padEnd(statusWidth);

          console.log(
            `${rewardId} | ${dateTime} | ${amount} | ${targetUser} | ${referrer} | ${referral} | ${deposits} | ${code} | ${status}`
          );
        });
      });

      // Summary by referrer
      console.log("\n" + "=".repeat(80));
      console.log("SUMMARY BY REFERRER:");
      console.log("=".repeat(80));
      sortedGroups.forEach(([referrerEmail, rewards]) => {
        const totalAmount = rewards.reduce((sum, reward) => sum + reward.rewardAmount, 0);
        console.log(
          `${referrerEmail}: ${rewards.length} rejected rewards, Total: €${(totalAmount / 100).toFixed(2)}`
        );
      });
    }

    console.log("\nScript completed successfully!");
  }
}

(async () => {
  try {
    await FetchRejectedEurRewardsDepositsScriptRunner.processFn();
    console.log("Script completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("Script failed", error);
    process.exit(1);
  }
})();
