import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { GiftDTOInterface } from "../models/Gift";
import Decimal from "decimal.js";
import GiftService from "../services/giftService";
import { UserRepository } from "../repositories/userRepository";
const GIFT_AMOUNT = 20;

class CreateGiftForSpecificUserScriptRunner extends ScriptRunner {
  scriptName = "create-gift-for-specific-user";

  async processFn(): Promise<void> {
    logger.info("Creating gift for specific user...", {
      module: `script:${this.scriptName}`
    });
    const gifterEmail = "<EMAIL>";
    const targetUserEmail = "<EMAIL>";
    const message = "Hi there! Welcome to Wealthyhood! Here’s a £20 gift to kickstart your investing journey! 🤑";

    try {
      const gifterDocument = await UserRepository.getUserByEmail(gifterEmail);

      const giftData: GiftDTOInterface = {
        gifter: gifterDocument._id,
        targetUserEmail: targetUserEmail,
        consideration: {
          currency: "GBP",
          amount: Decimal.mul(GIFT_AMOUNT, 100).toNumber()
        },
        message: message.trim()
      };

      const gift = await GiftService.createGift(giftData);

      logger.info("✅ Gift created!", {
        module: `script:${this.scriptName}`,
        data: {
          gift
        }
      });
    } catch (error) {
      logger.error(`Failed to create gift for user ${targetUserEmail}`, {
        module: `script:${this.scriptName}`,
        data: {
          message: error.message,
          error
        }
      });
    }
  }
}

new CreateGiftForSpecificUserScriptRunner().run();
