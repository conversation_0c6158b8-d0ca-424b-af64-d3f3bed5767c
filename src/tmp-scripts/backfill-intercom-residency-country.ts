import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import analytics from "../external-services/segmentAnalyticsService";
import { User, UserDocument } from "../models/User";

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

class BackfillIntercomResidencyCountryScriptRunner extends ScriptRunner {
  scriptName = "backfill-intercom-residency-country";

  async processFn(): Promise<void> {
    logger.info("Backfilling Intercom residencyCountry for users with residencyCountry set...", {
      module: `script:${this.scriptName}`
    });

    let processed = 0;
    let success = 0;
    let failed = 0;

    await User.find({
      residencyCountry: { $exists: true },
      email: { $not: /^deleted_/ }
    })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (user: UserDocument) => {
        try {
          analytics.identify(user, { residencyCountry: user.residencyCountry }, { All: false, Intercom: true });

          success++;
        } catch (err) {
          failed++;
          logger.error("Failed to backfill Intercom residencyCountry for user", {
            module: `script:${this.scriptName}`,
            data: { userId: user._id, email: user.email, error: err }
          });
        } finally {
          processed++;
          if (processed % 20 === 0) {
            logger.info(`Processed ${processed} users so far...`, {
              module: `script:${this.scriptName}`
            });
            // Small pause to avoid hitting rate limits
            await delay(5000);
          }
        }
      });

    logger.info("Backfill complete", {
      module: `script:${this.scriptName}`,
      data: { processed, success, failed }
    });
  }
}

new BackfillIntercomResidencyCountryScriptRunner().run();
