import logger from "../external-services/loggerService";
import ScriptRunner from "../jobs/services/scriptRunner";

import { User, UserDocument, UserTypeEnum } from "../models/User";
import MailchimpService, {
  AudienceIdEnum,
  MailchimpUserMergeFieldType
} from "../external-services/mailchimpService";

const delay = (time: number) => new Promise((resolve) => setTimeout(resolve, time));

class UpdateMailchimpStatusOfResetUsers extends ScriptRunner {
  scriptName = "update-mailchimp-status-of-reset-users";

  async processFn(): Promise<void> {
    logger.info("Update mailchimp status & referredBy of reset users...", {
      module: `script:${this.scriptName}`
    });

    await User.find({
      kycStatus: "pending",
      residencyCountry: { $exists: false },
      updatedAt: {
        $gte: new Date("2025-07-24T00:00:00.000Z"),
        $lt: new Date("2025-07-25T00:00:00.000Z")
      },
      email: { $not: /^deleted_/ },
      lastLogin: { $lt: new Date("2025-07-18T00:00:00.000Z") },
      referredByEmail: { $exists: false }
    })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users, index) => {
          const updateTasks = users
            .filter(
              (user: UserDocument) => !user.role.includes(UserTypeEnum.ADMIN) && !user.hasDisassociationRequest
            )
            .map((user) => this._updateMailchimpStatus(user));

          await Promise.allSettled(updateTasks);

          // Add delay every 10 users to avoid rate limiting
          await delay(5000);
        },
        { batchSize: 10 }
      );

    logger.info("Finished updating mailchimp status & referredBy of reset users!", {
      module: `script:${this.scriptName}`
    });
  }

  private async _updateMailchimpStatus(user: UserDocument): Promise<void> {
    try {
      const fieldsToValidate: MailchimpUserMergeFieldType[] = ["REFERRED", "REFERREDBY", "STATUS"];

      const response = await MailchimpService.getMergeFieldsAndStatusForUserEmail(user.email, fieldsToValidate);

      const fields = response.mergeFields;
      const status = response.status;
      if (status !== "subscribed") {
        return;
      }

      const mailchimpIsReferred = fields.REFERRED && fields.REFERRED !== "False";
      const mailchimpStatusNotSignedUp = fields.STATUS !== "Signed Up";

      if (mailchimpIsReferred || mailchimpStatusNotSignedUp) {
        await MailchimpService.updateMember(
          user.email,
          {
            merge_fields: { STATUS: "Signed Up", REFERRED: "", REFERREDBY: "" }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      }
    } catch (error) {
      logger.error(`Failed to update status for user ${user.email}`, {
        module: `script:${this.scriptName}`,
        data: {
          email: user.email,
          error: error.message
        }
      });
    }
  }
}

new UpdateMailchimpStatusOfResetUsers().run();
