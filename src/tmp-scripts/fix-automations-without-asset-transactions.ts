import ScriptRunner from "../jobs/services/scriptRunner";
import { DepositCashTransaction } from "../models/Transaction";
import PortfolioService, { PortfolioAllocationMethodEnum } from "../services/portfolioService";
import { TopUpAutomation } from "../models/Automation";
import Decimal from "decimal.js/decimal";
import logger from "../external-services/loggerService";

class FixAutomationsWithoutAssetTransactions extends ScriptRunner {
  scriptName = "fix-automations-without-asset-transactions";

  depositsWithoutAssetTransaction = [
    "685d526d0d9c4abdc69bef85",
    "685d571a24dc6529608fb6aa",
    "685d59719ce94caf2dca69f2",
    "685d54bfbd7c1afae4ab0d3e"
  ];

  async processFn(): Promise<void> {
    logger.info("Fix automations without asset transactions", {
      module: `script:${this.scriptName}`
    });

    for (const pendingDepositId of this.depositsWithoutAssetTransaction) {
      try {
        const pendingDeposit = await DepositCashTransaction.findById(pendingDepositId);
        const automation = await TopUpAutomation.findById(pendingDeposit.linkedAutomation);
        const populatedPortfolio = await PortfolioService.getPortfolio(pendingDeposit.portfolio.toString(), true);

        logger.info(`Fixing automation ${automation.id} for portfolio ${populatedPortfolio.id}`, {
          module: `script:${this.scriptName}`
        });

        // Choose which allocation method to use
        let allocationMethodToUse;
        const onlyHasRewardedHoldings = await PortfolioService.onlyHasRewardedHoldings(populatedPortfolio);
        const hasOnlyRewardedHoldingsOrNoHoldings = !populatedPortfolio.holdings.length || onlyHasRewardedHoldings;
        if (!populatedPortfolio.isTargetAllocationSetup) {
          allocationMethodToUse = PortfolioAllocationMethodEnum.HOLDINGS;
        } else if (hasOnlyRewardedHoldingsOrNoHoldings) {
          allocationMethodToUse = PortfolioAllocationMethodEnum.TARGET_ALLOCATION;
        } else {
          allocationMethodToUse = automation.allocationMethod ?? PortfolioAllocationMethodEnum.HOLDINGS;
        }

        // We create a portfolio buy transaction pending that deposit.
        await PortfolioService.buyAssetsForPortfolio(
          populatedPortfolio,
          Decimal.div(automation.consideration.amount, 100).toNumber(),
          {
            allocationMethod: allocationMethodToUse,
            pendingDeposit,
            linkedAutomation: automation,
            executeEtfOrdersInRealtime: false
          }
        );

        logger.info(`Fixed automation ${automation.id} for portfolio ${populatedPortfolio.id}`, {
          module: `script:${this.scriptName}`
        });
      } catch (err) {
        logger.error("Failed to fix automation", {
          module: `script:${this.scriptName}`,
          data: { error: err }
        });
      }
    }

    logger.info("Finished fixing automations without asset transactions!", {
      module: `script:${this.scriptName}`
    });
  }
}

new FixAutomationsWithoutAssetTransactions().run();
