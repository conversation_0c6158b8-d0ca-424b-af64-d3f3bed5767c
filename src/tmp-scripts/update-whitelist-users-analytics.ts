import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import analytics, { UserTraitType } from "../external-services/segmentAnalyticsService";
import { whitelistConfig, countriesConfig } from "@wealthyhood/shared-configs";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

/**
 * Script to update all whitelist users on Mixpanel and MailChimp
 *
 * This script:
 * 1. Gets all whitelist emails from shared config (whitelistConfig.WHITELISTED_EU_EMAILS)
 * 2. Finds users with those emails in the database
 * 3. Updates them on Mixpanel using analytics.identify with joinedWithCode: "whitelisted"
 * 4. Updates them on MailChimp using analytics.identify with waiting: "Given Access"
 *
 */
class UpdateWhitelistUsersAnalytics extends ScriptRunner {
  scriptName = "update-whitelist-users-analytics";

  async processFn(): Promise<void> {
    console.log("🚀 Script processFn started");
    logger.info("Starting update of whitelist users on Mixpanel/MailChimp...", {
      module: `script:${this.scriptName}`
    });

    // Get all whitelist emails from shared config
    const whitelistEmails = whitelistConfig.WHITELISTED_EU_EMAILS;

    logger.info(`Found ${whitelistEmails.length} whitelist emails to process`, {
      module: `script:${this.scriptName}`,
      data: {
        emailCount: whitelistEmails.length
      }
    });

    // Find all users who have a whitelist email and have submitted residency country
    const whitelistUsers = await User.find({
      email: { $in: whitelistEmails }
    });

    logger.info(`Found ${whitelistUsers.length} whitelist users in database`, {
      module: `script:${this.scriptName}`,
      data: {
        userCount: whitelistUsers.length
      }
    });

    if (whitelistUsers.length === 0) {
      logger.info("No whitelist users found in database", {
        module: `script:${this.scriptName}`
      });
      return;
    }

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // Process each user
    for (const user of whitelistUsers) {
      try {
        const mailchimpTraits: UserTraitType = {
          waiting: "Given Access"
        };
        const mixpanelTraits: UserTraitType = {
          joinedWithCode: "whitelisted"
        };

        analytics.identify(user, mailchimpTraits, { All: false, MailChimp: true });
        analytics.identify(user, mixpanelTraits, { All: false, Mixpanel: true });

        successCount++;
        processedCount++;

        logger.info(`Updated user ${user.email} (${user._id})`, {
          module: `script:${this.scriptName}`,
          data: {
            userId: user._id,
            email: user.email
          }
        });
      } catch (err) {
        errorCount++;
        processedCount++;

        logger.error(`Failed to update user ${user.email} (${user._id})`, {
          module: `script:${this.scriptName}`,
          data: {
            userId: user._id,
            email: user.email,
            error: err
          }
        });
      }
    }

    logger.info("✅ Finished updating whitelist users on Mixpanel/MailChimp!", {
      module: `script:${this.scriptName}`,
      data: {
        totalUsers: whitelistUsers.length,
        processed: processedCount,
        success: successCount,
        errors: errorCount
      }
    });

    // Add delay so that the events get sent before they method exits
    await delay(10000);
  }
}

new UpdateWhitelistUsersAnalytics().run();
