import ScriptRunner from "../jobs/services/scriptRunner";
import { BankAccount } from "../models/BankAccount";
import logger from "../external-services/loggerService";

class AddCurrencyInBankAccountsRunner extends ScriptRunner {
  scriptName = "add-currency-in-bank-accounts";

  async processFn(): Promise<void> {
    logger.info("Adding currency in bank accounts...", {
      module: `script:${this.scriptName}`
    });

    await BankAccount.updateMany(
      {
        activeProviders: "wealthkernel"
      },
      {
        currency: "GBP"
      }
    );

    await BankAccount.updateMany(
      {
        activeProviders: { $ne: "wealthkernel" }
      },
      {
        currency: "EUR"
      }
    );

    logger.info("Added currency in bank accounts!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddCurrencyInBankAccountsRunner().run();
