import <PERSON>riptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ReferralCode, LifetimeEnum } from "../models/ReferralCode";
import { whitelistConfig } from "@wealthyhood/shared-configs";

/**
 * <PERSON><PERSON><PERSON> to create non-expiring referral codes for all whitelist codes
 * from the shared configuration.
 *
 * This script:
 * 1. Gets all whitelist promo codes from shared config
 * 2. Checks if each code already exists as a referral code
 * 3. Creates non-expiring referral codes for any missing codes
 * 4. Uses a specific owner ID for all whitelist codes
 */
class CreateWhitelistReferralCodes extends ScriptRunner {
  scriptName = "create-whitelist-referral-codes";

  private readonly WHITELIST_OWNER_ID = "608696322b21eb003e96b238"; // <EMAIL>

  async processFn(): Promise<void> {
    console.log("🚀 Script processFn started");
    logger.info("Starting creation of whitelist referral codes...", {
      module: `script:${this.scriptName}`
    });

    // Get all whitelist codes from shared config
    const whitelistCodes = whitelistConfig.WHITELIST_PROMO_CODES;

    if (!whitelistCodes || whitelistCodes.length === 0) {
      logger.warn("No whitelist promo codes found in shared config", {
        module: `script:${this.scriptName}`
      });
      return;
    }

    logger.info(`Found ${whitelistCodes.length} whitelist codes to process`, {
      module: `script:${this.scriptName}`,
      data: {
        codes: whitelistCodes
      }
    });

    let createdCount = 0;
    let existingCount = 0;
    let errorCount = 0;

    for (const code of whitelistCodes) {
      try {
        // Check if referral code already exists
        const existingCode = await ReferralCode.findOne({
          code: code.toLowerCase().trim()
        });

        if (existingCode) {
          existingCount++;
          logger.info(`Referral code already exists: ${code}`, {
            module: `script:${this.scriptName}`,
            data: {
              code,
              existingCodeId: existingCode._id,
              owner: existingCode.owner,
              active: existingCode.active,
              lifetime: existingCode.lifetime
            }
          });
          continue;
        }

        // Create new referral code
        const newReferralCode = await new ReferralCode({
          code: code.toLowerCase().trim(),
          active: true,
          lifetime: LifetimeEnum.NON_EXPIRING,
          owner: this.WHITELIST_OWNER_ID
        }).save();

        createdCount++;
        logger.info(`Created referral code for whitelist code: ${code}`, {
          module: `script:${this.scriptName}`,
          data: {
            code,
            referralCodeId: newReferralCode._id,
            owner: this.WHITELIST_OWNER_ID,
            lifetime: LifetimeEnum.NON_EXPIRING
          }
        });
      } catch (error) {
        errorCount++;
        logger.error(`Failed to process whitelist code: ${code}`, {
          module: `script:${this.scriptName}`,
          data: {
            code,
            error: error.message,
            stack: error.stack
          }
        });
      }
    }

    // Summary
    logger.info("Whitelist referral codes creation completed", {
      module: `script:${this.scriptName}`,
      data: {
        totalCodes: whitelistCodes.length,
        created: createdCount,
        existing: existingCount,
        errors: errorCount
      }
    });

    if (errorCount > 0) {
      logger.warn(`${errorCount} errors occurred during processing`, {
        module: `script:${this.scriptName}`
      });
    }

    if (createdCount === 0 && existingCount === whitelistCodes.length) {
      logger.info("All whitelist codes already have corresponding referral codes", {
        module: `script:${this.scriptName}`
      });
    }
  }
}

new CreateWhitelistReferralCodes().run();
