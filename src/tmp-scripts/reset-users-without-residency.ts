import { Account } from "../models/Account";
import { BankAccount } from "../models/BankAccount";
import { Gift } from "../models/Gift";
import { PaymentMethod } from "../models/PaymentMethod";
import { Portfolio } from "../models/Portfolio";
import { Reward } from "../models/Reward";
import { RewardInvitation } from "../models/RewardInvitation";
import { Address } from "../models/Address";
import { KycOperation } from "../models/KycOperation";
import { User, UserDocument } from "../models/User";
import ScriptRunner from "../jobs/services/scriptRunner";

class ResetUsersWithoutResidency extends ScriptRunner {
  scriptName = "reset-users-without-residency";

  async processFn(): Promise<void> {
    console.log("Resetting users who haven't selected residency country but viewed early screens...");

    await User.find({
      residencyCountry: { $exists: false },
      viewedReferralCodeScreen: true,
      email: { $not: /^deleted_/ }
    })
      .populate("portfolios accounts")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users, index) => {
          console.log(`Resetting batch with index ${index}...`);

          const dbOperationPromises = users.map((owner: UserDocument) => {
            const userUpdate = User.findByIdAndUpdate(owner.id, {
              $unset: {
                companyEntity: 1,
                firstName: 1,
                lastName: 1,
                dateOfBirth: 1,
                submittedRequiredInfoAt: 1,
                activeProviders: 1,
                nationalities: 1,
                isPassportVerified: 1,
                isPotentiallyDuplicateAccount1: 1,
                providers: 1,
                currency: 1,
                employmentInfo: 1,
                taxResidency: 1,
                isUKTaxResident: 1,
                hasAcceptedTerms: 1,
                hasSeenBilling: 1,
                viewedWelcomePage: 1,
                viewedKYCSuccessPage: 1,
                viewedReferralCodeScreen: 1,
                viewedWealthybitesScreen: 1,
                joinedWaitingListAt: 1,
                kycFailedAt: 1,
                referredByEmail: 1
              },
              $set: {
                kycStatus: "pending",
                w8BenForm: {
                  activeProviders: []
                }
              }
            });

            const operations: Promise<any>[] = [userUpdate];

            // Only update portfolio if it exists
            if (owner.portfolios && owner.portfolios.length > 0) {
              const portfolioUpdate = Portfolio.findByIdAndUpdate(owner.portfolios[0].id, {
                $unset: {
                  providers: 1,
                  allocationCreationFlow: 1
                },
                $set: {
                  initialHoldingsAllocation: []
                }
              });
              operations.push(portfolioUpdate);
            }

            // Only update account if it exists
            if (owner.accounts && owner.accounts.length > 0) {
              const accountUpdate = Account.findByIdAndUpdate(owner.accounts[0].id, {
                $unset: {
                  providers: 1
                }
              });
              operations.push(accountUpdate);
            }

            const addressDelete = Address.findOneAndDelete({ owner: owner.id });
            const kycOperationDelete = KycOperation.deleteMany({ owner: owner.id });
            const bankAccountDelete = BankAccount.deleteMany({ owner: owner.id });
            const giftDelete = Gift.deleteMany({ targetUserEmail: owner.email });
            const paymentMethodDelete = PaymentMethod.deleteMany({ owner: owner.id });
            const rewardInvitationDelete = RewardInvitation.deleteMany({ targetUserEmail: owner.email });
            const rewardDelete = Reward.deleteMany({ referral: owner.id });

            operations.push(
              addressDelete,
              kycOperationDelete,
              bankAccountDelete,
              giftDelete,
              paymentMethodDelete,
              rewardInvitationDelete,
              rewardDelete
            );

            return operations;
          });

          await Promise.all(dbOperationPromises.flat());
        },
        { batchSize: 10 }
      );

    console.log("Reset users who hadn't selected residency country but viewed early screens!");
  }
}

new ResetUsersWithoutResidency().run();
