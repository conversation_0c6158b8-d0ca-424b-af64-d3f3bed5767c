import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DailyPortfolioSavingsTicker } from "../models/DailyTicker";

class FixDailyPortfolioSavingsTickerCurrencyRunner extends ScriptRunner {
  scriptName = "fix-daily-portfolio-savings-currency";

  EURO_SAVINGS_PRODUCT_ID = "674d8aee8f1cf26793b4b925";

  async processFn(): Promise<void> {
    logger.info("Fixing daily portfolio savings tickers currency...", {
      module: `script:${this.scriptName}`
    });

    await DailyPortfolioSavingsTicker.updateMany(
      {
        savingsProduct: this.EURO_SAVINGS_PRODUCT_ID
      },
      {
        currency: "EUR"
      }
    );

    logger.info("✅ Finished fixing daily portfolio savings tickers currency!", {
      module: `script:${this.scriptName}`
    });
  }
}

new FixDailyPortfolioSavingsTickerCurrencyRunner().run();
