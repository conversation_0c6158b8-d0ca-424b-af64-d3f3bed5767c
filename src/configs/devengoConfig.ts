import { EnvironmentType } from "../utils/environmentUtil";
import { DepositMethodEnum } from "../types/transactions";
import type { PartialRecord } from "types/utils";

type DevengoAccountConfigType = {
  devengoId: string;
  wealthkernelBankAccountId: string;
};

/**
 * This configuration specifies Devengo <> WK mappings for bank accounts that are used
 * for the Devengo flows. For development and staging, we provide placeholder values.
 */
export const DevengoAccountConfig: Record<
  EnvironmentType,
  PartialRecord<DepositMethodEnum, DevengoAccountConfigType>
> = {
  development: {
    [DepositMethodEnum.BANK_TRANSFER]: {
      devengoId: "acc_bank_transfer",
      wealthkernelBankAccountId: "wk_bank_transfer"
    },
    [DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER]: {
      devengoId: "acc_direct_debit_and_bank_transfer",
      wealthkernelBankAccountId: "wk_direct_debit_and_bank_transfer"
    }
  },
  staging: {
    [DepositMethodEnum.BANK_TRANSFER]: {
      devengoId: "acc_bank_transfer",
      wealthkernelBankAccountId: "wk_bank_transfer"
    },
    [DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER]: {
      devengoId: "acc_direct_debit_and_bank_transfer",
      wealthkernelBankAccountId: "wk_direct_debit_and_bank_transfer"
    }
  },
  production: {
    [DepositMethodEnum.BANK_TRANSFER]: {
      devengoId: "acc_6p6HpxqoUuhdEhBpCm5AvE",
      wealthkernelBankAccountId: "bac-37rps5abz243ym"
    },
    [DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER]: {
      devengoId: "acc_63lE8bFMy7GeNI73M5BWBa",
      wealthkernelBankAccountId: "bac-37st7fefu243yi"
    }
  }
};
