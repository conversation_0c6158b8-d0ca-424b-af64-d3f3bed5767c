/**
 * Backfill script for withdrawal ledger entries
 * This script reuses methods from AccountingService to ensure consistency
 * with the main accounting logic for withdrawal entry generation.
 */
import { WithdrawalCashTransaction, TransactionPopulationFieldsEnum } from "../../../models/Transaction";
import { AccountingRecordIndex } from "../../../models/AccountingRecordIndex";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../../external-services/accountingLedgerStorageService";
import {
  LedgerAccounts,
  AccountingClientSegment,
  AccountingEventType,
  AccountingEntry
} from "../../../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../../../configs/accountingConfig";
import { UserDocument } from "../../../models/User";
import { AccountingService } from "../../../services/accountingService";
import Decimal from "decimal.js";

interface MissingEntryInfo {
  stage: string;
  entries: AccountingEntry[];
  description: string;
  articleDate: string;
}

interface WithdrawalAnalysis {
  withdrawalId: string;
  amount: number;
  userId: string;
  clientSegment: AccountingClientSegment;
  clientLedgerAccount: LedgerAccounts;
  currentStages: {
    stage1Completed: boolean;
    stage2Completed: boolean;
  };
  existingLedgerEntries: any[];
  missingEntries: MissingEntryInfo[];
}

const fixWithdrawalLedgerEntries = async (withdrawalId: string, dryRun: boolean = true) => {
  try {
    console.info(`Starting ledger entry fix for withdrawal: ${withdrawalId} (dry-run: ${dryRun})`);

    // 1. Fetch the withdrawal and populate necessary fields
    const withdrawal = await WithdrawalCashTransaction.findById(withdrawalId).populate([
      TransactionPopulationFieldsEnum.OWNER
    ]);

    if (!withdrawal) {
      console.error(`Withdrawal with ID ${withdrawalId} not found`);
      return;
    }

    if (withdrawal.consideration.currency !== "EUR") {
      console.error(`Withdrawal ${withdrawalId} is not in EUR currency. Only EUR withdrawals are supported.`);
      return;
    }

    const user = withdrawal.owner as UserDocument;
    if (!user.isEuropeanEntity) {
      console.error(
        `Withdrawal ${withdrawalId} belongs to non-European entity. Only European entities are supported.`
      );
      return;
    }

    console.info(`Analyzing withdrawal: ${withdrawalId}`);
    console.info(`- Amount: €${(withdrawal.consideration.amount / 100).toFixed(2)}`);
    console.info(`- User: ${user._id}`);
    console.info(`- Client Segment: ${user.accountingClientSegment}`);

    // 2. Analyze the withdrawal to determine what entries should exist
    const analysis = await analyzeWithdrawal(withdrawal, user);

    // 3. Check what ledger entries already exist
    const existingEntries = await AccountingLedgerStorageService.queryLedgerEntriesByTransactionId(withdrawalId);
    analysis.existingLedgerEntries = existingEntries;

    console.info("\n=== WITHDRAWAL ANALYSIS ===");
    console.info("Current Stages:");
    console.info(`- Stage 1 (WealthKernel settlement): ${analysis.currentStages.stage1Completed ? "✅" : "❌"}`);
    console.info(`- Stage 2 (Devengo collection): ${analysis.currentStages.stage2Completed ? "✅" : "❌"}`);
    console.info(`Existing Ledger Entries: ${existingEntries.length}`);

    if (existingEntries.length > 0) {
      console.info("\n--- Existing Ledger Entries ---");
      existingEntries.forEach((entry, index) => {
        console.info(
          `${index + 1}. ${entry.account_code} ${entry.side} €${entry.amount.toFixed(2)} (AA: ${entry.aa})`
        );
      });
    }

    // 4. Determine missing entries
    const missingEntries = determineMissingEntries(analysis);

    if (missingEntries.length === 0) {
      console.info(`\n🎉 No missing ledger entries found for withdrawal ${withdrawalId}`);
      return;
    }

    console.info("\n=== MISSING ENTRIES ANALYSIS ===");
    console.info(`Found ${missingEntries.length} missing entry group(s):`);

    missingEntries.forEach((missingEntry, index) => {
      console.info(`\n--- Missing Entry Group ${index + 1}: ${missingEntry.stage} ---`);
      console.info(`Description: ${missingEntry.description}`);
      console.info(`Article Date: ${missingEntry.articleDate}`);
      console.info("Entries:");
      missingEntry.entries.forEach((entry, entryIndex) => {
        console.info(`  ${entryIndex + 1}. ${entry.account} ${entry.type} €${(entry.amount / 100).toFixed(2)}`);
      });
    });

    // 5. Create entries if not in dry-run mode
    if (!dryRun) {
      console.info("\n=== CREATING MISSING ENTRIES ===");
      await createMissingLedgerEntries(withdrawalId, missingEntries, user._id.toString());
      console.info(`✅ Successfully created ${missingEntries.length} missing entry group(s)`);
    } else {
      console.info("\n💡 This was a dry-run. To actually create the entries, run with --no-dry-run");
    }

    console.info(`\n🎯 Analysis completed for withdrawal ${withdrawalId}\n`);
  } catch (error) {
    console.error("Error fixing withdrawal ledger entries:", { data: { error: error.toString() } });
  }
};

async function analyzeWithdrawal(withdrawal: any, user: UserDocument): Promise<WithdrawalAnalysis> {
  console.log(`🔄 Analyzing withdrawal ${withdrawal.id}`);
  const amount = withdrawal.consideration.amount;
  const clientSegment = user.accountingClientSegment;
  const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

  // Determine current stages completed
  const stage1Completed = withdrawal.providers?.wealthkernel?.status === "Settled";
  const stage2Completed =
    withdrawal.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status === "confirmed";

  const missingEntries: MissingEntryInfo[] = [];

  // Generate expected entries for each completed stage
  if (stage1Completed) {
    console.log(`Using AccountingService.generateWithdrawalStage1Entries for withdrawal ${withdrawal.id}`);
    const stage1Entries = AccountingService.generateWithdrawalStage1Entries(withdrawal);
    missingEntries.push({
      stage: "Stage 1 - WealthKernel Settlement",
      entries: stage1Entries,
      description: AccountingService.getAccountingActivityDescription(
        user._id.toString(),
        withdrawal.id,
        AccountingEventType.BANK_TRANSACTION_WITHDRAWAL
      ),
      articleDate: new Date().toISOString().slice(0, 10)
    });
  }

  if (stage2Completed) {
    console.log(`Using AccountingService.generateWithdrawalStage2Entries for withdrawal ${withdrawal.id}`);
    const stage2Entries = AccountingService.generateWithdrawalStage2Entries(withdrawal, clientSegment);
    missingEntries.push({
      stage: "Stage 2 - Devengo Collection",
      entries: stage2Entries,
      description: AccountingService.getAccountingActivityDescription(
        user._id.toString(),
        withdrawal.id,
        AccountingEventType.BANK_TRANSACTION_WITHDRAWAL
      ),
      articleDate: new Date().toISOString().slice(0, 10)
    });
  }

  return {
    withdrawalId: withdrawal.id,
    amount,
    userId: user._id.toString(),
    clientSegment,
    clientLedgerAccount,
    currentStages: {
      stage1Completed,
      stage2Completed
    },
    existingLedgerEntries: [],
    missingEntries
  };
}

function determineMissingEntries(analysis: WithdrawalAnalysis): MissingEntryInfo[] {
  const { missingEntries, existingLedgerEntries } = analysis;
  const actuallyMissingEntries: MissingEntryInfo[] = [];

  // For each expected entry group, check if it already exists in the ledger
  for (const expectedGroup of missingEntries) {
    // Check if all entries in this group already exist
    const allEntriesExist = expectedGroup.entries.every((expectedEntry) => {
      return existingLedgerEntries.some((existingEntry) => {
        const expectedAmountEuros = Decimal.div(expectedEntry.amount, 100).toNumber();
        const amountMatches = Math.abs(existingEntry.amount - expectedAmountEuros) < 0.01;
        const accountMatches = existingEntry.account_code === expectedEntry.account;
        const sideMatches = existingEntry.side === expectedEntry.type;

        return amountMatches && accountMatches && sideMatches;
      });
    });

    if (!allEntriesExist) {
      actuallyMissingEntries.push(expectedGroup);
    }
  }

  return actuallyMissingEntries;
}

async function createMissingLedgerEntries(
  withdrawalId: string,
  missingEntries: MissingEntryInfo[],
  userId: string
): Promise<void> {
  for (const missingEntry of missingEntries) {
    console.info(`Creating entries for: ${missingEntry.stage}`);

    // Create AccountingRecordIndex for this entry group
    const recordIndex = await new AccountingRecordIndex({
      linkedDocumentId: withdrawalId,
      sourceDocumentType: "Transaction"
    }).save();

    console.info(`Created AccountingRecordIndex with AA: ${recordIndex.aaIndex}`);

    // Transform entries to ledger format
    const ledgerEntries: AccountingLedgerEntry[] = missingEntry.entries.map((entry) => ({
      aa: recordIndex.aaIndex,
      account_code: entry.account,
      side: entry.type,
      amount: Decimal.div(entry.amount, 100).toNumber(), // Convert cents to euros
      reference_number: undefined as any,
      article_date: missingEntry.articleDate,
      description: missingEntry.description,
      document_id: withdrawalId,
      owner_id: userId
    }));

    // Add to ledger
    const result = await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntries);

    if (result.success) {
      console.info(`✅ Successfully created ${ledgerEntries.length} ledger entries for ${missingEntry.stage}`);
    } else {
      console.error(`❌ Failed to create ledger entries for ${missingEntry.stage}: ${result.error}`);
    }
  }
}

// Main execution
(async () => {
  try {
    const args = process.argv.slice(2);

    if (args.length === 0) {
      console.error("Usage: node backfill-withdrawal-ledger-entries.ts <withdrawalId> [--no-dry-run]");
      console.error("Example: node backfill-withdrawal-ledger-entries.ts 507f1f77bcf86cd799439011");
      console.error("Example: node backfill-withdrawal-ledger-entries.ts 507f1f77bcf86cd799439011 --no-dry-run");
      process.exit(1);
    }

    const withdrawalId = args[0];
    const dryRun = !args.includes("--no-dry-run");

    if (!withdrawalId.match(/^[0-9a-fA-F]{24}$/)) {
      console.error("Invalid withdrawal ID format. Must be a valid MongoDB ObjectId.");
      process.exit(1);
    }

    await fixWithdrawalLedgerEntries(withdrawalId, dryRun);
    process.exit(0);
  } catch (error) {
    console.error("Script execution failed:", error);
    process.exit(1);
  }
})();
