import { Reward, RewardPopulationFieldsEnum } from "../../../models/Reward";
import { AccountingRecordIndex } from "../../../models/AccountingRecordIndex";
import { InvoiceReferenceNumber } from "../../../models/InvoiceReferenceNumber";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../../external-services/accountingLedgerStorageService";
import {
  LedgerAccounts,
  AccountingClientSegment,
  AccountingEventType,
  AccountingEntry
} from "../../../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../../../configs/accountingConfig";
import { UserDocument } from "../../../models/User";
import Decimal from "decimal.js";

interface MissingEntryInfo {
  stage: string;
  entries: AccountingEntry[];
  description: string;
  articleDate: string;
  entryType: "movements" | "revenues" | "expenses";
}

interface RewardAnalysis {
  rewardId: string;
  amount: number;
  userId: string;
  clientSegment: AccountingClientSegment;
  clientLedgerAccount: LedgerAccounts;
  currentStages: {
    depositSettled: boolean;
    orderSettled: boolean;
  };
  fees: {
    fx: number;
    commission: number;
    executionSpread: number;
    brokerFee: number;
  };
  existingLedgerEntries: any[];
  missingEntries: MissingEntryInfo[];
}

const fixRewardLedgerEntries = async (rewardId: string, dryRun: boolean = true) => {
  try {
    console.info(`Starting ledger entry fix for reward: ${rewardId} (dry-run: ${dryRun})`);

    // 1. Fetch the reward and populate necessary fields
    const reward = await Reward.findById(rewardId).populate([RewardPopulationFieldsEnum.TARGET_USER]);

    if (!reward) {
      console.error(`Reward with ID ${rewardId} not found`);
      return;
    }

    if (reward.consideration.currency !== "EUR") {
      console.error(`Reward ${rewardId} is not in EUR currency. Only EUR rewards are supported.`);
      return;
    }

    const user = reward.targetUser as UserDocument;
    if (!user.isEuropeanEntity) {
      console.error(`Reward ${rewardId} belongs to non-European entity. Only European entities are supported.`);
      return;
    }

    console.info(`Analyzing reward: ${rewardId}`);
    console.info(`- Amount: €${(reward.consideration.amount / 100).toFixed(2)}`);
    console.info(`- User: ${user._id}`);
    console.info(`- Client Segment: ${user.accountingClientSegment}`);

    // 2. Analyze the reward to determine what entries should exist
    const analysis = await analyzeReward(reward, user);

    // 3. Check what ledger entries already exist
    const existingEntries = await AccountingLedgerStorageService.queryLedgerEntriesByTransactionId(rewardId);
    analysis.existingLedgerEntries = existingEntries;

    console.info("\n=== REWARD ANALYSIS ===");
    console.info("Current Stages:");
    console.info(`- Deposit Settled: ${analysis.currentStages.depositSettled ? "✅" : "❌"}`);
    console.info(`- Order Settled: ${analysis.currentStages.orderSettled ? "✅" : "❌"}`);
    console.info(`Existing Ledger Entries: ${existingEntries.length}`);

    if (existingEntries.length > 0) {
      console.info("\n--- Existing Ledger Entries ---");
      existingEntries.forEach((entry, index) => {
        console.info(
          `${index + 1}. ${entry.account_code} ${entry.side} €${entry.amount.toFixed(2)} (AA: ${entry.aa})`
        );
      });
    }

    // 4. Determine missing entries
    const missingEntries = determineMissingEntries(analysis);

    if (missingEntries.length === 0) {
      console.info(`\n🎉 No missing ledger entries found for reward ${rewardId}`);
      return;
    }

    console.info("\n=== MISSING ENTRIES ANALYSIS ===");
    console.info(`Found ${missingEntries.length} missing entry group(s):`);

    missingEntries.forEach((missingEntry, index) => {
      console.info(`\n--- Missing Entry Group ${index + 1}: ${missingEntry.stage} ---`);
      console.info(`Type: ${missingEntry.entryType}`);
      console.info(`Description: ${missingEntry.description}`);
      console.info(`Article Date: ${missingEntry.articleDate}`);
      console.info("Entries:");
      missingEntry.entries.forEach((entry, entryIndex) => {
        console.info(`  ${entryIndex + 1}. ${entry.account} ${entry.type} €${(entry.amount / 100).toFixed(2)}`);
      });
    });

    // 5. Create entries if not in dry-run mode
    if (!dryRun) {
      console.info("\n=== CREATING MISSING ENTRIES ===");
      await createMissingLedgerEntries(rewardId, missingEntries, user._id.toString());
      console.info(`✅ Successfully created ${missingEntries.length} missing entry group(s)`);
    } else {
      console.info("\n💡 This was a dry-run. To actually create the entries, run with --no-dry-run");
    }

    console.info(`\n🎯 Analysis completed for reward ${rewardId}\n`);
  } catch (error) {
    console.error("Error fixing reward ledger entries:", { data: { error: error.toString() } });
  }
};

async function analyzeReward(reward: any, user: UserDocument): Promise<RewardAnalysis> {
  const amount = reward.consideration.amount;
  const bonusAmount = reward.consideration.bonusAmount;
  const orderAmount = reward.consideration.orderAmount;
  const clientSegment = user.accountingClientSegment;
  const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

  // Determine current stages completed
  const depositSettled = reward.depositStatus === "Settled";
  const orderSettled = reward.status === "Settled";

  // Extract fees (convert from euros to cents where needed)
  const fxFeeEuros = reward.fees?.fx?.amount || 0;
  const commissionFeeEuros = reward.fees?.commission?.amount || 0;
  const executionSpreadFeeEuros = reward.fees?.executionSpread?.amount || 0;
  const brokerFeeEuros = reward.order?.providers?.wealthkernel?.accountingBrokerFxFee || 0;

  const fees = {
    fx: Decimal.mul(fxFeeEuros, 100).toNumber(), // Convert to cents
    commission: Decimal.mul(commissionFeeEuros, 100).toNumber(), // Convert to cents
    executionSpread: Decimal.mul(executionSpreadFeeEuros, 100).toNumber(), // Convert to cents
    brokerFee: Decimal.mul(brokerFeeEuros, 100).toNumber() // Convert to cents
  };

  const missingEntries: MissingEntryInfo[] = [];

  // Stage 1: Deposit Settlement - Company expense for giving bonus
  if (depositSettled) {
    const depositEntries: AccountingEntry[] = [
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: bonusAmount, type: "debit" },
      { account: LedgerAccounts.BONUS_EXPENSE, amount: bonusAmount, type: "credit" }
    ];
    missingEntries.push({
      stage: "Deposit Settlement - Bonus Expense",
      entries: depositEntries,
      description: getAccountingActivityDescription(user._id.toString(), reward.id, AccountingEventType.BONUS),
      articleDate: new Date().toISOString().slice(0, 10),
      entryType: "movements"
    });
  }

  // Stage 2: Order Settlement - Asset purchase and fees
  if (orderSettled) {
    const netAmount = Decimal.sub(orderAmount, fees.brokerFee).toNumber();

    // Main Asset Movement (Buy order) - movements
    const orderMovements: AccountingEntry[] = [
      { account: clientLedgerAccount, amount: netAmount, type: "debit" },
      { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "credit" },
      { account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "debit" },
      { account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "credit" }
    ];

    missingEntries.push({
      stage: "Order Settlement - Asset Movement",
      entries: orderMovements,
      description: getAccountingActivityDescription(user._id.toString(), reward.id, AccountingEventType.BONUS),
      articleDate: new Date().toISOString().slice(0, 10),
      entryType: "movements"
    });

    // Wealthyhood Commission - Revenue
    // Always generate revenue entries to ensure reference number is created, even if commission is 0
    const revenueEntries: AccountingEntry[] = [
      { account: clientLedgerAccount, amount: fees.fx, type: "debit" },
      { account: LedgerAccounts.COMMISSION_FEES_WH, amount: fees.fx, type: "credit" }
    ];

    missingEntries.push({
      stage: "Order Settlement - Commission Revenue",
      entries: revenueEntries,
      description: getAccountingActivityDescription(user._id.toString(), reward.id, AccountingEventType.BONUS),
      articleDate: new Date().toISOString().slice(0, 10),
      entryType: "revenues"
    });

    // Broker Fee Expense (only if broker fee > 0)
    if (fees.brokerFee > 0) {
      const expenseEntries: AccountingEntry[] = [
        { account: LedgerAccounts.BROKER_FEE_EXPENSE, amount: fees.brokerFee, type: "debit" },
        { account: LedgerAccounts.PAYABLES_TO_BROKER, amount: fees.brokerFee, type: "credit" },
        { account: LedgerAccounts.PAYABLES_TO_BROKER, amount: fees.brokerFee, type: "debit" },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: fees.brokerFee, type: "credit" }
      ];

      missingEntries.push({
        stage: "Order Settlement - Broker Fee Expense",
        entries: expenseEntries,
        description: getAccountingActivityDescription(user._id.toString(), reward.id, AccountingEventType.BONUS),
        articleDate: new Date().toISOString().slice(0, 10),
        entryType: "expenses"
      });
    }
  }

  return {
    rewardId: reward.id,
    amount,
    userId: user._id.toString(),
    clientSegment,
    clientLedgerAccount,
    currentStages: {
      depositSettled,
      orderSettled
    },
    fees,
    existingLedgerEntries: [],
    missingEntries
  };
}

function determineMissingEntries(analysis: RewardAnalysis): MissingEntryInfo[] {
  const { missingEntries, existingLedgerEntries } = analysis;
  const actuallyMissingEntries: MissingEntryInfo[] = [];

  // For each expected entry group, check if it already exists in the ledger
  for (const expectedGroup of missingEntries) {
    // Check if all entries in this group already exist
    const allEntriesExist = expectedGroup.entries.every((expectedEntry) => {
      return existingLedgerEntries.some((existingEntry) => {
        const expectedAmountEuros = Decimal.div(expectedEntry.amount, 100).toNumber();
        const amountMatches = Math.abs(existingEntry.amount - expectedAmountEuros) < 0.01;
        const accountMatches = existingEntry.account_code === expectedEntry.account;
        const sideMatches = existingEntry.side === expectedEntry.type;

        return amountMatches && accountMatches && sideMatches;
      });
    });

    if (!allEntriesExist) {
      actuallyMissingEntries.push(expectedGroup);
    }
  }

  return actuallyMissingEntries;
}

async function createMissingLedgerEntries(
  rewardId: string,
  missingEntries: MissingEntryInfo[],
  userId: string
): Promise<void> {
  for (const missingEntry of missingEntries) {
    console.info(`Creating entries for: ${missingEntry.stage} (${missingEntry.entryType})`);

    // Create AccountingRecordIndex for this entry group
    const recordIndex = await new AccountingRecordIndex({
      linkedDocumentId: rewardId,
      sourceDocumentType: "Reward"
    }).save();

    console.info(`Created AccountingRecordIndex with AA: ${recordIndex.aaIndex}`);

    // Create InvoiceReferenceNumber for revenues (commission income)
    let invoiceReferenceNumber: string | undefined;
    if (missingEntry.entryType === "revenues") {
      const invoiceReference = await new InvoiceReferenceNumber({
        linkedDocumentId: rewardId,
        sourceDocumentType: "Reward"
      }).save();
      invoiceReferenceNumber = invoiceReference.invoiceId.toString();
      console.info(`Created InvoiceReferenceNumber with ID: ${invoiceReference.invoiceId}`);
    }

    // Transform entries to ledger format
    const ledgerEntries: AccountingLedgerEntry[] = missingEntry.entries.map((entry) => ({
      aa: recordIndex.aaIndex,
      account_code: entry.account,
      side: entry.type,
      amount: Decimal.div(entry.amount, 100).toNumber(), // Convert cents to euros
      reference_number: invoiceReferenceNumber as any,
      article_date: missingEntry.articleDate,
      description: missingEntry.description,
      document_id: rewardId,
      owner_id: userId
    }));

    // Add to ledger
    const result = await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntries);

    if (result.success) {
      console.info(`✅ Successfully created ${ledgerEntries.length} ledger entries for ${missingEntry.stage}`);
    } else {
      console.error(`❌ Failed to create ledger entries for ${missingEntry.stage}: ${result.error}`);
    }
  }
}

function getAccountingActivityDescription(
  userId: string,
  rewardId: string,
  eventType: AccountingEventType
): string {
  return `${userId}|${rewardId}|${eventType}`;
}

// Main execution
(async () => {
  try {
    const args = process.argv.slice(2);

    if (args.length === 0) {
      console.error("Usage: node fix-reward-ledger-entries.ts <rewardId> [--no-dry-run]");
      console.error("Example: node fix-reward-ledger-entries.ts 507f1f77bcf86cd799439011");
      console.error("Example: node fix-reward-ledger-entries.ts 507f1f77bcf86cd799439011 --no-dry-run");
      process.exit(1);
    }

    const rewardId = args[0];
    const dryRun = !args.includes("--no-dry-run");

    if (!rewardId.match(/^[0-9a-fA-F]{24}$/)) {
      console.error("Invalid reward ID format. Must be a valid MongoDB ObjectId.");
      process.exit(1);
    }

    await fixRewardLedgerEntries(rewardId, dryRun);
    process.exit(0);
  } catch (error) {
    console.error("Script execution failed:", error);
    process.exit(1);
  }
})();
