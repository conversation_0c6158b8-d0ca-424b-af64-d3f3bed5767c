import { AccountingValidationService } from "../../../services/accountingValidationService";
import logger from "../../../external-services/loggerService";

/**
 * Script to validate that WealthKernel cash balances reconcile with
 * the corresponding amounts in the accounting ledger for a given period
 */
async function validateWKCash(fromDate: string) {
  try {
    // Validate WK cash against ledger - service handles all detailed logging
    const result = await AccountingValidationService.validateCashLedgerReconciliation(fromDate);

    // Simple summary for script completion
    const allAccountsValid = Object.values(result.perAccount).every((delta) => delta.difference === 0);
    const aggregateValid = result.aggregate.difference === 0;

    if (allAccountsValid && aggregateValid) {
      logger.info("✅ Cash reconciliation completed successfully - all balances reconciled", {
        module: "ValidateWKCashScript",
        method: "validateWKCash",
        data: { fromDate, accountCount: Object.keys(result.perAccount).length }
      });
    } else {
      const discrepancyCount = Object.values(result.perAccount).filter((delta) => delta.difference !== 0).length;
      logger.warn("⚠️ Cash reconciliation completed with discrepancies", {
        module: "ValidateWKCashScript",
        method: "validateWKCash",
        data: {
          fromDate,
          accountDiscrepancies: discrepancyCount,
          aggregateDiscrepancy: !aggregateValid
        }
      });
    }

    return result;
  } catch (error) {
    logger.error("❌ Cash validation script failed", {
      module: "ValidateWKCashScript",
      method: "validateWKCash",
      data: { fromDate, error: error.message }
    });
    throw error;
  }
}

// Run the validation if this script is executed directly
if (require.main === module) {
  // Get fromDate from command line arguments - now required
  // Usage: npm run accounting:validate-wk --from=2024-01-01
  // or: npx ts-node src/scripts/accounting/validate-wk-cash.ts 2024-01-01
  const fromDate = process.argv[2];

  if (!fromDate) {
    console.error("Error: fromDate parameter is required");
    console.error("Usage: npm run accounting:validate-wk --from=YYYY-MM-DD");
    console.error("   or: npx ts-node src/scripts/accounting/validate-wk-cash.ts YYYY-MM-DD");
    process.exit(1);
  }

  // Validate date format (basic check)
  if (!/^\d{4}-\d{2}-\d{2}$/.test(fromDate)) {
    console.error("Error: Invalid date format. Expected YYYY-MM-DD");
    process.exit(1);
  }

  validateWKCash(fromDate)
    .then(() => {
      process.exit(0);
    })
    .catch(() => {
      process.exit(1);
    });
}
