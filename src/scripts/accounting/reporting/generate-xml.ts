/**
 * generate-xml.ts
 *
 * CLI helper that generates XML reports from the accounting ledger database
 * using the AccountingReportingService.generateXMLAccountingReportFile method.
 *
 * Checkpoint behavior:
 *   --force-full: Processes all entries for the date range, does NOT update checkpoint
 *   --from-date/--to-date: Processes entries for custom date range, does NOT update checkpoint
 *   No options: Uses incremental processing from last checkpoint, DOES update checkpoint
 *
 * Examples:
 *   npx ts-node generate-xml.ts
 *   npx ts-node generate-xml.ts --upload-to-cloud false
 *   npx ts-node generate-xml.ts --force-full
 *   npx ts-node generate-xml.ts --from-date 2025-01-01 --to-date 2025-01-31
 *
 * Notes:
 *   • The script reads from the existing accounting ledger database
 *   • Date filtering is optional; defaults to current day if not specified
 */

import { Command } from "commander";
import { AccountingReportingService } from "../../../services/accountingReportingService";

/**
 * Parse date string and validate format
 */
function parseDate(value: string): Date {
  const date = new Date(value);
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date format: ${value}. Use YYYY-MM-DD format.`);
  }
  return date;
}

/**
 * Parse boolean string and validate format
 */
function parseBoolean(value: string): boolean {
  if (value.toLowerCase() === "true") {
    return true;
  } else if (value.toLowerCase() === "false") {
    return false;
  } else {
    throw new Error(`Invalid boolean value: ${value}. Use 'true' or 'false'.`);
  }
}

/**
 * Main program setup and execution
 */
async function main(): Promise<void> {
  const program = new Command();

  program
    .name("generate-xml")
    .description("Generate XML reports from the accounting ledger database")
    .version("1.0.0")
    .option(
      "--upload-to-cloud [value]",
      "Upload to cloud storage (true/false), defaults to service method default if not specified",
      parseBoolean
    )
    .option("--force-full", "Force full regeneration for date range")
    .option("--from-date <date>", "Start date for report (YYYY-MM-DD format)", parseDate)
    .option("--to-date <date>", "End date for report (YYYY-MM-DD format)", parseDate)
    .option("--update-checkpoint", "Update the last processed checkpoint after successful generation", false)
    .action(async (options) => {
      try {
        // Generate XML using AccountingReportingService
        const generateOptions: any = {
          fromDate: options.fromDate,
          toDate: options.toDate,
          forceFullGeneration: options.forceFull,
          updateCheckpoint: options.updateCheckpoint
        };

        // Only set uploadToCloud if explicitly provided
        if (options.uploadToCloud !== undefined) {
          generateOptions.uploadToCloud = options.uploadToCloud;
        }

        const result = await AccountingReportingService.generateXMLAccountingReportFile(generateOptions);

        if (result) {
          // Check if result is a cloud URL (starts with https://) or local file path
          if (result.startsWith("https://")) {
            console.log(`✅ XML report uploaded to cloud: ${result}`);
          } else {
            console.log(`✅ XML report generated: ${result}`);
          }
        } else {
          console.log("ℹ️  No entries found to process");
        }
      } catch (error) {
        console.error("❌ Error:", error.message);
        process.exit(1);
      } finally {
        // Give some time for logs to flush
        setTimeout(() => process.exit(0), 1000);
      }
    });

  await program.parseAsync();
}

// Run the script
main().catch((error) => {
  console.error("❌ Unhandled error:", error);
  process.exit(1);
});
