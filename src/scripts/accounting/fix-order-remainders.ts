import Decimal from "decimal.js";
import mongoose from "mongoose";
import { Order } from "../../models/Order";
import { AccountingRecordIndex } from "../../models/AccountingRecordIndex";
import PortfolioService from "../../services/portfolioService";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../external-services/accountingLedgerStorageService";
import { LedgerAccounts, AccountingEventType, AccountingEntry } from "../../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../../configs/accountingConfig";
import DbUtil from "../../utils/dbUtil";

interface RemainderUpdateInfo {
  orderId: string;
  wkOrderId: string;
  userId: string;
  portfolioId: string;
  wkPortfolioId: string;
  userEmail: string;
  remainderAmount: number; // in EUR
  remainderAmountCents: number; // in cents
  filledAt: string;
  transactionCategory: string;
  orderSide: string;
  clientLedgerAccount: string;
  isin?: string;
  needsLedgerEntries?: boolean;
}

interface UserRemainderSummary {
  userEmail: string;
  wkPortfolioId: string;
  totalRemainderAmount: number;
  orderCount: number;
}

async function findOrdersWithRemainders(dryRun = true, targetEmail?: string): Promise<RemainderUpdateInfo[]> {
  const fromDateObj = new Date("2025-07-01");

  try {
    console.log("=== ORDER REMAINDER PROCESSING ===");
    console.info(`Mode: ${dryRun ? "DRY RUN" : "EXECUTION"}`);
    if (targetEmail) {
      console.info(`Target email filter: ${targetEmail}`);
    }
    console.info("Fetching EUR orders with remainders after 2025-07-01...");

    // First fetch all EUR orders (since remainder is a virtual property)
    const allEurOrders = await Order.find({
      "consideration.currency": "EUR",
      $or: [{ status: "Matched" }, { status: "Settled" }],
      filledAt: { $gte: fromDateObj }
    }).populate({
      path: "transaction",
      populate: {
        path: "owner portfolio"
      }
    });

    console.log(`\nFetched ${allEurOrders.length} EUR orders, filtering for remainders...`);

    // Filter orders with remainder > 0 (virtual property)
    const ordersWithRemainders = allEurOrders.filter((order) => order.remainder > 0);

    console.log(`Orders with remainders before transaction filtering: ${ordersWithRemainders.length}`);

    if (ordersWithRemainders.length === 0) {
      console.info("No EUR orders with remainders found after 2025-07-01.");
      return [];
    }

    console.log(`Found ${ordersWithRemainders.length} orders with remainders\n`);

    const remainderUpdates: RemainderUpdateInfo[] = [];

    // Process each order and collect information
    let skippedMissingData = 0;
    let skippedEmailFilter = 0;

    for (const order of ordersWithRemainders) {
      const transaction = order.transaction as any;

      if (!transaction || !transaction.owner || !transaction.portfolio) {
        console.warn(`Skipping order ${order.id} - missing transaction, owner, or portfolio`);
        skippedMissingData++;
        continue;
      }

      const user = transaction.owner;
      const portfolio = transaction.portfolio;

      // Filter by email if specified
      if (targetEmail && user.email !== targetEmail) {
        skippedEmailFilter++;
        continue;
      }

      const remainderAmountCents = order.remainder;
      const remainderAmountEur = new Decimal(remainderAmountCents).div(100).toNumber();

      // Get client ledger account for remainder entries
      const clientLedgerAccount =
        clientSegmentToLedgerAccountMapping[
          user.accountingClientSegment as keyof typeof clientSegmentToLedgerAccountMapping
        ];

      remainderUpdates.push({
        orderId: order.id,
        wkOrderId: order.providers?.wealthkernel?.id || "N/A",
        userId: user.id,
        portfolioId: portfolio.id,
        wkPortfolioId: portfolio.providers?.wealthkernel?.id || "N/A",
        userEmail: user.email,
        remainderAmount: remainderAmountEur,
        remainderAmountCents: remainderAmountCents,
        filledAt: order.filledAt ? new Date(order.filledAt).toISOString().split("T")[0] : "N/A",
        transactionCategory: transaction.category,
        orderSide: order.side,
        clientLedgerAccount: clientLedgerAccount,
        isin: order.isin,
        needsLedgerEntries: undefined // Will be determined later
      });
    }

    console.log("\nProcessing summary:");
    console.log(`- Orders with remainders found: ${ordersWithRemainders.length}`);
    console.log(`- Skipped due to missing data: ${skippedMissingData}`);
    if (targetEmail) {
      console.log(`- Skipped due to email filter (${targetEmail}): ${skippedEmailFilter}`);
    }
    console.log(`- Orders to process: ${remainderUpdates.length}\n`);

    if (remainderUpdates.length === 0) {
      console.info("No orders to process after filtering.");
      return [];
    }

    // Check which orders need ledger entries
    console.log("Checking existing ledger entries for remainder orders...");
    await checkExistingLedgerEntries(remainderUpdates);

    return remainderUpdates;
  } catch (error) {
    console.error("Error finding orders with remainders:", { data: { error: error.toString() } });
    throw error;
  }
}

async function displayRemainderDetails(remainderUpdates: RemainderUpdateInfo[]): Promise<void> {
  if (remainderUpdates.length === 0) {
    console.info("No remainder updates to display.");
    return;
  }

  // Sort by filled date and then by remainder amount
  remainderUpdates.sort((a, b) => {
    const dateA = new Date(a.filledAt).getTime();
    const dateB = new Date(b.filledAt).getTime();
    if (dateA !== dateB) return dateA - dateB;
    return b.remainderAmount - a.remainderAmount;
  });

  console.log(
    "Order ID".padEnd(26) +
      "WK Order ID".padEnd(35) +
      "Filled At".padEnd(12) +
      "User Email".padEnd(45) +
      "Remainder Amount".padEnd(17) +
      "Portfolio ID"
  );
  console.log("-".repeat(154));

  let totalRemainderAmount = new Decimal(0);
  const userPortfolioMap = new Map<string, Set<string>>();

  for (const update of remainderUpdates) {
    // Track unique user-portfolio combinations
    if (!userPortfolioMap.has(update.userId)) {
      userPortfolioMap.set(update.userId, new Set());
    }
    userPortfolioMap.get(update.userId)!.add(update.portfolioId);

    console.log(
      update.orderId.padEnd(26) +
        update.wkOrderId.padEnd(35) +
        update.filledAt.padEnd(12) +
        update.userEmail.padEnd(45) +
        `€${update.remainderAmount.toFixed(2)}`.padEnd(17) +
        update.portfolioId
    );

    totalRemainderAmount = totalRemainderAmount.plus(update.remainderAmount);
  }

  // Display totals and summary
  console.log("-".repeat(154));
  console.log("TOTALS".padEnd(98) + `€${totalRemainderAmount.toFixed(2)}`.padEnd(17));

  console.log("\n=== REMAINDER SUMMARY ===");
  console.log(`Total Orders with Remainders: ${remainderUpdates.length}`);
  console.log(`Total Remainder Amount: €${totalRemainderAmount.toFixed(2)}`);
  console.log(`Unique Users Affected: ${userPortfolioMap.size}`);

  let totalPortfolios = 0;
  userPortfolioMap.forEach((portfolios) => {
    totalPortfolios += portfolios.size;
  });
  console.log(`Unique Portfolios Affected: ${totalPortfolios}`);

  // Show distribution by transaction category
  const categoryMap = new Map<string, { count: number; amount: Decimal }>();
  remainderUpdates.forEach((update) => {
    if (!categoryMap.has(update.transactionCategory)) {
      categoryMap.set(update.transactionCategory, { count: 0, amount: new Decimal(0) });
    }
    const category = categoryMap.get(update.transactionCategory)!;
    category.count++;
    category.amount = category.amount.plus(update.remainderAmount);
  });

  console.log("\n=== BY TRANSACTION CATEGORY ===");
  categoryMap.forEach((data, category) => {
    console.log(`${category}: ${data.count} orders, €${data.amount.toFixed(2)}`);
  });

  // Create aggregated user summary
  const userSummaryMap = new Map<string, UserRemainderSummary>();

  remainderUpdates.forEach((update) => {
    const key = `${update.userEmail}-${update.wkPortfolioId}`;

    if (!userSummaryMap.has(key)) {
      userSummaryMap.set(key, {
        userEmail: update.userEmail,
        wkPortfolioId: update.wkPortfolioId,
        totalRemainderAmount: 0,
        orderCount: 0
      });
    }

    const summary = userSummaryMap.get(key)!;
    summary.totalRemainderAmount += update.remainderAmount;
    summary.orderCount++;
  });

  // Convert to array and sort by total remainder amount (descending)
  const userSummaries = Array.from(userSummaryMap.values()).sort(
    (a, b) => b.totalRemainderAmount - a.totalRemainderAmount
  );

  console.log("\n=== USER REMAINDER AGGREGATION ===");
  console.log("User Email".padEnd(45) + "WK Portfolio ID".padEnd(26) + "Orders".padEnd(8) + "Total Remainder");
  console.log("-".repeat(95));

  for (const summary of userSummaries) {
    console.log(
      summary.userEmail.padEnd(45) +
        summary.wkPortfolioId.padEnd(26) +
        summary.orderCount.toString().padEnd(8) +
        `€${summary.totalRemainderAmount.toFixed(2)}`
    );
  }

  console.log("-".repeat(95));
  const totalUsers = userSummaries.length;
  // const totalOrders = userSummaries.reduce((sum, s) => sum + s.orderCount, 0);
  const totalAmount = userSummaries.reduce((sum, s) => sum + s.totalRemainderAmount, 0);

  console.log(`TOTALS (${totalUsers} users)`.padEnd(79) + `€${totalAmount.toFixed(2)}`);
}

async function executeRemainderUpdates(
  remainderUpdates: RemainderUpdateInfo[],
  dryRun: boolean = true
): Promise<void> {
  if (remainderUpdates.length === 0) {
    console.info("No remainder updates to execute.");
    return;
  }

  console.log(`\n=== ${dryRun ? "DRY RUN - " : ""}EXECUTING REMAINDER UPDATES ==="`);
  console.log(`📚 Ledger entry creation: ${dryRun ? "WOULD BE ENABLED" : "ENABLED"}`);

  if (dryRun) {
    console.log("\n🔍 DRY RUN MODE - No actual changes will be made\n");
  }

  let successCount = 0;
  let errorCount = 0;
  const errors: string[] = [];

  for (const update of remainderUpdates) {
    try {
      if (dryRun) {
        // In dry run, show detailed ledger entries that would be created
        console.log(
          `🔍 Would update portfolio ${update.portfolioId} for user ${update.userEmail}: +€${update.remainderAmount.toFixed(2)}`
        );
        console.log(
          "  💰 Cash Update: Add €" + update.remainderAmount.toFixed(2) + " as available and settled cash"
        );

        // Show detailed ledger entries
        if (update.needsLedgerEntries) {
          console.log("  📚 Would create remainder ledger entries:");
          console.log("    - AA Index: [Generated automatically]");
          console.log(
            `    - Description: ${getAccountingActivityDescription(update.userId, update.orderId, AccountingEventType.ASSET_BUY, update.isin)}`
          );
          console.log(`    - Article Date: ${update.filledAt}`);
          console.log(
            `    - Entry 1: ${LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS} DEBIT €${update.remainderAmount.toFixed(2)}`
          );
          console.log(`    - Entry 2: ${update.clientLedgerAccount} CREDIT €${update.remainderAmount.toFixed(2)}`);
        } else {
          console.log("  📚 Remainder ledger entries already exist - skipping");
        }
        console.log("");
        successCount++;
      } else {
        // Actual execution
        await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
          // Update cash availability: add remainder as available and settled cash
          await PortfolioService.updateCashAvailability(
            update.portfolioId,
            "EUR", // All orders are EUR as per filter
            update.remainderAmount,
            {
              session,
              available: true,
              settled: true
            }
          );

          // Create ledger entries only if needed
          if (update.needsLedgerEntries) {
            await createRemainderLedgerEntries(update, session);
          }
        });

        console.log(
          `✅ Updated portfolio ${update.portfolioId} for user ${update.userEmail}: +€${update.remainderAmount.toFixed(2)} ${update.needsLedgerEntries ? "(with ledger entries)" : "(ledger entries already exist)"}`
        );
        successCount++;
      }
    } catch (error) {
      const errorMsg = `❌ ${dryRun ? "Would fail to update" : "Failed to update"} portfolio ${update.portfolioId} for user ${update.userEmail}: ${error.toString()}`;
      console.error(errorMsg);
      errors.push(errorMsg);
      errorCount++;
    }
  }

  console.log(`\n=== ${dryRun ? "DRY RUN " : ""}EXECUTION SUMMARY ==="`);
  console.log(`${dryRun ? "Would be successful" : "Successful"} updates: ${successCount}`);
  console.log(`${dryRun ? "Would fail" : "Failed"} updates: ${errorCount}`);

  if (errors.length > 0) {
    console.log("\n=== ERRORS ===");
    errors.forEach((error) => console.log(error));
  }

  if (successCount > 0) {
    const totalProcessed = new Decimal(
      remainderUpdates.slice(0, successCount).reduce((sum, update) => sum + update.remainderAmount, 0)
    );
    console.log(`Total cash ${dryRun ? "that would be" : ""} added: €${totalProcessed.toFixed(2)}`);
    if (!dryRun) {
      const ledgerEntriesCreated = remainderUpdates.filter((u) => u.needsLedgerEntries).length;
      console.log(
        `Total ledger entries created: ${ledgerEntriesCreated * 2} (${ledgerEntriesCreated} remainder entries with 2 ledger lines each)`
      );
      if (ledgerEntriesCreated < successCount) {
        console.log(`Skipped ${successCount - ledgerEntriesCreated} orders that already had ledger entries`);
      }
    }
  }
}

async function createRemainderLedgerEntries(
  update: RemainderUpdateInfo,
  session: mongoose.ClientSession
): Promise<void> {
  console.log(`  📚 Creating ledger entries for remainder €${update.remainderAmount.toFixed(2)}...`);

  // Create AccountingRecordIndex for this remainder entry
  const recordIndex = await new AccountingRecordIndex({
    linkedDocumentId: update.orderId,
    sourceDocumentType: "Order"
  }).save({ session });

  console.log(`  📚 Created AccountingRecordIndex with AA: ${recordIndex.aaIndex}`);

  // Create remainder entries: debit omnibus, credit client account
  const remainderEntries: AccountingEntry[] = [
    { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: update.remainderAmountCents, type: "debit" },
    { account: update.clientLedgerAccount as LedgerAccounts, amount: update.remainderAmountCents, type: "credit" }
  ];

  // Generate description following the same pattern as backfill script
  const description = getAccountingActivityDescription(
    update.userId,
    update.orderId,
    AccountingEventType.ASSET_BUY,
    update.isin
  );

  // Transform entries to ledger format
  const ledgerEntries: AccountingLedgerEntry[] = remainderEntries.map((entry) => ({
    aa: recordIndex.aaIndex,
    account_code: entry.account,
    side: entry.type,
    amount: new Decimal(entry.amount).div(100).toNumber(), // Convert cents to euros
    reference_number: undefined as any, // Remainder entries don't need invoice reference
    article_date: new Date(Date.now()).toISOString().slice(0, 10),
    description: description,
    document_id: update.orderId,
    owner_id: update.userId
  }));

  // Add to ledger
  const result = await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntries);

  if (result.success) {
    console.log(`  📚 ✅ Successfully created ${ledgerEntries.length} remainder ledger entries`);
  } else {
    throw new Error(`Failed to create remainder ledger entries: ${result.error}`);
  }
}

async function checkExistingLedgerEntries(remainderUpdates: RemainderUpdateInfo[]): Promise<void> {
  let entriesChecked = 0;
  let entriesNeedCreation = 0;
  let entriesAlreadyExist = 0;

  for (const update of remainderUpdates) {
    try {
      // Query existing ledger entries for this order
      const existingEntries = await AccountingLedgerStorageService.queryLedgerEntriesByTransactionId(
        update.orderId
      );

      // Check if remainder entries already exist
      const hasRemainderEntries = existingEntries.some((entry) => {
        const amountMatches = Math.abs(entry.amount - update.remainderAmount) < 0.01;
        const isOmnibusDebit =
          entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit";
        const isClientCredit = entry.account_code === update.clientLedgerAccount && entry.side === "credit";
        return amountMatches && (isOmnibusDebit || isClientCredit);
      });

      update.needsLedgerEntries = !hasRemainderEntries;
      entriesChecked++;

      if (hasRemainderEntries) {
        entriesAlreadyExist++;
      } else {
        entriesNeedCreation++;
      }
    } catch (error) {
      console.warn(`Failed to check ledger entries for order ${update.orderId}: ${error.toString()}`);
      // Default to creating entries if we can't check
      update.needsLedgerEntries = true;
      entriesNeedCreation++;
    }
  }

  console.log(`Checked ${entriesChecked} orders:`);
  console.log(`- ${entriesNeedCreation} orders need new ledger entries`);
  console.log(`- ${entriesAlreadyExist} orders already have ledger entries\n`);
}

function getAccountingActivityDescription(
  userId: string,
  orderId: string,
  eventType: AccountingEventType,
  isin?: string
): string {
  return `${userId}|${orderId}${isin ? `|${isin}` : ""}|${eventType}`;
}

async function main(): Promise<void> {
  // Check command line arguments for mode
  const args = process.argv.slice(2);
  const isDryRun = !args.includes("--execute");

  // Parse email filter
  const emailArgIndex = args.findIndex((arg) => arg === "--email");
  const targetEmail =
    emailArgIndex !== -1 && emailArgIndex + 1 < args.length ? args[emailArgIndex + 1] : undefined;

  if (isDryRun) {
    console.log("Running in DRY RUN mode. Use --execute flag to perform actual updates.");
    console.log("📚 Will show what cash and ledger changes would be made.");
  } else {
    console.log("🚨 EXECUTION MODE: Will perform actual cash updates and create ledger entries!");
  }

  if (targetEmail) {
    console.log(`🎯 Email filter: Processing only orders for ${targetEmail}`);
  }

  try {
    // Find all orders with remainders
    const remainderUpdates = await findOrdersWithRemainders(isDryRun, targetEmail);

    // Display the details
    await displayRemainderDetails(remainderUpdates);

    if (remainderUpdates.length > 0) {
      if (isDryRun) {
        console.log("\n🔍 DRY RUN: The above shows what would be processed.");
        console.log("📚 Cash updates and ledger entries would be created for remainder amounts.");
      } else {
        // Confirm before execution
        console.log("\n⚠️  About to update cash availability for the above portfolios.");
        console.log("This will add remainder amounts as available and settled cash.");
        console.log("📚 Will also create accounting ledger entries for remainder amounts.");
      }

      // Always run the execution function, but pass isDryRun to control behavior
      await executeRemainderUpdates(remainderUpdates, isDryRun);
    }

    console.log(`\n🎯 Remainder processing completed ${isDryRun ? "(DRY RUN)" : "(EXECUTED)"}!`);
    process.exit(0);
  } catch (error) {
    console.error("Script execution failed:", error);
    process.exit(1);
  }
}

// Show usage if help is requested
const args = process.argv.slice(2);
if (args.includes("--help") || args.includes("-h")) {
  console.log("\n=== REMAINDER PROCESSING SCRIPT ===");
  console.log("\nUsage:");
  console.log("  node fix-order-remainders.ts [options]");
  console.log("\nOptions:");
  console.log("  --execute              Execute actual updates with ledger entries (default: dry-run)");
  console.log("  --email <email>        Process only orders for specific email");
  console.log("  --help, -h             Show this help message");
  console.log("\nExamples:");
  console.log("  node fix-order-remainders.ts");
  console.log("  node fix-order-remainders.ts --email <EMAIL>");
  console.log("  node fix-order-remainders.ts --execute");
  console.log("  node fix-order-remainders.ts --execute --email <EMAIL>");
  process.exit(0);
}

// Execute the script
main();
