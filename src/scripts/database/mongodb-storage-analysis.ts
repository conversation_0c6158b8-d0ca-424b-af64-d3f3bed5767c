/**
 * MongoDB Storage Analysis Script
 *
 * Analyzes MongoDB storage usage by collection, showing document counts, sizes, and index usage.
 * Provides recommendations for storage optimization.
 *
 * Usage Examples:
 *
 * # Basic usage with environment variables
 * export MONGODB_URI="mongodb://localhost:27017"
 * export DB_NAME="wealthyhood-db-staging"
 * npx ts-node src/scripts/database/mongodb-storage-analysis.ts
 *
 * # With command line options
 * npx ts-node src/scripts/database/mongodb-storage-analysis.ts \
 *   --uri "mongodb+srv://user:<EMAIL>/dbname" \
 *   --db-name "wealthyhood-db-staging"
 *
 * # Save results to JSON file
 * npx ts-node src/scripts/database/mongodb-storage-analysis.ts --save
 *
 * # Custom output filename
 * npx ts-node src/scripts/database/mongodb-storage-analysis.ts \
 *   --save --output "storage-report-$(date +%Y%m%d).json"
 *
 * # Show help
 * npx ts-node src/scripts/database/mongodb-storage-analysis.ts --help
 *
 * Environment Variables:
 * - MONGODB_URI: MongoDB connection string
 * - DB_NAME: Database name to analyze
 */

import { MongoClient } from "mongodb";
import * as fs from "fs";
import { Command } from "commander";

interface CollectionStats {
  name: string;
  documentCount: number;
  documentSize: number;
  indexSize: number;
  totalSize: number;
  avgDocumentSize: number;
}

interface StorageAnalysis {
  totalDbSize: number;
  collections: CollectionStats[];
  largestCollectionsByDocuments: CollectionStats[];
  largestCollectionsByIndexes: CollectionStats[];
  largestCollectionsByTotal: CollectionStats[];
}

async function analyzeMongoDBStorage(options: { uri?: string; dbName?: string }): Promise<StorageAnalysis> {
  const connectionString = options.uri || process.env.MONGODB_URI || "mongodb://localhost:27017";
  const dbName = options.dbName || process.env.DB_NAME || "wealthyhood";

  const client = new MongoClient(connectionString);

  try {
    await client.connect();
    console.log("Connected to MongoDB");

    const db = client.db(dbName);
    db.admin();

    // Get database stats
    const dbStats = await db.stats();
    const totalDbSize = dbStats.dataSize + dbStats.indexSize;

    console.log(`\nDatabase: ${dbName}`);
    console.log(`Total Database Size: ${formatBytes(totalDbSize)}`);
    console.log(`Data Size: ${formatBytes(dbStats.dataSize)}`);
    console.log(`Index Size: ${formatBytes(dbStats.indexSize)}`);
    console.log(`Collections: ${dbStats.collections}`);

    // Get list of collections
    const collections = await db.listCollections().toArray();
    const collectionStats: CollectionStats[] = [];

    console.log("\nAnalyzing collections...");

    for (const collection of collections) {
      const collName = collection.name;

      try {
        const stats = await db.command({ collStats: collName });

        const collectionStat: CollectionStats = {
          name: collName,
          documentCount: stats.count || 0,
          documentSize: stats.size || 0,
          indexSize: stats.totalIndexSize || 0,
          totalSize: (stats.size || 0) + (stats.totalIndexSize || 0),
          avgDocumentSize: stats.count > 0 ? (stats.size || 0) / stats.count : 0
        };

        collectionStats.push(collectionStat);

        console.log(`✓ ${collName}: ${stats.count || 0} docs, ${formatBytes(collectionStat.totalSize)}`);
      } catch (error) {
        console.log(`✗ Failed to get stats for ${collName}: ${error}`);
      }
    }

    // Sort collections by different criteria
    const largestByDocuments = [...collectionStats].sort((a, b) => b.documentSize - a.documentSize).slice(0, 10);

    const largestByIndexes = [...collectionStats].sort((a, b) => b.indexSize - a.indexSize).slice(0, 10);

    const largestByTotal = [...collectionStats].sort((a, b) => b.totalSize - a.totalSize).slice(0, 10);

    return {
      totalDbSize,
      collections: collectionStats,
      largestCollectionsByDocuments: largestByDocuments,
      largestCollectionsByIndexes: largestByIndexes,
      largestCollectionsByTotal: largestByTotal
    };
  } finally {
    await client.close();
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function formatPercentage(part: number, total: number): string {
  return ((part / total) * 100).toFixed(2) + "%";
}

function printAnalysis(analysis: StorageAnalysis): void {
  console.log("\n" + "=".repeat(80));
  console.log("MONGODB STORAGE ANALYSIS REPORT");
  console.log("=".repeat(80));

  console.log(`\nTotal Database Size: ${formatBytes(analysis.totalDbSize)}`);

  console.log("\n📊 TOP 10 COLLECTIONS BY DOCUMENT SIZE:");
  console.log("-".repeat(80));
  console.log("Rank | Collection Name".padEnd(35) + "| Documents | Doc Size  | % of Total");
  console.log("-".repeat(80));

  analysis.largestCollectionsByDocuments.forEach((col, index) => {
    const rank = (index + 1).toString().padStart(4);
    const name = col.name.padEnd(25);
    const docs = col.documentCount.toLocaleString().padStart(9);
    const size = formatBytes(col.documentSize).padStart(9);
    const percentage = formatPercentage(col.documentSize, analysis.totalDbSize).padStart(8);

    console.log(`${rank} | ${name} | ${docs} | ${size} | ${percentage}`);
  });

  console.log("\n📈 TOP 10 COLLECTIONS BY INDEX SIZE:");
  console.log("-".repeat(80));
  console.log("Rank | Collection Name".padEnd(35) + "| Indexes  | Index Size | % of Total");
  console.log("-".repeat(80));

  analysis.largestCollectionsByIndexes.forEach((col, index) => {
    const rank = (index + 1).toString().padStart(4);
    const name = col.name.padEnd(25);
    const indexes = "N/A".padStart(9); // MongoDB stats don't provide index count easily
    const size = formatBytes(col.indexSize).padStart(10);
    const percentage = formatPercentage(col.indexSize, analysis.totalDbSize).padStart(8);

    console.log(`${rank} | ${name} | ${indexes} | ${size} | ${percentage}`);
  });

  console.log("\n🏆 TOP 10 COLLECTIONS BY TOTAL SIZE (Documents + Indexes):");
  console.log("-".repeat(90));
  console.log("Rank | Collection Name".padEnd(35) + "| Total Size | Doc Size  | Index Size | % of Total");
  console.log("-".repeat(90));

  analysis.largestCollectionsByTotal.forEach((col, index) => {
    const rank = (index + 1).toString().padStart(4);
    const name = col.name.padEnd(25);
    const total = formatBytes(col.totalSize).padStart(10);
    const docSize = formatBytes(col.documentSize).padStart(9);
    const indexSize = formatBytes(col.indexSize).padStart(10);
    const percentage = formatPercentage(col.totalSize, analysis.totalDbSize).padStart(8);

    console.log(`${rank} | ${name} | ${total} | ${docSize} | ${indexSize} | ${percentage}`);
  });

  console.log("\n📋 DETAILED COLLECTION BREAKDOWN:");
  console.log("-".repeat(100));
  console.log("Collection Name".padEnd(30) + "| Documents | Avg Doc Size | Doc Size  | Index Size | Total Size");
  console.log("-".repeat(100));

  analysis.collections
    .sort((a, b) => b.totalSize - a.totalSize)
    .forEach((col) => {
      const name = col.name.padEnd(28);
      const docs = col.documentCount.toLocaleString().padStart(9);
      const avgSize = formatBytes(col.avgDocumentSize).padStart(12);
      const docSize = formatBytes(col.documentSize).padStart(9);
      const indexSize = formatBytes(col.indexSize).padStart(10);
      const totalSize = formatBytes(col.totalSize).padStart(10);

      console.log(`${name} | ${docs} | ${avgSize} | ${docSize} | ${indexSize} | ${totalSize}`);
    });

  console.log("\n" + "=".repeat(80));
  console.log("SUMMARY");
  console.log("=".repeat(80));

  const totalDocumentSize = analysis.collections.reduce((sum, col) => sum + col.documentSize, 0);
  const totalIndexSize = analysis.collections.reduce((sum, col) => sum + col.indexSize, 0);
  const totalDocuments = analysis.collections.reduce((sum, col) => sum + col.documentCount, 0);

  console.log(`Total Collections: ${analysis.collections.length}`);
  console.log(`Total Documents: ${totalDocuments.toLocaleString()}`);
  console.log(
    `Total Document Size: ${formatBytes(totalDocumentSize)} (${formatPercentage(totalDocumentSize, analysis.totalDbSize)})`
  );
  console.log(
    `Total Index Size: ${formatBytes(totalIndexSize)} (${formatPercentage(totalIndexSize, analysis.totalDbSize)})`
  );
  console.log(`Total Database Size: ${formatBytes(analysis.totalDbSize)}`);

  // Recommendations
  console.log("\n💡 OPTIMIZATION RECOMMENDATIONS:");
  console.log("-".repeat(50));

  const largestCollection = analysis.largestCollectionsByTotal[0];
  if (largestCollection) {
    console.log(
      `• Consider archiving old data from "${largestCollection.name}" (${formatBytes(largestCollection.totalSize)})`
    );
  }

  const indexHeavyCollections = analysis.largestCollectionsByIndexes
    .filter((col) => col.indexSize > col.documentSize)
    .slice(0, 3);

  if (indexHeavyCollections.length > 0) {
    console.log("• Review index usage for collections with high index-to-document ratios:");
    indexHeavyCollections.forEach((col) => {
      const ratio = (col.indexSize / col.documentSize).toFixed(1);
      console.log(`  - ${col.name}: ${ratio}x more index than document data`);
    });
  }

  const avgDocSizeThreshold = 1024 * 1024; // 1MB
  const largeDocCollections = analysis.collections
    .filter((col) => col.avgDocumentSize > avgDocSizeThreshold)
    .slice(0, 3);

  if (largeDocCollections.length > 0) {
    console.log("• Consider document size optimization for collections with large average document size:");
    largeDocCollections.forEach((col) => {
      console.log(`  - ${col.name}: ${formatBytes(col.avgDocumentSize)} average document size`);
    });
  }
}

async function main(): Promise<void> {
  const program = new Command();

  program
    .name("mongodb-storage-analysis")
    .description("Analyze MongoDB storage usage by collection")
    .version("1.0.0")
    .option("-u, --uri <uri>", "MongoDB connection URI", process.env.MONGODB_URI || "mongodb://localhost:27017")
    .option("-d, --db-name <name>", "Database name to analyze", process.env.DB_NAME || "wealthyhood-db-staging")
    .option("-s, --save", "Save analysis results to JSON file")
    .option("-o, --output <filename>", "Output filename for saved results")
    .parse();

  const options = program.opts();

  try {
    console.log("Starting MongoDB Storage Analysis...");
    console.log(`Database: ${options.dbName}`);
    console.log(`Connection: ${options.uri.replace(/\/\/[^@]*@/, "//***:***@")}`); // Hide credentials in output

    const analysis = await analyzeMongoDBStorage({
      uri: options.uri,
      dbName: options.dbName
    });

    printAnalysis(analysis);

    // Save to file if requested
    if (options.save) {
      const timestamp = new Date().toISOString().split("T")[0];
      const filename = options.output || `mongodb-storage-analysis-${options.dbName}-${timestamp}.json`;

      fs.writeFileSync(filename, JSON.stringify(analysis, null, 2));
      console.log(`\nAnalysis saved to: ${filename}`);
    }
    process.exit(0);
  } catch (error) {
    console.error("Error analyzing MongoDB storage:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
