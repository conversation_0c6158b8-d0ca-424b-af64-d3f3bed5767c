import { User, PlatformType, UserDocument } from "../models/User";

export class UserRepository {
  static async updateDeviceToken(userId: string, platform: PlatformType, token: string): Promise<void> {
    await User.findByIdAndUpdate(userId, {
      [`deviceTokens.${platform}`]: token
    });
  }

  static async removeDeviceToken(userId: string, platform: PlatformType): Promise<void> {
    await User.findByIdAndUpdate(userId, {
      $unset: {
        [`deviceTokens.${platform}`]: 1
      }
    });
  }

  public static async getUserByEmail(email: string): Promise<UserDocument | null> {
    return User.findOne({ email });
  }
}
