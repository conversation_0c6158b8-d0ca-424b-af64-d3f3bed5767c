import { usersConfig, whitelistConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "../models/User";

const { WHITELISTED_EMAILS } = usersConfig;

export default class UserUtil {
  public static isUserWhitelisted(user: UserDocument): boolean {
    return WHITELISTED_EMAILS.includes(user.email);
  }

  /**
   * Check if an email is in the whitelist
   * @param email The email to check
   * @returns true if the email is whitelisted, false otherwise
   */
  public static isEmailEuWhitelisted(email: string): boolean {
    if (!email) return false;
    const normalizedEmail = email.toLowerCase().trim();
    return whitelistConfig.WHITELISTED_EU_EMAILS.includes(normalizedEmail);
  }
}
