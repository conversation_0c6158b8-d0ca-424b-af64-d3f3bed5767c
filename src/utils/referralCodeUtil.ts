import { whitelistConfig } from "@wealthyhood/shared-configs";

export default class ReferralCodeUtil {
  /**
   * Check if a referral code is in the whitelist
   * @param code The referral code to check
   * @returns true if the code is whitelisted, false otherwise
   */
  public static isReferralCodeWhitelisted(code: string): boolean {
    if (!code) return false;
    const normalizedCode = code.toLowerCase().trim();
    return whitelistConfig.WHITELIST_PROMO_CODES.includes(normalizedCode);
  }
}
