import { captureException } from "@sentry/node";
import mongoose, { Document, PopulateOptions as MongoosePopulateOptions } from "mongoose";
import { PortfolioPopulationFieldsEnum } from "../models/Portfolio";
import { UserPopulationFieldsEnum } from "../models/User";
import { TransactionPopulationFieldsEnum } from "../models/Transaction";
import { OrderPopulationFieldsEnum } from "../models/Order";
import logger from "../external-services/loggerService";
import { BankAccountPopulationFieldsEnum } from "../models/BankAccount";
import { RewardPopulationFieldsEnum } from "../models/Reward";
import { AccountPopulationFieldsEnum } from "../models/Account";
import { AutomationPopulationFieldsEnum } from "../models/Automation";
import { DailySummarySnapshotPopulationFieldsEnum } from "../models/DailySummarySnapshot";
import { CreditTicketPopulationFieldsEnum } from "../models/CreditTicket";
import { StreamLockToken } from "../models/StreamLockToken";

// Read preference is used to put read load created from cron jobs to the secondary nodes
// and enable the app-api to use the primary one with reduce read load.
export enum MongoReadPreferenceEnum {
  PRIMARY = "primary",
  SECONDARY = "secondary"
}

type PopulationFieldsType =
  | PortfolioPopulationFieldsEnum
  | UserPopulationFieldsEnum
  | TransactionPopulationFieldsEnum
  | BankAccountPopulationFieldsEnum
  | AccountPopulationFieldsEnum
  | RewardPopulationFieldsEnum
  | OrderPopulationFieldsEnum
  | AutomationPopulationFieldsEnum
  | CreditTicketPopulationFieldsEnum
  | DailySummarySnapshotPopulationFieldsEnum;

export type PopulationOptions =
  | string // e.g "owner"
  | string[] // e.g. ["owner", "orders"]
  | {
      path: string;
      populate?: string | PopulationOptions | (string | PopulationOptions)[];
    } // Similar to Mongoose population options
  | {
      path: string;
      populate?: string | PopulationOptions | (string | PopulationOptions)[];
    }[]; // Similar to Mongoose population options

const DEFAULT_NUMBER_OF_RETRIES = 20;

export default class DbUtil {
  /**
   * Method that takes in a function and runs it within the context of a Mongoose session.
   *
   * To retry on transient errors, options.numberOfRetriesForTransientError can be set.
   *
   * @param processorFn
   * @param options
   */
  public static async runInSession<T>(
    processorFn: (session: mongoose.ClientSession) => Promise<T>,
    options: {
      numberOfRetriesForTransientError?: number;
    } = { numberOfRetriesForTransientError: DEFAULT_NUMBER_OF_RETRIES }
  ): Promise<T> {
    const session = await mongoose.startSession({
      defaultTransactionOptions: { readPreference: MongoReadPreferenceEnum.PRIMARY }
    });
    try {
      let retries = 0;
      while (retries < options.numberOfRetriesForTransientError) {
        try {
          session.startTransaction();
          const result: T = await processorFn(session);
          await session.commitTransaction();
          return result;
        } catch (error) {
          try {
            await session.abortTransaction();
          } catch (abortTransactionError) {
            logger.error("We got an error while aborting the transaction!", {
              module: "DbUtil",
              method: "runInSession",
              data: { error, abortTransactionError, retries }
            });
            throw abortTransactionError;
          }

          retries++;
          if (retries >= options.numberOfRetriesForTransientError) {
            throw new Error("Maximum retry limit reached, transaction failed!");
          }

          if (!error.errorLabels || error.errorLabels.indexOf("TransientTransactionError") < 0) {
            throw error;
          }
        }
      }
    } finally {
      await session.endSession();
    }
  }

  /**
   * This method listens to changes in the given model. When a change has been received, we execute the relevant function
   * given. We ONLY listen to change streams if the LISTEN_TO_CHANGE_STREAMS is enabled.
   *
   * **Note**: This method uses a unique token approach to prevent duplicate processing across multiple pods.
   * Each change stream event is processed only once using the StreamLockToken model.
   *
   * @param model
   * @param operationType - The type of operation to listen for ('insert' or 'update')
   * @param onOperationProcessorFn
   */
  public static listenToChangeStream<T>(
    model: mongoose.Model<T>,
    operationType: "insert" | "update",
    onOperationProcessorFn: (newDocument: T, oldDocument?: T) => Promise<void>
  ): void {
    if (process.env.LISTEN_TO_CHANGE_STREAMS !== "true") {
      return;
    }

    model
      .watch([], { fullDocument: "updateLookup", fullDocumentBeforeChange: "whenAvailable" })
      .on("change", async (change) => {
        if (change.operationType === operationType) {
          await DbUtil._lockStream(change, model.modelName, async () => {
            // plain mongodb document
            const rawNewDocument = change.fullDocument;
            // hydrated mongoose document
            const newDocument = new model(rawNewDocument) as T;

            let oldDocument: T | undefined = undefined;
            if (change.fullDocumentBeforeChange) {
              const rawOldDocument = change.fullDocumentBeforeChange;
              oldDocument = new model(rawOldDocument) as T;
            }

            await onOperationProcessorFn(newDocument, oldDocument);
          });
        }
      });
  }

  /**
   * This method extracts the field to be used for sorting along with the ordering type.
   * The ordering type determined from th first char of string param sort '-' for desc, '+' or nothing for asc.
   * @param sort
   */
  public static determineSorting(sort: string): { [key in string]: number } {
    return {
      [sort.replace("+", "").replace("-", "")]: sort.charAt(0) == "-" ? -1 : 1
    };
  }

  public static getMongoosePopulateOption(
    options: PopulationOptions
  ): MongoosePopulateOptions | (MongoosePopulateOptions | string)[] {
    if (typeof options === "string") {
      return { path: options };
    } else return options as MongoosePopulateOptions;
  }

  public static async populateIfNotAlreadyPopulated(
    document: Document,
    path: PopulationFieldsType,
    options?: { session: mongoose.ClientSession }
  ) {
    if (!document.populated(path)) {
      await document.populate({ path, options: { session: options?.session } });
    }
  }

  public static depopulateIfAlreadyPopulated(document: Document, path: PopulationFieldsType) {
    if (document.populated(path)) {
      document.depopulate(path);
    }
  }

  public static getPopulationString(populate: Record<string, boolean>): string {
    if (!populate) {
      return "";
    }

    return Object.entries(populate)
      .filter(([, toPopulate]) => toPopulate)
      .map(([fieldName]) => fieldName)
      .join(" ");
  }

  /**
   * Creates a lock for a change stream event to prevent duplicate processing across multiple pods.
   * Uses StreamLockToken to ensure each change stream event is processed only once.
   *
   * @param change - The change stream event document
   * @param modelName - Name of the model for logging purposes
   * @param callback - Function to execute if lock is successfully acquired
   */
  private static async _lockStream(change: any, modelName: string, callback: () => Promise<void>): Promise<void> {
    // Use the change stream event _id as a unique token to prevent duplicate processing
    const changeId = change._id._data.toString();
    const documentId = change.fullDocument?._id?.toString?.() || "unknown";

    try {
      // Attempt to create a StreamLockToken with the change ID
      // This will fail with a duplicate key error (code 11000) if already processed
      await new StreamLockToken({ changeId }).save();

      // If we reach here, this is the first time processing this event
      logger.info(`Processing change stream event for ${modelName} ${documentId}`, {
        module: "DbUtil",
        method: "lockStream",
        data: {
          changeId,
          documentId,
          modelName
        }
      });

      // Execute the callback function
      await callback();
    } catch (error: any) {
      // If error code is 11000, it means this event was already processed by another pod
      if (error.code === 11000) {
        logger.info("Change stream event already processed, skipping", {
          module: "DbUtil",
          method: "lockStream",
          data: {
            changeId,
            documentId,
            modelName
          }
        });
        return; // Skip processing
      }

      // For any other error, log it and let it bubble up
      captureException(error);
      logger.error(`Error processing change stream event for ${modelName} ${documentId}`, {
        module: "DbUtil",
        method: "lockStream",
        data: {
          changeId,
          documentId,
          modelName,
          error
        }
      });
    }
  }
}
