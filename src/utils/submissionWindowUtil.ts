import { DateTime } from "luxon";
import { MMF_SUBMISSION_HOURS_CONFIG, SUBMISSION_HOURS_CONFIG } from "../configs/submissionWindowConfig";
import DateUtil from "./dateUtil";
import { OrderDTOInterface, OrderInterface, OrderSubmissionIntentEnum } from "../models/Order";
import { currenciesConfig, investmentsConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import Decimal from "decimal.js";
import { getAssetConfigFromIsin } from "./investmentUniverseUtil";

// Temporarily increasing this to avoid EUR orders being rejected when submitted as realtime.
// This should be reverted when fixed by WK.
const PERCENTAGE_THRESHOLD_FOR_MINIMUM_REALTIME_EXECUTION_ASSET_SELL = 0.18;
const MINIMUM_ALLOWED_ETF_INVESTMENT_REALTIME_EXECUTION = 1;

const { MIN_ALLOWED_ASSET_SELL_AMOUNT_REALTIME_EXECUTION } = investmentsConfig;

/**
 * Utility class to determine the submission window for an order or a transaction.
 *
 * #### Note:
 * Submission window is meant to be used internally in contrast with ExecutionWindowUtil.
 */
export default class SubmissionWindowUtil {
  public static isCurrentTimeWithinSavingsProductSubmissionWindow(): boolean {
    const now = new Date(Date.now());

    if (!DateUtil.isUKWorkDay(now)) {
      return false;
    }

    const start = DateTime.fromJSDate(now).setZone(MMF_SUBMISSION_HOURS_CONFIG.timeZone).set({
      hour: MMF_SUBMISSION_HOURS_CONFIG.start.HOUR,
      minute: MMF_SUBMISSION_HOURS_CONFIG.start.MINUTE,
      second: 0,
      millisecond: 0
    });
    const end = DateTime.fromJSDate(now).setZone(MMF_SUBMISSION_HOURS_CONFIG.timeZone).set({
      hour: MMF_SUBMISSION_HOURS_CONFIG.end.HOUR,
      minute: MMF_SUBMISSION_HOURS_CONFIG.end.MINUTE,
      second: 0,
      millisecond: 0
    });

    const submissionWindowStart = DateUtil.convertToTimeZone(start, "GMT");
    const submissionWindowEnd = DateUtil.convertToTimeZone(end, "GMT");

    return now.getTime() >= submissionWindowStart.getTime() && now.getTime() <= submissionWindowEnd.getTime();
  }

  /**
   * @description
   * Returns true if the current time is inside the submission window +/- 1 hour.
   */
  public static isCurrentTimeWithinOneHourOfSavingsProductSubmissionWindow(): boolean {
    const now = new Date(Date.now());

    if (!DateUtil.isUKWorkDay(now)) {
      return false;
    }

    const start = DateTime.fromJSDate(now)
      .setZone(MMF_SUBMISSION_HOURS_CONFIG.timeZone)
      .set({
        hour: MMF_SUBMISSION_HOURS_CONFIG.start.HOUR - 1,
        minute: MMF_SUBMISSION_HOURS_CONFIG.start.MINUTE,
        second: 0,
        millisecond: 0
      });
    const end = DateTime.fromJSDate(now)
      .setZone(MMF_SUBMISSION_HOURS_CONFIG.timeZone)
      .set({
        hour: MMF_SUBMISSION_HOURS_CONFIG.end.HOUR + 1,
        minute: MMF_SUBMISSION_HOURS_CONFIG.end.MINUTE,
        second: 0,
        millisecond: 0
      });

    const submissionWindowStart = DateUtil.convertToTimeZone(start, "GMT");
    const submissionWindowEnd = DateUtil.convertToTimeZone(end, "GMT");

    return now.getTime() >= submissionWindowStart.getTime() && now.getTime() <= submissionWindowEnd.getTime();
  }

  public static isCurrentTimeWithinAggregateSubmissionWindow(
    assetCategory: investmentUniverseConfig.AssetCategoryType
  ): boolean {
    const now = new Date(Date.now());

    const isWorkDay = assetCategory === "etf" ? DateUtil.isUKWorkDay(now) : DateUtil.isUSWorkDay(now);
    if (!isWorkDay) {
      return false;
    }

    const start = DateTime.fromJSDate(now).setZone(SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.timeZone).set({
      hour: SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.start.HOUR,
      minute: SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.start.MINUTE,
      second: 0,
      millisecond: 0
    });
    const end = DateTime.fromJSDate(now).setZone(SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.timeZone).set({
      hour: SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.end.HOUR,
      minute: SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.end.MINUTE,
      second: 0,
      millisecond: 0
    });

    const submissionWindowStart = DateUtil.convertToTimeZone(start, "GMT");
    const submissionWindowEnd = DateUtil.convertToTimeZone(end, "GMT");

    return now.getTime() >= submissionWindowStart.getTime() && now.getTime() <= submissionWindowEnd.getTime();
  }

  public static isDateBeforeAggregateSubmissionCutoffTime(
    date: Date,
    assetCategory: investmentUniverseConfig.AssetCategoryType
  ): boolean {
    const cutoffDate = DateTime.fromJSDate(date)
      .setZone(SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.timeZone)
      .set({
        hour: SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.end.HOUR,
        minute: SUBMISSION_HOURS_CONFIG[assetCategory].aggregate.end.MINUTE,
        second: 0,
        millisecond: 0
      });

    return date.getTime() < cutoffDate.toJSDate().getTime();
  }

  /**
   * Determines the submission intent (REAL_TIME or AGGREGATE) for an order based on:
   *
   * 1. Asset configuration (some assets are configured for aggregated submission only)
   * 2. Order amount (must be above minimum threshold for realtime execution)
   * 3. Asset category specific rules:
   *    - ETFs: depends on executeEtfOrdersInRealtime flag
   *    - Stocks: always REAL_TIME if above thresholds
   *
   * Submission intent does **NOT** describe whether the stock will be submitted now or later!
   * It only described whether the intent of this order is to be submitted as a realtime or as an aggregate order!
   *
   * @param order - Order data without transaction and submissionIntent
   * @param options - Configuration options
   * @param options.executeEtfOrdersInRealtime - Flag to enable realtime ETF order execution
   * @param options.userCurrency - User's currency for calculations
   * @param options.investmentProduct - Investment product details
   * @returns The determined submission intent
   */
  public static getOrderSubmissionIntent(
    order: Omit<OrderDTOInterface, "transaction" | "submissionIntent">,
    options: {
      executeEtfOrdersInRealtime?: boolean;
      userCurrency: currenciesConfig.MainCurrencyType;
      investmentProduct: InvestmentProductDocument;
    }
  ): OrderSubmissionIntentEnum {
    const { executeEtfOrdersInRealtime = false, userCurrency, investmentProduct } = options;
    const config = getAssetConfigFromIsin(order.isin);

    // If asset is configured for aggregated submission only, always use AGGREGATE
    if (config?.aggregatedSubmission) {
      return OrderSubmissionIntentEnum.AGGREGATE;
    }

    // Check if order amount is below minimum for realtime execution
    const isOrderEstimatedAmountBelowMinimumRealtimeAmount =
      SubmissionWindowUtil.isOrderEstimatedAmountBelowMinimumRealtimeAmount(order, {
        assetCategory: config.category,
        userCurrency,
        investmentProduct
      });
    if (isOrderEstimatedAmountBelowMinimumRealtimeAmount) {
      return OrderSubmissionIntentEnum.AGGREGATE;
    }

    // For ETFs
    if (config.category === "etf") {
      return executeEtfOrdersInRealtime
        ? OrderSubmissionIntentEnum.REAL_TIME
        : OrderSubmissionIntentEnum.AGGREGATE;
    }

    // For Stocks - always REALTIME
    if (config.category === "stock") {
      return OrderSubmissionIntentEnum.REAL_TIME;
    }

    // Default fallback
    return OrderSubmissionIntentEnum.AGGREGATE;
  }

  public static isOrderEligibleForRealtimeSubmissionNow(
    order: Partial<OrderInterface>,
    options: {
      assetCategory: investmentUniverseConfig.AssetCategoryType;
      userCurrency: currenciesConfig.MainCurrencyType;
      investmentProduct: InvestmentProductDocument;
    }
  ): boolean {
    const isCurrentTimeWithinRealtimeWindow = SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow(
      options.assetCategory
    );
    const isOrderEstimatedAmountBelowMinimumRealtimeAmount =
      SubmissionWindowUtil.isOrderEstimatedAmountBelowMinimumRealtimeAmount(order, {
        assetCategory: options.assetCategory,
        userCurrency: options.userCurrency,
        investmentProduct: options.investmentProduct
      });

    return (
      isCurrentTimeWithinRealtimeWindow &&
      !isOrderEstimatedAmountBelowMinimumRealtimeAmount &&
      order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME
    );
  }

  public static isCurrentTimeWithinRealtimeSubmissionWindow(
    assetCategory: investmentUniverseConfig.AssetCategoryType
  ): boolean {
    const now = new Date(Date.now());

    const isWorkDay = assetCategory === "etf" ? DateUtil.isUKWorkDay(now) : DateUtil.isUSWorkDay(now);
    if (!isWorkDay) {
      return false;
    }

    const start = DateTime.fromJSDate(now).setZone(SUBMISSION_HOURS_CONFIG[assetCategory].realtime.timeZone).set({
      hour: SUBMISSION_HOURS_CONFIG[assetCategory].realtime.start.HOUR,
      minute: SUBMISSION_HOURS_CONFIG[assetCategory].realtime.start.MINUTE,
      second: 0,
      millisecond: 0
    });
    const end = DateTime.fromJSDate(now).setZone(SUBMISSION_HOURS_CONFIG[assetCategory].realtime.timeZone).set({
      hour: SUBMISSION_HOURS_CONFIG[assetCategory].realtime.end.HOUR,
      minute: SUBMISSION_HOURS_CONFIG[assetCategory].realtime.end.MINUTE,
      second: 0,
      millisecond: 0
    });

    const submissionWindowStart = DateUtil.convertToTimeZone(start, "GMT");
    const submissionWindowEnd = DateUtil.convertToTimeZone(end, "GMT");

    return now.getTime() >= submissionWindowStart.getTime() && now.getTime() <= submissionWindowEnd.getTime();
  }

  public static isOrderEstimatedAmountBelowMinimumRealtimeAmount(
    order: Partial<OrderInterface>,
    options: {
      assetCategory: investmentUniverseConfig.AssetCategoryType;
      userCurrency: currenciesConfig.MainCurrencyType;
      investmentProduct: InvestmentProductDocument;
    }
  ): boolean {
    const { assetCategory, userCurrency, investmentProduct } = options;

    if (assetCategory === "stock") {
      return (
        order.side === "Sell" &&
        Decimal.mul(order.quantity as number, investmentProduct.currentTicker.getPrice(userCurrency)).lessThan(
          Decimal.add(
            MIN_ALLOWED_ASSET_SELL_AMOUNT_REALTIME_EXECUTION,
            PERCENTAGE_THRESHOLD_FOR_MINIMUM_REALTIME_EXECUTION_ASSET_SELL
          )
        )
      );
    } else {
      const orderAmount =
        order.side === "Buy"
          ? Decimal.div(order.consideration.amount, 100)
          : Decimal.mul(order.quantity as number, investmentProduct.currentTicker.getPrice(userCurrency));

      return orderAmount.lessThan(MINIMUM_ALLOWED_ETF_INVESTMENT_REALTIME_EXECUTION);
    }
  }
}
