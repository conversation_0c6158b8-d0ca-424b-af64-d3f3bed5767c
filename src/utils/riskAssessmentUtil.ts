import { RiskScoreClassificationEnum } from "../models/RiskAssessment";

/**
 * Returns the risk score classification based on the provided total score.
 */
export function getRiskScoreClassification(totalScore: number): RiskScoreClassificationEnum {
  if (totalScore <= 5) {
    return RiskScoreClassificationEnum.LowRisk;
  }
  if (totalScore <= 10) {
    return RiskScoreClassificationEnum.MediumRisk;
  }
  if (totalScore < 100) {
    return RiskScoreClassificationEnum.HighRisk;
  }

  return RiskScoreClassificationEnum.Prohibited;
}
